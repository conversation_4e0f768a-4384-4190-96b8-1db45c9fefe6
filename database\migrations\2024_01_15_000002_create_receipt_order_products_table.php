<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('receipt_order_products', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('receipt_order_id');
            $table->unsignedBigInteger('product_id');
            $table->decimal('quantity', 15, 2);
            $table->decimal('unit_cost', 15, 2)->default(0);
            $table->decimal('total_cost', 15, 2)->default(0);
            $table->date('expiry_date')->nullable();
            $table->boolean('is_return')->default(false);
            $table->text('notes')->nullable();
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('receipt_order_id')->references('id')->on('receipt_orders')->onDelete('cascade');
            $table->foreign('product_id')->references('id')->on('product_services')->onDelete('cascade');

            // Indexes for better performance
            $table->index(['receipt_order_id', 'product_id']);
            $table->index(['product_id', 'is_return']);
            $table->index(['expiry_date']);
            $table->index(['quantity']);
            $table->index(['total_cost']);
            $table->index('created_at');

            // Unique constraint to prevent duplicate products in same order
            $table->unique(['receipt_order_id', 'product_id', 'expiry_date'], 'unique_receipt_product_expiry');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('receipt_order_products');
    }
};
