using Microsoft.EntityFrameworkCore;
using POS_Desktop_App.Models.LocalCache;
using System;
using System.IO;

namespace POS_Desktop_App.Services.CacheService
{
    /// <summary>
    /// سياق قاعدة البيانات المحلية - SQLite للتخزين المؤقت
    /// </summary>
    public class LocalCacheDbContext : DbContext
    {
        // DbSets for local cache entities
        public DbSet<LocalPosTransaction> LocalPosTransactions { get; set; }
        public DbSet<LocalProduct> LocalProducts { get; set; }
        public DbSet<LocalCustomer> LocalCustomers { get; set; }
        public DbSet<LocalWarehouse> LocalWarehouses { get; set; }
        public DbSet<LocalWarehouseProduct> LocalWarehouseProducts { get; set; }
        public DbSet<SyncQueue> SyncQueue { get; set; }
        public DbSet<AppSettings> AppSettings { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                // Create local database directory if it doesn't exist
                var localDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "POS_Desktop_App");
                if (!Directory.Exists(localDataPath))
                {
                    Directory.CreateDirectory(localDataPath);
                }

                var dbPath = Path.Combine(localDataPath, "pos_local_cache.db");
                optionsBuilder.UseSqlite($"Data Source={dbPath}");

                #if DEBUG
                optionsBuilder.EnableSensitiveDataLogging();
                optionsBuilder.LogTo(Console.WriteLine);
                #endif
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure LocalPosTransaction
            modelBuilder.Entity<LocalPosTransaction>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(e => e.TransactionData).IsRequired();
                entity.Property(e => e.SyncStatus).IsRequired();
                entity.Property(e => e.CreatedAt).IsRequired();

                entity.HasIndex(e => e.SyncStatus);
                entity.HasIndex(e => e.CreatedAt);
                entity.HasIndex(e => e.ServerPosId);
            });

            // Configure LocalProduct
            modelBuilder.Entity<LocalProduct>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(e => e.ServerProductId).IsRequired();
                entity.Property(e => e.ProductData).IsRequired();
                entity.Property(e => e.LastUpdated).IsRequired();

                entity.HasIndex(e => e.ServerProductId).IsUnique();
                entity.HasIndex(e => e.LastUpdated);
                entity.HasIndex(e => e.Sku);
                entity.HasIndex(e => e.Name);
            });

            // Configure LocalCustomer
            modelBuilder.Entity<LocalCustomer>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(e => e.ServerCustomerId).IsRequired();
                entity.Property(e => e.CustomerData).IsRequired();
                entity.Property(e => e.LastUpdated).IsRequired();

                entity.HasIndex(e => e.ServerCustomerId).IsUnique();
                entity.HasIndex(e => e.LastUpdated);
                entity.HasIndex(e => e.Name);
                entity.HasIndex(e => e.Contact);
            });

            // Configure LocalWarehouse
            modelBuilder.Entity<LocalWarehouse>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(e => e.ServerWarehouseId).IsRequired();
                entity.Property(e => e.WarehouseData).IsRequired();
                entity.Property(e => e.LastUpdated).IsRequired();

                entity.HasIndex(e => e.ServerWarehouseId).IsUnique();
                entity.HasIndex(e => e.LastUpdated);
                entity.HasIndex(e => e.Name);
            });

            // Configure LocalWarehouseProduct
            modelBuilder.Entity<LocalWarehouseProduct>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(e => e.WarehouseId).IsRequired();
                entity.Property(e => e.ProductId).IsRequired();
                entity.Property(e => e.Quantity).IsRequired();
                entity.Property(e => e.LastUpdated).IsRequired();

                entity.HasIndex(e => new { e.WarehouseId, e.ProductId }).IsUnique();
                entity.HasIndex(e => e.LastUpdated);
            });

            // Configure SyncQueue
            modelBuilder.Entity<SyncQueue>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(e => e.EntityType).IsRequired();
                entity.Property(e => e.Operation).IsRequired();
                entity.Property(e => e.EntityData).IsRequired();
                entity.Property(e => e.Priority).IsRequired();
                entity.Property(e => e.CreatedAt).IsRequired();

                entity.HasIndex(e => e.Priority);
                entity.HasIndex(e => e.CreatedAt);
                entity.HasIndex(e => e.EntityType);
                entity.HasIndex(e => e.Operation);
                entity.HasIndex(e => e.IsProcessed);
            });

            // Configure AppSettings
            modelBuilder.Entity<AppSettings>(entity =>
            {
                entity.HasKey(e => e.Key);
                entity.Property(e => e.Key).IsRequired();
                entity.Property(e => e.Value).IsRequired();
                entity.Property(e => e.LastUpdated).IsRequired();

                entity.HasIndex(e => e.LastUpdated);
            });
        }

        // Initialize database with default data
        public async Task InitializeDatabaseAsync()
        {
            try
            {
                await Database.EnsureCreatedAsync();
                await SeedDefaultDataAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تهيئة قاعدة البيانات المحلية: {ex.Message}", ex);
            }
        }

        private async Task SeedDefaultDataAsync()
        {
            // Add default app settings if they don't exist
            var defaultSettings = new[]
            {
                new AppSettings { Key = "LastSyncTime", Value = DateTime.MinValue.ToString(), LastUpdated = DateTime.Now },
                new AppSettings { Key = "ServerUrl", Value = "http://localhost", LastUpdated = DateTime.Now },
                new AppSettings { Key = "SyncInterval", Value = "30", LastUpdated = DateTime.Now },
                new AppSettings { Key = "AutoSync", Value = "true", LastUpdated = DateTime.Now },
                new AppSettings { Key = "CurrentUserId", Value = "0", LastUpdated = DateTime.Now },
                new AppSettings { Key = "CurrentWarehouseId", Value = "0", LastUpdated = DateTime.Now },
                new AppSettings { Key = "CurrentShiftId", Value = "0", LastUpdated = DateTime.Now },
                new AppSettings { Key = "PrinterName", Value = "", LastUpdated = DateTime.Now },
                new AppSettings { Key = "ThermalPrintEnabled", Value = "false", LastUpdated = DateTime.Now }
            };

            foreach (var setting in defaultSettings)
            {
                var existingSetting = await AppSettings.FindAsync(setting.Key);
                if (existingSetting == null)
                {
                    AppSettings.Add(setting);
                }
            }

            await SaveChangesAsync();
        }

        // Clean old cache data
        public async Task CleanOldCacheAsync(int daysToKeep = 30)
        {
            var cutoffDate = DateTime.Now.AddDays(-daysToKeep);

            // Clean old synced transactions
            var oldTransactions = LocalPosTransactions
                .Where(t => t.SyncStatus == SyncStatus.Synced && t.CreatedAt < cutoffDate);
            LocalPosTransactions.RemoveRange(oldTransactions);

            // Clean old sync queue items
            var oldSyncItems = SyncQueue
                .Where(s => s.IsProcessed && s.CreatedAt < cutoffDate);
            SyncQueue.RemoveRange(oldSyncItems);

            await SaveChangesAsync();
        }

        // Get database size
        public long GetDatabaseSize()
        {
            var dbPath = Database.GetConnectionString().Replace("Data Source=", "");
            if (File.Exists(dbPath))
            {
                return new FileInfo(dbPath).Length;
            }
            return 0;
        }

        // Backup database
        public async Task<bool> BackupDatabaseAsync(string backupPath)
        {
            try
            {
                var dbPath = Database.GetConnectionString().Replace("Data Source=", "");
                if (File.Exists(dbPath))
                {
                    await Task.Run(() => File.Copy(dbPath, backupPath, true));
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        // Restore database
        public async Task<bool> RestoreDatabaseAsync(string backupPath)
        {
            try
            {
                if (!File.Exists(backupPath))
                    return false;

                var dbPath = Database.GetConnectionString().Replace("Data Source=", "");
                
                // Close current connection
                await Database.CloseConnectionAsync();
                
                // Copy backup file
                await Task.Run(() => File.Copy(backupPath, dbPath, true));
                
                // Reopen connection
                await Database.OpenConnectionAsync();
                
                return true;
            }
            catch
            {
                return false;
            }
        }

        // Vacuum database (SQLite optimization)
        public async Task VacuumDatabaseAsync()
        {
            try
            {
                await Database.ExecuteSqlRawAsync("VACUUM");
            }
            catch
            {
                // Ignore vacuum errors
            }
        }

        // Get cache statistics
        public async Task<CacheStatistics> GetCacheStatisticsAsync()
        {
            return new CacheStatistics
            {
                TotalTransactions = await LocalPosTransactions.CountAsync(),
                PendingSyncTransactions = await LocalPosTransactions.CountAsync(t => t.SyncStatus == SyncStatus.Pending),
                TotalProducts = await LocalProducts.CountAsync(),
                TotalCustomers = await LocalCustomers.CountAsync(),
                TotalWarehouses = await LocalWarehouses.CountAsync(),
                PendingSyncItems = await SyncQueue.CountAsync(s => !s.IsProcessed),
                DatabaseSize = GetDatabaseSize(),
                LastSyncTime = await GetLastSyncTimeAsync()
            };
        }

        private async Task<DateTime?> GetLastSyncTimeAsync()
        {
            var setting = await AppSettings.FindAsync("LastSyncTime");
            if (setting != null && DateTime.TryParse(setting.Value, out DateTime lastSync))
            {
                return lastSync;
            }
            return null;
        }
    }

    // Cache statistics model
    public class CacheStatistics
    {
        public int TotalTransactions { get; set; }
        public int PendingSyncTransactions { get; set; }
        public int TotalProducts { get; set; }
        public int TotalCustomers { get; set; }
        public int TotalWarehouses { get; set; }
        public int PendingSyncItems { get; set; }
        public long DatabaseSize { get; set; }
        public DateTime? LastSyncTime { get; set; }
    }
}
