<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التعديل المباشر - معالجة فواتير المستودع</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">🔧 اختبار التعديل المباشر</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>🎯 الروابط السريعة</h5>
                    </div>
                    <div class="card-body">
                        <a href="/warehouse-purchase-processing" class="btn btn-primary btn-lg d-block mb-3" target="_blank">
                            📊 صفحة معالجة فواتير المستودع
                        </a>
                        <a href="/test_ajax.php" class="btn btn-info btn-lg d-block mb-3" target="_blank">
                            🔗 اختبار AJAX
                        </a>
                        <button class="btn btn-success btn-lg d-block" onclick="runTests()">
                            ⚡ تشغيل الاختبارات
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>📋 خطوات الاختبار اليدوي</h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>افتح صفحة معالجة فواتير المستودع</li>
                            <li>اضغط F12 لفتح Developer Tools</li>
                            <li>انتقل إلى تبويب Console</li>
                            <li>انقر على أي حقل قابل للتعديل</li>
                            <li>راقب الرسائل في Console</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>🧪 نتائج الاختبارات</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults">
                            <div class="info test-result">
                                اضغط على "تشغيل الاختبارات" لبدء الفحص
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>📝 رسائل Console المتوقعة</h5>
                    </div>
                    <div class="card-body">
                        <pre class="bg-dark text-light p-3 rounded">
jQuery loaded: true
CSRF Token: [token value]
Editable fields found: [number > 0]
Field 0: {field: "vender_id", type: "select", value: "1", text: "اسم المورد"}
Field 1: {field: "warehouse_id", type: "select", value: "2", text: "اسم المستودع"}

عند النقر على حقل:
Editable field clicked!
Direct click handler triggered
Cell element: [element]
Field data: {field: "vender_id", type: "select", value: "1", purchaseId: "123"}
Loading options for field: vender_id
Options response: {success: true, options: [...]}
                        </pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function runTests() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<div class="info test-result">جاري تشغيل الاختبارات...</div>';
            
            let results = '';
            
            // Test 1: Check if server is running
            testServerConnection()
                .then(() => {
                    results += '<div class="success test-result">✅ الخادم يعمل بشكل صحيح</div>';
                    return testMainPage();
                })
                .then(() => {
                    results += '<div class="success test-result">✅ صفحة معالجة فواتير المستودع متاحة</div>';
                    return testFieldOptions();
                })
                .then(() => {
                    results += '<div class="success test-result">✅ مسار خيارات الحقول يعمل</div>';
                    return testAjaxEndpoint();
                })
                .then(() => {
                    results += '<div class="success test-result">✅ AJAX يعمل بشكل صحيح</div>';
                    results += '<div class="success test-result">🎉 جميع الاختبارات نجحت! يمكنك الآن اختبار التعديل المباشر</div>';
                    resultsDiv.innerHTML = results;
                })
                .catch(error => {
                    results += `<div class="error test-result">❌ خطأ: ${error.message}</div>`;
                    results += '<div class="warning test-result">⚠️ تحقق من أن الخادم يعمل: php artisan serve</div>';
                    resultsDiv.innerHTML = results;
                });
        }
        
        function testServerConnection() {
            return fetch('/test_ajax.php')
                .then(response => {
                    if (!response.ok) throw new Error('Server not responding');
                    return response.json();
                })
                .then(data => {
                    if (!data.success) throw new Error('Server error');
                });
        }
        
        function testMainPage() {
            return fetch('/warehouse-purchase-processing')
                .then(response => {
                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                });
        }
        
        function testFieldOptions() {
            return fetch('/warehouse-purchase-processing/field-options?field=status')
                .then(response => {
                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    return response.json();
                })
                .then(data => {
                    if (!data.success) throw new Error('Field options failed');
                });
        }
        
        function testAjaxEndpoint() {
            return fetch('/test_ajax.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({test: 'data'})
            })
            .then(response => {
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                return response.json();
            })
            .then(data => {
                if (!data.success) throw new Error('AJAX test failed');
            });
        }
    </script>
</body>
</html>
