using Microsoft.Extensions.DependencyInjection;
using POS_Desktop_App.ViewModels;
using System;
using System.Windows;
using System.Windows.Threading;

namespace POS_Desktop_App.Views
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly DispatcherTimer _clockTimer;

        public MainWindow(IServiceProvider serviceProvider)
        {
            InitializeComponent();
            _serviceProvider = serviceProvider;

            // Initialize clock timer
            _clockTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            _clockTimer.Tick += ClockTimer_Tick;
            _clockTimer.Start();

            // Load initial view
            LoadPosView();

            // Set initial status
            UpdateConnectionStatus(true);
            UpdateCurrentUser("مستخدم تجريبي");
        }

        private void ClockTimer_Tick(object sender, EventArgs e)
        {
            CurrentTime.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss");
        }

        private void LoadPosView()
        {
            try
            {
                var posView = _serviceProvider.GetRequiredService<PosView>();
                MainContent.Content = posView;
                UpdateStatus("تم تحميل شاشة البيع");
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل شاشة البيع: {ex.Message}");
            }
        }

        private void LoadProductsView()
        {
            try
            {
                var productsView = _serviceProvider.GetRequiredService<ProductsView>();
                MainContent.Content = productsView;
                UpdateStatus("تم تحميل شاشة المنتجات");
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل شاشة المنتجات: {ex.Message}");
            }
        }

        private void LoadCustomersView()
        {
            try
            {
                var customersView = _serviceProvider.GetRequiredService<CustomersView>();
                MainContent.Content = customersView;
                UpdateStatus("تم تحميل شاشة العملاء");
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل شاشة العملاء: {ex.Message}");
            }
        }

        private void UpdateConnectionStatus(bool isConnected)
        {
            if (isConnected)
            {
                ConnectionIcon.Kind = MaterialDesignThemes.Wpf.PackIconKind.WifiStrength4;
                ConnectionIcon.Foreground = System.Windows.Media.Brushes.LightGreen;
                ConnectionStatus.Text = "متصل";
            }
            else
            {
                ConnectionIcon.Kind = MaterialDesignThemes.Wpf.PackIconKind.WifiStrengthOff;
                ConnectionIcon.Foreground = System.Windows.Media.Brushes.Orange;
                ConnectionStatus.Text = "غير متصل";
            }
        }

        private void UpdateCurrentUser(string userName)
        {
            CurrentUser.Text = userName;
        }

        private void UpdateStatus(string message)
        {
            StatusText.Text = message;
        }

        private void UpdateLastSyncTime(DateTime? syncTime)
        {
            if (syncTime.HasValue)
            {
                LastSyncTime.Text = syncTime.Value.ToString("HH:mm:ss");
            }
            else
            {
                LastSyncTime.Text = "لم يتم";
            }
        }

        private void ShowError(string message)
        {
            MessageBox.Show(message, "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }

        private void ShowInfo(string message)
        {
            MessageBox.Show(message, "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        // Event Handlers
        private void PosButton_Click(object sender, RoutedEventArgs e)
        {
            LoadPosView();
        }

        private void InvoicesButton_Click(object sender, RoutedEventArgs e)
        {
            ShowInfo("شاشة الفواتير قيد التطوير");
        }

        private void ProductsButton_Click(object sender, RoutedEventArgs e)
        {
            LoadProductsView();
        }

        private void CustomersButton_Click(object sender, RoutedEventArgs e)
        {
            LoadCustomersView();
        }

        private void WarehousesButton_Click(object sender, RoutedEventArgs e)
        {
            ShowInfo("شاشة المستودعات قيد التطوير");
        }

        private void SalesReportButton_Click(object sender, RoutedEventArgs e)
        {
            ShowInfo("تقرير المبيعات قيد التطوير");
        }

        private void InventoryReportButton_Click(object sender, RoutedEventArgs e)
        {
            ShowInfo("تقرير المخزون قيد التطوير");
        }

        private async void SyncButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                UpdateStatus("جاري المزامنة...");
                
                // TODO: Implement sync functionality
                await Task.Delay(2000); // Simulate sync
                
                UpdateStatus("تمت المزامنة بنجاح");
                UpdateLastSyncTime(DateTime.Now);
                ShowInfo("تمت مزامنة البيانات بنجاح");
            }
            catch (Exception ex)
            {
                UpdateStatus("فشل في المزامنة");
                ShowError($"خطأ في المزامنة: {ex.Message}");
            }
        }

        private async void BackupButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                UpdateStatus("جاري إنشاء نسخة احتياطية...");
                
                // TODO: Implement backup functionality
                await Task.Delay(1000); // Simulate backup
                
                UpdateStatus("تم إنشاء النسخة الاحتياطية");
                ShowInfo("تم إنشاء النسخة الاحتياطية بنجاح");
            }
            catch (Exception ex)
            {
                UpdateStatus("فشل في إنشاء النسخة الاحتياطية");
                ShowError($"خطأ في النسخ الاحتياطي: {ex.Message}");
            }
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var settingsView = _serviceProvider.GetRequiredService<SettingsView>();
                MainContent.Content = settingsView;
                UpdateStatus("تم تحميل شاشة الإعدادات");
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل شاشة الإعدادات: {ex.Message}");
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _clockTimer?.Stop();
            base.OnClosed(e);
        }
    }
}
