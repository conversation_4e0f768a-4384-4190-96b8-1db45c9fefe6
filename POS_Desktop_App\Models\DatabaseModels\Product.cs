using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace POS_Desktop_App.Models.DatabaseModels
{
    /// <summary>
    /// نموذج المنتج - يطابق جدول product_services في قاعدة البيانات
    /// </summary>
    [Table("product_services")]
    public class Product
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("name")]
        [Required]
        [StringLength(255)]
        public string Name { get; set; }

        [Column("sku")]
        [Required]
        [StringLength(255)]
        public string Sku { get; set; }

        [Column("sale_price")]
        [Precision(16, 2)]
        public decimal SalePrice { get; set; } = 0.0m;

        [Column("purchase_price")]
        [Precision(16, 2)]
        public decimal PurchasePrice { get; set; } = 0.0m;

        [Column("quantity")]
        public float Quantity { get; set; } = 0.0f;

        [Column("tax_id")]
        [StringLength(50)]
        public string TaxId { get; set; }

        [Column("category_id")]
        public int CategoryId { get; set; } = 0;

        [Column("unit_id")]
        public int UnitId { get; set; } = 0;

        [Column("type")]
        [Required]
        [StringLength(50)]
        public string Type { get; set; }

        [Column("sale_chartaccount_id")]
        public int SaleChartAccountId { get; set; } = 0;

        [Column("expense_chartaccount_id")]
        public int ExpenseChartAccountId { get; set; } = 0;

        [Column("description")]
        public string Description { get; set; }

        [Column("pro_image")]
        [StringLength(255)]
        public string ProImage { get; set; }

        [Column("created_by")]
        public int CreatedBy { get; set; } = 0;

        [Column("expiry_date")]
        public DateTime? ExpiryDate { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; }

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; }

        // Navigation Properties
        public virtual ProductCategory Category { get; set; }
        public virtual ProductUnit Unit { get; set; }
        public virtual ICollection<WarehouseProduct> WarehouseProducts { get; set; } = new List<WarehouseProduct>();
        public virtual ICollection<PosProduct> PosProducts { get; set; } = new List<PosProduct>();

        // Calculated Properties
        [NotMapped]
        public string ImageUrl
        {
            get
            {
                if (string.IsNullOrEmpty(ProImage))
                    return "/Images/no-image.png";
                
                return $"/uploads/pro_image/{ProImage}";
            }
        }

        [NotMapped]
        public bool HasImage => !string.IsNullOrEmpty(ProImage);

        [NotMapped]
        public string CategoryName => Category?.Name ?? "غير محدد";

        [NotMapped]
        public string UnitName => Unit?.Name ?? "قطعة";

        [NotMapped]
        public bool IsExpired => ExpiryDate.HasValue && ExpiryDate.Value < DateTime.Now;

        [NotMapped]
        public bool IsExpiringSoon => ExpiryDate.HasValue && ExpiryDate.Value <= DateTime.Now.AddDays(30);

        [NotMapped]
        public int DaysUntilExpiry
        {
            get
            {
                if (!ExpiryDate.HasValue) return int.MaxValue;
                return (int)(ExpiryDate.Value - DateTime.Now).TotalDays;
            }
        }

        [NotMapped]
        public decimal ProfitMargin
        {
            get
            {
                if (PurchasePrice == 0) return 0;
                return ((SalePrice - PurchasePrice) / PurchasePrice) * 100;
            }
        }

        [NotMapped]
        public decimal ProfitAmount => SalePrice - PurchasePrice;

        // Product Types Constants
        public static class ProductTypes
        {
            public const string Product = "product";
            public const string Service = "service";
        }

        // Helper Methods
        public bool IsProduct()
        {
            return Type == ProductTypes.Product;
        }

        public bool IsService()
        {
            return Type == ProductTypes.Service;
        }

        public List<decimal> GetTaxRates()
        {
            var rates = new List<decimal>();
            
            if (string.IsNullOrEmpty(TaxId))
                return rates;

            var taxIds = TaxId.Split(',');
            foreach (var taxId in taxIds)
            {
                if (int.TryParse(taxId.Trim(), out int id))
                {
                    // Here you would typically look up the tax rate from a tax table
                    // For now, we'll assume a default rate
                    rates.Add(15.0m); // Default VAT rate
                }
            }

            return rates;
        }

        public decimal GetTotalTaxRate()
        {
            return GetTaxRates().Sum();
        }

        public decimal GetPriceWithTax()
        {
            var taxRate = GetTotalTaxRate();
            return SalePrice + (SalePrice * taxRate / 100);
        }

        public decimal GetTaxAmount()
        {
            var taxRate = GetTotalTaxRate();
            return (SalePrice * taxRate) / 100;
        }

        public int GetWarehouseStock(long warehouseId)
        {
            var warehouseProduct = WarehouseProducts?.FirstOrDefault(wp => wp.WarehouseId == warehouseId);
            return warehouseProduct?.Quantity ?? 0;
        }

        public bool HasStockInWarehouse(long warehouseId, int requiredQuantity = 1)
        {
            return GetWarehouseStock(warehouseId) >= requiredQuantity;
        }

        public void UpdateWarehouseStock(long warehouseId, int newQuantity)
        {
            var warehouseProduct = WarehouseProducts?.FirstOrDefault(wp => wp.WarehouseId == warehouseId);
            if (warehouseProduct != null)
            {
                warehouseProduct.Quantity = newQuantity;
            }
        }

        public void AddToWarehouseStock(long warehouseId, int quantity)
        {
            var warehouseProduct = WarehouseProducts?.FirstOrDefault(wp => wp.WarehouseId == warehouseId);
            if (warehouseProduct != null)
            {
                warehouseProduct.Quantity += quantity;
            }
        }

        public void RemoveFromWarehouseStock(long warehouseId, int quantity)
        {
            var warehouseProduct = WarehouseProducts?.FirstOrDefault(wp => wp.WarehouseId == warehouseId);
            if (warehouseProduct != null)
            {
                warehouseProduct.Quantity = Math.Max(0, warehouseProduct.Quantity - quantity);
            }
        }

        // Validation
        public List<string> Validate()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(Name))
                errors.Add("اسم المنتج مطلوب");

            if (string.IsNullOrWhiteSpace(Sku))
                errors.Add("رمز المنتج (SKU) مطلوب");

            if (SalePrice < 0)
                errors.Add("سعر البيع لا يمكن أن يكون سالب");

            if (PurchasePrice < 0)
                errors.Add("سعر الشراء لا يمكن أن يكون سالب");

            if (string.IsNullOrWhiteSpace(Type))
                errors.Add("نوع المنتج مطلوب");

            if (Type != ProductTypes.Product && Type != ProductTypes.Service)
                errors.Add("نوع المنتج غير صحيح");

            if (ExpiryDate.HasValue && ExpiryDate.Value < DateTime.Now.Date)
                errors.Add("تاريخ انتهاء الصلاحية لا يمكن أن يكون في الماضي");

            return errors;
        }

        public bool IsValid()
        {
            return !Validate().Any();
        }

        // Search Methods
        public bool MatchesSearch(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return true;

            searchTerm = searchTerm.ToLower();

            return Name.ToLower().Contains(searchTerm) ||
                   Sku.ToLower().Contains(searchTerm) ||
                   (Description?.ToLower().Contains(searchTerm) == true);
        }

        // Display Methods
        public string GetFormattedSalePrice()
        {
            return SalePrice.ToString("C2");
        }

        public string GetFormattedPurchasePrice()
        {
            return PurchasePrice.ToString("C2");
        }

        public string GetFormattedProfitMargin()
        {
            return $"{ProfitMargin:0.##}%";
        }

        public string GetExpiryStatusText()
        {
            if (!ExpiryDate.HasValue)
                return "بدون تاريخ انتهاء";

            if (IsExpired)
                return "منتهي الصلاحية";

            if (IsExpiringSoon)
                return $"ينتهي خلال {DaysUntilExpiry} يوم";

            return $"صالح لـ {DaysUntilExpiry} يوم";
        }

        // Override Methods
        public override string ToString()
        {
            return $"{Name} ({Sku}) - {GetFormattedSalePrice()}";
        }

        public override bool Equals(object obj)
        {
            if (obj is Product other)
            {
                return this.Id == other.Id;
            }
            return false;
        }

        public override int GetHashCode()
        {
            return Id.GetHashCode();
        }
    }
}
