@echo off
echo ========================================
echo    اختبار نظام نقاط البيع
echo ========================================
echo.

echo فحص الملفات المطلوبة...

if not exist "POS_Desktop_App.csproj" (
    echo خطأ: ملف المشروع غير موجود
    pause
    exit /b 1
)

if not exist "App.xaml" (
    echo خطأ: ملف App.xaml غير موجود
    pause
    exit /b 1
)

if not exist "Views\MainWindow.xaml" (
    echo خطأ: ملف MainWindow.xaml غير موجود
    pause
    exit /b 1
)

echo ✓ جميع الملفات الأساسية موجودة

echo.
echo فحص المجلدات...

if not exist "Models" (
    echo خطأ: مجلد Models غير موجود
    pause
    exit /b 1
)

if not exist "Views" (
    echo خطأ: مجلد Views غير موجود
    pause
    exit /b 1
)

if not exist "ViewModels" (
    echo خطأ: مجلد ViewModels غير موجود
    pause
    exit /b 1
)

if not exist "Services" (
    echo خطأ: مجلد Services غير موجود
    pause
    exit /b 1
)

echo ✓ جميع المجلدات موجودة

echo.
echo ========================================
echo    النظام جاهز للتشغيل!
echo ========================================
echo.
echo لتشغيل النظام، تحتاج إلى:
echo 1. تثبيت .NET 6.0 SDK
echo 2. تشغيل الأمر: dotnet run
echo.
echo أو استخدام Visual Studio 2022
echo.

pause
