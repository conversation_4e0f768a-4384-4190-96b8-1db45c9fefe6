{{ Form::open(['url' => 'vendor-representatives', 'method' => 'post', 'enctype' => 'multipart/form-data']) }}
<div class="modal-body">
    <div class="row">
        <div class="col-lg-12">
            <!-- Header Section -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-gradient-primary text-white">
                    <div class="d-flex align-items-center">
                        <div class="avatar avatar-sm me-3">
                            <span class="avatar-initial rounded bg-white text-primary">
                                <i class="ti ti-user-plus fs-4"></i>
                            </span>
                        </div>
                        <div>
                            <h5 class="card-title mb-0 text-white">{{ __('إضافة مندوب جديد') }}</h5>
                            <small class="text-white-50">{{ __('قم بملء البيانات المطلوبة لإضافة مندوب جديد للنظام') }}</small>
                        </div>
                    </div>
                </div>

                <div class="card-body p-4">
                    <div class="row g-4">
                        <!-- Personal Information Section -->
                        <div class="col-12">
                            <div class="section-header mb-3">
                                <h6 class="section-title text-primary">
                                    <i class="ti ti-user me-2"></i>{{ __('المعلومات الشخصية') }}
                                </h6>
                                <hr class="section-divider">
                            </div>
                        </div>

                        <!-- اسم المندوب -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label fw-semibold">
                                    <i class="ti ti-user text-primary me-2"></i>{{ __('اسم المندوب') }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ Form::text('name', '', [
                                    'class' => 'form-control form-control-lg',
                                    'required' => 'required',
                                    'placeholder' => __('أدخل اسم المندوب بالكامل'),
                                    'id' => 'name'
                                ]) }}
                                <div class="form-text">
                                    <i class="ti ti-info-circle me-1"></i>{{ __('مثال: أحمد محمد علي') }}
                                </div>
                            </div>
                        </div>

                        <!-- رقم الهاتف -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label fw-semibold">
                                    <i class="ti ti-phone text-success me-2"></i>{{ __('رقم الهاتف') }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ Form::text('phone', '', [
                                    'class' => 'form-control form-control-lg',
                                    'required' => 'required',
                                    'placeholder' => __('أدخل رقم الهاتف'),
                                    'pattern' => '[0-9+\-\s()]*',
                                    'id' => 'phone'
                                ]) }}
                                <div class="form-text">
                                    <i class="ti ti-info-circle me-1"></i>{{ __('مثال: 0501234567 أو +966501234567') }}
                                </div>
                            </div>
                        </div>

                        <!-- Business Information Section -->
                        <div class="col-12 mt-4">
                            <div class="section-header mb-3">
                                <h6 class="section-title text-info">
                                    <i class="ti ti-building-store me-2"></i>{{ __('معلومات العمل') }}
                                </h6>
                                <hr class="section-divider">
                            </div>
                        </div>

                        <!-- الشركة الموردة -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label fw-semibold">
                                    <i class="ti ti-building text-info me-2"></i>{{ __('الشركة الموردة') }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ Form::select('vendor_id', $vendors->pluck('name', 'id'), '', [
                                    'class' => 'form-control form-control-lg select2',
                                    'required' => 'required',
                                    'placeholder' => __('اختر الشركة الموردة'),
                                    'id' => 'vendor_id'
                                ]) }}
                                <div class="form-text">
                                    <i class="ti ti-info-circle me-1"></i>{{ __('اختر الشركة التي يعمل معها المندوب') }}
                                </div>
                            </div>
                        </div>

                        <!-- فئة المنتجات -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label fw-semibold">
                                    <i class="ti ti-category text-warning me-2"></i>{{ __('فئة المنتجات') }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ Form::select('category_id', $categories->pluck('name', 'id'), '', [
                                    'class' => 'form-control form-control-lg select2',
                                    'required' => 'required',
                                    'placeholder' => __('اختر فئة المنتجات'),
                                    'id' => 'category_id'
                                ]) }}
                                <div class="form-text">
                                    <i class="ti ti-info-circle me-1"></i>{{ __('اختر فئة المنتجات التي يختص بها المندوب') }}
                                </div>
                            </div>
                        </div>

                        <!-- Settings Section -->
                        <div class="col-12 mt-4">
                            <div class="section-header mb-3">
                                <h6 class="section-title text-success">
                                    <i class="ti ti-settings me-2"></i>{{ __('الإعدادات والملاحظات') }}
                                </h6>
                                <hr class="section-divider">
                            </div>
                        </div>

                        <!-- الحالة -->
                        <div class="col-12">
                            <div class="form-group">
                                <div class="status-card p-3 border rounded-3 bg-light">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div class="d-flex align-items-center">
                                            <div class="status-icon me-3">
                                                <i class="ti ti-toggle-right text-success fs-2"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-1 fw-semibold">{{ __('حالة المندوب') }}</h6>
                                                <small class="text-muted">{{ __('عند التفعيل، سيكون المندوب متاحاً للعمل والتواصل') }}</small>
                                            </div>
                                        </div>
                                        <div class="form-check form-switch">
                                            {{ Form::checkbox('is_active', 1, true, [
                                                'class' => 'form-check-input form-check-input-lg',
                                                'id' => 'is_active'
                                            ]) }}
                                            <label class="form-check-label fw-semibold ms-2" for="is_active">
                                                {{ __('نشط') }}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- الملاحظات -->
                        <div class="col-12">
                            <div class="form-group">
                                <label class="form-label fw-semibold">
                                    <i class="ti ti-notes text-secondary me-2"></i>{{ __('الملاحظات') }}
                                    <span class="badge bg-secondary ms-2">{{ __('اختياري') }}</span>
                                </label>
                                {{ Form::textarea('notes', '', [
                                    'class' => 'form-control form-control-lg',
                                    'rows' => 4,
                                    'placeholder' => __('أدخل أي ملاحظات إضافية حول المندوب (مثل: ساعات العمل، المناطق المختصة، إلخ...)'),
                                    'id' => 'notes'
                                ]) }}
                                <div class="form-text">
                                    <i class="ti ti-info-circle me-1"></i>{{ __('يمكنك إضافة أي معلومات إضافية مفيدة حول المندوب') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal-footer bg-light border-top">
    <button type="button" class="btn btn-light btn-lg" onclick="location.href = '{{ route('vendor.representatives.index') }}';">
        <i class="ti ti-x me-2"></i>{{ __('إلغاء') }}
    </button>
    <button type="submit" class="btn btn-primary btn-lg">
        <i class="ti ti-check me-2"></i>{{ __('إنشاء المندوب') }}
    </button>
</div>
{{ Form::close() }}

<style>
    /* Enhanced Form Styling */
    .bg-gradient-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .card {
        transition: all 0.3s ease;
        border: none !important;
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
    }

    .section-header {
        position: relative;
        margin-bottom: 1.5rem;
    }

    .section-title {
        font-weight: 700;
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
    }

    .section-divider {
        margin: 0;
        border-top: 2px solid #e9ecef;
        opacity: 0.5;
    }

    .form-control-lg {
        height: 50px;
        font-size: 1rem;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .form-control-lg:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        transform: translateY(-1px);
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
    }

    .form-text {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.5rem;
    }

    .status-card {
        transition: all 0.3s ease;
        border: 2px solid #e9ecef !important;
    }

    .status-card:hover {
        border-color: #28a745 !important;
        background-color: #f8fff9 !important;
    }

    .form-check-input-lg {
        transform: scale(1.5);
    }

    .avatar-initial {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
    }

    .btn-lg {
        padding: 12px 30px;
        font-weight: 600;
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .modal-footer {
        padding: 1.5rem;
        border-top: 2px solid #e9ecef;
    }

    /* Select2 Enhancements */
    .select2-container--default .select2-selection--single {
        height: 50px !important;
        border: 2px solid #e9ecef !important;
        border-radius: 10px !important;
        font-size: 1rem !important;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 46px !important;
        padding-left: 12px !important;
        color: #495057;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 46px !important;
        right: 10px !important;
    }

    .select2-container--default.select2-container--focus .select2-selection--single {
        border-color: #007bff !important;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
    }

    .select2-dropdown {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    /* Textarea Styling */
    textarea.form-control-lg {
        min-height: 100px;
        resize: vertical;
    }

    /* Animation Effects */
    .form-group {
        animation: fadeInUp 0.5s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .card-body {
            padding: 1.5rem !important;
        }

        .section-title {
            font-size: 1rem;
        }

        .form-control-lg {
            height: 45px;
        }

        .btn-lg {
            padding: 10px 20px;
        }
    }

    /* Focus States */
    .form-control:focus + .form-text {
        color: #007bff;
    }

    /* Required Field Indicator */
    .text-danger {
        font-weight: 700;
    }

    /* Badge Styling */
    .badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
</style>

<script>
    $(document).ready(function() {
        // Initialize Select2 for dropdowns with enhanced styling
        $('.select2').select2({
            placeholder: function() {
                return $(this).data('placeholder') || $(this).attr('placeholder');
            },
            allowClear: true,
            width: '100%',
            theme: 'default'
        });

        // Phone number formatting
        $('#phone').on('input', function() {
            let value = $(this).val().replace(/\D/g, '');
            if (value.startsWith('966')) {
                value = '+' + value;
            } else if (value.startsWith('05')) {
                // Saudi mobile format
                value = value.replace(/(\d{3})(\d{3})(\d{4})/, '$1 $2 $3');
            }
            $(this).val(value);
        });

        // Character counter for notes
        $('#notes').on('input', function() {
            let length = $(this).val().length;
            let maxLength = 500;

            if (!$(this).next('.char-counter').length) {
                $(this).after('<small class="char-counter text-muted float-end mt-1"></small>');
            }

            $(this).next('.char-counter').text(length + ' / ' + maxLength + ' حرف');

            if (length > maxLength) {
                $(this).addClass('is-invalid');
                $(this).next('.char-counter').addClass('text-danger');
            } else {
                $(this).removeClass('is-invalid');
                $(this).next('.char-counter').removeClass('text-danger');
            }
        });

        // Form validation with enhanced UX
        $('form').on('submit', function(e) {
            let isValid = true;
            let firstError = null;

            // Check required fields
            $(this).find('[required]').each(function() {
                if (!$(this).val()) {
                    isValid = false;
                    $(this).addClass('is-invalid');
                    if (!firstError) {
                        firstError = $(this);
                    }
                } else {
                    $(this).removeClass('is-invalid').addClass('is-valid');
                }
            });

            if (!isValid) {
                e.preventDefault();

                // Show error message
                if (typeof show_toastr === 'function') {
                    show_toastr('خطأ', 'يرجى ملء جميع الحقول المطلوبة', 'error');
                } else {
                    alert('يرجى ملء جميع الحقول المطلوبة');
                }

                // Focus on first error field
                if (firstError) {
                    firstError.focus();
                }

                return false;
            }

            // Show loading state
            $(this).find('button[type="submit"]').html('<i class="ti ti-loader-2 me-2"></i>جاري الحفظ...').prop('disabled', true);
        });

        // Real-time validation
        $('[required]').on('blur', function() {
            if ($(this).val()) {
                $(this).removeClass('is-invalid').addClass('is-valid');
            } else {
                $(this).removeClass('is-valid').addClass('is-invalid');
            }
        });

        // Enhanced form animations
        $('.form-control').on('focus', function() {
            $(this).parent().find('label').addClass('text-primary');
        }).on('blur', function() {
            $(this).parent().find('label').removeClass('text-primary');
        });
    });
</script>

