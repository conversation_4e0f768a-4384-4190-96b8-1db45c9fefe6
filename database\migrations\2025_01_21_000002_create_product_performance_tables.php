<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // جدول أداء المنتجات
        Schema::create('product_performance', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('product_id');
            $table->unsignedBigInteger('warehouse_id')->nullable();
            $table->decimal('sales_velocity', 10, 2)->default(0); // متوسط المبيعات اليومية
            $table->decimal('profit_margin', 5, 2)->default(0); // هامش الربح
            $table->decimal('stock_turnover', 10, 2)->default(0); // معدل دوران المخزون
            $table->integer('days_to_stockout')->default(0); // أيام حتى نفاد المخزون
            $table->integer('reorder_point')->default(0); // نقطة إعادة الطلب
            $table->decimal('performance_score', 5, 2)->default(0); // نقاط الأداء
            $table->integer('total_sold')->default(0); // إجمالي المباع
            $table->decimal('total_revenue', 15, 2)->default(0); // إجمالي الإيرادات
            $table->date('analysis_date'); // تاريخ التحليل
            $table->unsignedBigInteger('created_by');
            $table->timestamps();

            $table->foreign('product_id')->references('id')->on('product_services')->onDelete('cascade');
            $table->foreign('warehouse_id')->references('id')->on('warehouses')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
            $table->index(['product_id', 'warehouse_id', 'analysis_date']);
        });

        // جدول اتجاهات المبيعات
        Schema::create('sales_trends', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('product_id')->nullable();
            $table->unsignedBigInteger('warehouse_id')->nullable();
            $table->enum('trend_period', ['daily', 'weekly', 'monthly'])->default('daily');
            $table->enum('trend_direction', ['up', 'down', 'stable'])->default('stable');
            $table->decimal('growth_rate', 5, 2)->default(0); // معدل النمو
            $table->decimal('seasonality_factor', 5, 2)->default(0); // عامل الموسمية
            $table->date('period_start'); // بداية الفترة
            $table->date('period_end'); // نهاية الفترة
            $table->text('analysis_notes')->nullable(); // ملاحظات التحليل
            $table->unsignedBigInteger('created_by');
            $table->timestamps();

            $table->foreign('product_id')->references('id')->on('product_services')->onDelete('cascade');
            $table->foreign('warehouse_id')->references('id')->on('warehouses')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
            $table->index(['product_id', 'warehouse_id', 'trend_period']);
        });

        // جدول مؤشرات الأداء الرئيسية
        Schema::create('kpi_metrics', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('metric_name', 100); // اسم المؤشر
            $table->decimal('metric_value', 15, 2); // قيمة المؤشر
            $table->decimal('target_value', 15, 2)->nullable(); // القيمة المستهدفة
            $table->unsignedBigInteger('warehouse_id')->nullable();
            $table->enum('period_type', ['daily', 'weekly', 'monthly', 'yearly'])->default('daily');
            $table->date('period_date'); // تاريخ الفترة
            $table->enum('status', ['above_target', 'on_target', 'below_target'])->default('on_target');
            $table->text('description')->nullable(); // وصف المؤشر
            $table->unsignedBigInteger('created_by');
            $table->timestamps();

            $table->foreign('warehouse_id')->references('id')->on('warehouses')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
            $table->index(['metric_name', 'warehouse_id', 'period_date']);
        });

        // جدول الرؤى التلقائية
        Schema::create('automated_insights', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->enum('insight_type', ['opportunity', 'warning', 'achievement', 'recommendation'])->default('recommendation');
            $table->string('title', 200); // عنوان الرؤية
            $table->text('description'); // وصف مفصل
            $table->enum('priority', ['high', 'medium', 'low'])->default('medium');
            $table->unsignedBigInteger('warehouse_id')->nullable();
            $table->unsignedBigInteger('related_customer_id')->nullable(); // عميل مرتبط
            $table->unsignedBigInteger('related_product_id')->nullable(); // منتج مرتبط
            $table->boolean('is_read')->default(false); // هل تم قراءتها
            $table->boolean('is_actionable')->default(true); // هل تحتاج إجراء
            $table->date('expires_at')->nullable(); // تاريخ انتهاء الصلاحية
            $table->unsignedBigInteger('created_by');
            $table->timestamps();

            $table->foreign('warehouse_id')->references('id')->on('warehouses')->onDelete('cascade');
            $table->foreign('related_customer_id')->references('id')->on('customers')->onDelete('cascade');
            $table->foreign('related_product_id')->references('id')->on('product_services')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
            $table->index(['insight_type', 'priority', 'is_read']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('automated_insights');
        Schema::dropIfExists('kpi_metrics');
        Schema::dropIfExists('sales_trends');
        Schema::dropIfExists('product_performance');
    }
};
