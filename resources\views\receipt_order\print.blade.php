<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $receiptOrder->order_number }} - فاتورة {{ $receiptOrder->order_type }}</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            font-size: 14px;
            line-height: 1.4;
            color: #333;
            background: #fff;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #fff;
        }
        
        .invoice-header {
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .company-info {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .company-logo {
            max-height: 80px;
            margin-bottom: 10px;
        }
        
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .company-details {
            font-size: 12px;
            color: #666;
            line-height: 1.3;
        }
        
        .invoice-title {
            text-align: center;
            background: #3498db;
            color: white;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        
        .invoice-title h1 {
            font-size: 22px;
            margin-bottom: 5px;
        }
        
        .invoice-meta {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }
        
        .meta-group {
            flex: 1;
        }
        
        .meta-group h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 14px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        
        .meta-item {
            margin-bottom: 5px;
            font-size: 13px;
        }
        
        .meta-label {
            font-weight: bold;
            color: #555;
        }
        
        .products-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            border: 2px solid #2c3e50;
        }
        
        .products-table th {
            background: #34495e;
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #2c3e50;
        }
        
        .products-table td {
            padding: 10px 8px;
            border: 1px solid #ddd;
            text-align: center;
        }
        
        .products-table tbody tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .products-table tbody tr:hover {
            background: #e8f4f8;
        }
        
        .product-name {
            text-align: right;
            font-weight: 500;
        }
        
        .total-row {
            background: #2c3e50 !important;
            color: white;
            font-weight: bold;
        }
        
        .total-row td {
            border-color: #2c3e50;
        }
        
        .summary-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        
        .summary-box {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            border-right: 4px solid #3498db;
            flex: 1;
            margin-left: 10px;
        }
        
        .summary-box:last-child {
            margin-left: 0;
        }
        
        .summary-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .summary-item {
            margin-bottom: 5px;
            font-size: 13px;
        }
        
        .notes-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 30px;
        }
        
        .notes-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 10px;
        }
        
        .signatures-section {
            display: flex;
            justify-content: space-between;
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }
        
        .signature-box {
            text-align: center;
            flex: 1;
            margin: 0 10px;
        }
        
        .signature-line {
            border-top: 2px solid #333;
            margin-top: 40px;
            padding-top: 5px;
            font-weight: bold;
        }
        
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 12px;
            color: #666;
        }
        
        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            color: white;
        }
        
        .badge-success { background: #27ae60; }
        .badge-info { background: #3498db; }
        .badge-warning { background: #f39c12; }
        .badge-danger { background: #e74c3c; }
        
        @media print {
            body { margin: 0; }
            .invoice-container { padding: 10px; }
            .no-print { display: none !important; }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- رأس الفاتورة -->
        <div class="invoice-header">
            <div class="company-info">
                @php
                    $logo = \App\Models\Utility::get_file('uploads/logo/');
                    $company_logo = \App\Models\Utility::getValByName('company_logo_dark');
                    $img = $logo . '/' . (isset($company_logo) && !empty($company_logo) ? $company_logo : 'logo-dark.png');
                @endphp
                <img src="{{ $img }}" alt="شعار الشركة" class="company-logo">
                <div class="company-name">{{ \App\Models\Utility::getValByName('company_name') ?: 'اسم الشركة' }}</div>
                <div class="company-details">
                    {{ \App\Models\Utility::getValByName('company_address') ?: 'عنوان الشركة' }}<br>
                    هاتف: {{ \App\Models\Utility::getValByName('company_phone') ?: 'هاتف الشركة' }} | 
                    بريد: {{ \App\Models\Utility::getValByName('company_email') ?: 'بريد الشركة' }}
                </div>
            </div>
        </div>

        <!-- عنوان الفاتورة -->
        <div class="invoice-title">
            <h1>
                @if($receiptOrder->order_type === 'استلام بضاعة')
                    فاتورة استلام بضاعة
                @elseif($receiptOrder->order_type === 'نقل بضاعة')
                    أمر نقل بضاعة
                @else
                    أمر إخراج بضاعة
                @endif
            </h1>
            <div>رقم الأمر: {{ $receiptOrder->order_number }}</div>
        </div>

        <!-- معلومات الفاتورة -->
        <div class="invoice-meta">
            <div class="meta-group">
                <h4>معلومات الأمر</h4>
                <div class="meta-item">
                    <span class="meta-label">نوع الأمر:</span>
                    @if($receiptOrder->order_type === 'استلام بضاعة')
                        <span class="badge badge-success">{{ $receiptOrder->order_type }}</span>
                    @elseif($receiptOrder->order_type === 'نقل بضاعة')
                        <span class="badge badge-info">{{ $receiptOrder->order_type }}</span>
                    @else
                        <span class="badge badge-warning">{{ $receiptOrder->order_type }}</span>
                    @endif
                </div>
                @if($receiptOrder->vendor)
                    <div class="meta-item">
                        <span class="meta-label">المورد:</span> {{ $receiptOrder->vendor->name }}
                    </div>
                @endif
                <div class="meta-item">
                    <span class="meta-label">المستودع:</span> {{ $receiptOrder->warehouse->name ?? 'غير محدد' }}
                </div>
                @if($receiptOrder->fromWarehouse)
                    <div class="meta-item">
                        <span class="meta-label">من مستودع:</span> {{ $receiptOrder->fromWarehouse->name }}
                    </div>
                @endif
            </div>
            
            <div class="meta-group">
                <h4>التواريخ</h4>
                <div class="meta-item">
                    <span class="meta-label">التاريخ:</span> {{ \App\Models\Utility::getDateFormated($receiptOrder->invoice_date ?: $receiptOrder->created_at) }}
                </div>
                <div class="meta-item">
                    <span class="meta-label">الوقت:</span> {{ $receiptOrder->created_at->format('H:i') }}
                </div>
                @if($receiptOrder->exit_date)
                    <div class="meta-item">
                        <span class="meta-label">تاريخ الإخراج:</span> {{ \App\Models\Utility::getDateFormated($receiptOrder->exit_date) }}
                    </div>
                @endif
                @if($receiptOrder->invoice_number)
                    <div class="meta-item">
                        <span class="meta-label">رقم الفاتورة:</span> {{ $receiptOrder->invoice_number }}
                    </div>
                @endif
            </div>
            
            <div class="meta-group">
                <h4>معلومات إضافية</h4>
                @if($receiptOrder->exit_reason)
                    <div class="meta-item">
                        <span class="meta-label">سبب الإخراج:</span> {{ $receiptOrder->exit_reason }}
                    </div>
                @endif
                @if($receiptOrder->responsible_person)
                    <div class="meta-item">
                        <span class="meta-label">الشخص المسؤول:</span> {{ $receiptOrder->responsible_person }}
                    </div>
                @endif
                <div class="meta-item">
                    <span class="meta-label">المنشئ:</span> {{ isset($creator) && $creator ? $creator->name : 'غير محدد' }}
                </div>
            </div>
        </div>

        <!-- جدول المنتجات -->
        <table class="products-table">
            <thead>
                <tr>
                    <th width="5%">#</th>
                    <th width="15%">رمز المنتج</th>
                    <th width="30%">اسم المنتج</th>
                    <th width="10%">الكمية</th>
                    @if($receiptOrder->order_type === 'استلام بضاعة')
                        <th width="12%">سعر الوحدة</th>
                        <th width="12%">الإجمالي</th>
                    @endif
                    @if($receiptOrder->products->where('expiry_date', '!=', null)->count() > 0)
                        <th width="12%">تاريخ الصلاحية</th>
                    @endif
                    <th width="16%">ملاحظات</th>
                </tr>
            </thead>
            <tbody>
                @php $totalAmount = 0; @endphp
                @foreach($receiptOrder->products as $index => $item)
                    <tr>
                        <td>{{ $index + 1 }}</td>
                        <td>{{ $item->product->sku ?? 'غير محدد' }}</td>
                        <td class="product-name">{{ $item->product->name ?? 'غير محدد' }}</td>
                        <td>{{ number_format($item->quantity, 2) }}</td>
                        @if($receiptOrder->order_type === 'استلام بضاعة')
                            <td>{{ number_format($item->unit_cost, 2) }}</td>
                            <td>{{ number_format($item->total_cost, 2) }}</td>
                            @php $totalAmount += $item->total_cost; @endphp
                        @endif
                        @if($receiptOrder->products->where('expiry_date', '!=', null)->count() > 0)
                            <td>
                                @if($item->expiry_date)
                                    {{ \App\Models\Utility::getDateFormated($item->expiry_date) }}
                                @else
                                    -
                                @endif
                            </td>
                        @endif
                        <td>
                            @if($item->is_return)
                                <span class="badge badge-danger">مرتجع</span><br>
                            @endif
                            {{ $item->notes }}
                        </td>
                    </tr>
                @endforeach
                
                @if($receiptOrder->order_type === 'استلام بضاعة' && $totalAmount > 0)
                    <tr class="total-row">
                        <td colspan="{{ $receiptOrder->products->where('expiry_date', '!=', null)->count() > 0 ? '5' : '4' }}">الإجمالي</td>
                        <td>{{ number_format($totalAmount, 2) }}</td>
                        <td></td>
                    </tr>
                @endif
            </tbody>
        </table>

        <!-- ملخص الأمر -->
        <div class="summary-section">
            <div class="summary-box">
                <div class="summary-title">ملخص الأمر</div>
                <div class="summary-item"><strong>إجمالي المنتجات:</strong> {{ $receiptOrder->total_products }}</div>
                @if($receiptOrder->order_type === 'استلام بضاعة')
                    <div class="summary-item"><strong>إجمالي المبلغ:</strong> {{ number_format($receiptOrder->total_amount, 2) }}</div>
                @endif
                <div class="summary-item">
                    <strong>الحالة:</strong> 
                    <span class="badge badge-{{ $receiptOrder->status_color }}">{{ $receiptOrder->status }}</span>
                </div>
            </div>
        </div>

        <!-- الملاحظات -->
        @if($receiptOrder->notes)
            <div class="notes-section">
                <div class="notes-title">ملاحظات:</div>
                <div>{{ $receiptOrder->notes }}</div>
            </div>
        @endif

        <!-- التوقيعات -->
        <div class="signatures-section">
            <div class="signature-box">
                <div class="signature-line">توقيع المستلم</div>
            </div>
            <div class="signature-box">
                <div class="signature-line">توقيع المسؤول</div>
            </div>
            <div class="signature-box">
                <div class="signature-line">ختم الشركة</div>
            </div>
        </div>

        <!-- الفوتر -->
        <div class="footer">
            <div>تاريخ الطباعة: {{ now()->format('Y-m-d H:i') }} | طُبع بواسطة: {{ Auth::check() ? Auth::user()->name : 'غير محدد' }}</div>
            <div style="margin-top: 5px;">تم إنشاء هذه الفاتورة بواسطة نظام إدارة المستودعات</div>
        </div>
    </div>

    <script>
        window.onload = function() {
            window.print();
        }
    </script>
</body>
</html>
