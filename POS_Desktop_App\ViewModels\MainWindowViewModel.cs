namespace POS_Desktop_App.ViewModels
{
    public class MainWindowViewModel : BaseViewModel
    {
        private string _currentUser = "مستخدم تجريبي";
        private bool _isConnected = true;
        private string _statusMessage = "جاهز";
        private DateTime? _lastSyncTime;

        public string CurrentUser
        {
            get => _currentUser;
            set => SetProperty(ref _currentUser, value);
        }

        public bool IsConnected
        {
            get => _isConnected;
            set => SetProperty(ref _isConnected, value);
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        public DateTime? LastSyncTime
        {
            get => _lastSyncTime;
            set => SetProperty(ref _lastSyncTime, value);
        }

        public string ConnectionStatusText => IsConnected ? "متصل" : "غير متصل";
        public string LastSyncTimeText => LastSyncTime?.ToString("HH:mm:ss") ?? "لم يتم";
    }
}
