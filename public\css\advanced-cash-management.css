/* Advanced Cash Management Styles */

.header-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 0;
    margin-bottom: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.filter-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 25px;
    border: 1px solid #e3e6f0;
}

.stats-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 20px;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stats-card.success {
    border-left: 5px solid #28a745;
    background: linear-gradient(135deg, #ffffff 0%, #f8fff9 100%);
}

.stats-card.danger {
    border-left: 5px solid #dc3545;
    background: linear-gradient(135deg, #ffffff 0%, #fff8f8 100%);
}

.stats-card.warning {
    border-left: 5px solid #ffc107;
    background: linear-gradient(135deg, #ffffff 0%, #fffef8 100%);
}

.stats-card.info {
    border-left: 5px solid #17a2b8;
    background: linear-gradient(135deg, #ffffff 0%, #f8fcfd 100%);
}

.table-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 25px;
    border: 1px solid #e3e6f0;
}

.table-responsive {
    border-radius: 10px;
    overflow: hidden;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
    padding: 15px 10px;
    font-size: 14px;
}

.table td {
    padding: 12px 10px;
    vertical-align: middle;
    border-color: #e3e6f0;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transition: background-color 0.3s ease;
}

.badge-active {
    background-color: #28a745;
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.badge-closed {
    background-color: #6c757d;
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.chart-container {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
}

.alert-custom {
    border-radius: 10px;
    border: none;
    padding: 15px 20px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.loading-spinner {
    display: none;
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.loading-spinner i {
    color: #667eea;
    margin-bottom: 10px;
}

/* Navigation Tabs Styling */
.nav-tabs {
    border-bottom: 2px solid #e3e6f0;
    margin-bottom: 0;
}

.nav-tabs .nav-link {
    border: none;
    border-radius: 0;
    color: #6c757d;
    font-weight: 500;
    padding: 15px 20px;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    color: #667eea;
    background-color: #f8f9fa;
}

.nav-tabs .nav-link.active {
    color: #667eea;
    background-color: transparent;
    border-color: transparent;
    border-bottom: 3px solid #667eea;
    font-weight: 600;
}

/* Button Styling */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.4);
}

.btn-light {
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #667eea;
    font-weight: 600;
}

.btn-light:hover {
    background-color: white;
    color: #667eea;
    transform: translateY(-2px);
}

/* Form Controls */
.form-select, .form-control {
    border-radius: 8px;
    border: 1px solid #e3e6f0;
    padding: 10px 15px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-select:focus, .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

/* Action Buttons */
.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 6px;
    margin: 0 2px;
}

.btn-sm i {
    font-size: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-section {
        text-align: center;
        padding: 15px;
    }
    
    .header-section .col-md-4 {
        margin-top: 15px;
    }
    
    .stats-card {
        margin-bottom: 15px;
    }
    
    .filter-card .row > div {
        margin-bottom: 15px;
    }
    
    .table-responsive {
        font-size: 14px;
    }
    
    .chart-container {
        height: 250px;
    }
}

/* Animation for data loading */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* Custom scrollbar for tables */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #667eea;
    border-radius: 10px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #5a6fd8;
}

/* Status badges */
.badge {
    font-size: 11px;
    padding: 5px 10px;
    border-radius: 15px;
    font-weight: 500;
}

/* Number formatting */
.number-display {
    font-family: 'Courier New', monospace;
    font-weight: 600;
}

/* Deficit styling */
.deficit-positive {
    color: #dc3545 !important;
    font-weight: bold;
    background-color: rgba(220, 53, 69, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
}

.deficit-zero {
    color: #28a745 !important;
    font-weight: 500;
}

.deficit-highlight {
    animation: pulse-red 2s infinite;
}

@keyframes pulse-red {
    0% {
        background-color: rgba(220, 53, 69, 0.1);
    }
    50% {
        background-color: rgba(220, 53, 69, 0.3);
    }
    100% {
        background-color: rgba(220, 53, 69, 0.1);
    }
}

/* POS Sales Table Styling */
.pos-sales-table {
    font-size: 0.9rem;
}

.pos-sales-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.pos-sales-table td {
    vertical-align: middle;
    padding: 12px 8px;
}

/* Deficit/Surplus styling */
.deficit-cell {
    background-color: rgba(220, 53, 69, 0.05);
    border-left: 3px solid #dc3545;
}

.surplus-cell {
    background-color: rgba(255, 193, 7, 0.05);
    border-left: 3px solid #ffc107;
}

.balanced-cell {
    background-color: rgba(40, 167, 69, 0.05);
    border-left: 3px solid #28a745;
}

/* Open shift styling */
.open-shift-row {
    position: relative;
    animation: subtle-pulse 3s infinite;
}

.open-shift-row::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 15px 15px 0;
    border-color: transparent #ffc107 transparent transparent;
}

@keyframes subtle-pulse {
    0%, 100% {
        background-color: inherit;
    }
    50% {
        background-color: rgba(255, 193, 7, 0.1);
    }
}

/* Shift status badges */
.badge-success {
    background-color: #28a745 !important;
    color: white;
}

.badge-warning {
    background-color: #ffc107 !important;
    color: #212529;
}

/* Real-time update notifications */
.voucher-update-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 9999;
    font-size: 14px;
    font-weight: 500;
    display: none;
    animation: slideInRight 0.3s ease-out;
}

.voucher-update-notification i {
    animation: spin 1s linear infinite;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Real-time data indicators */
.real-time-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    background-color: #28a745;
    border-radius: 50%;
    margin-left: 8px;
    animation: pulse-green 2s infinite;
}

@keyframes pulse-green {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.2);
    }
}

/* Live data table styling */
.live-data-table {
    position: relative;
}

.live-data-table::before {
    content: 'LIVE';
    position: absolute;
    top: -10px;
    right: 10px;
    background: #dc3545;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: bold;
    z-index: 10;
}

/* Icon styling */
.fas, .ti {
    margin-right: 5px;
}

/* Card hover effects */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}
