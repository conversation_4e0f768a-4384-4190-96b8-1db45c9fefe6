<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BillAttachment extends Model
{
    use HasFactory;

    protected $fillable = [
        'bill_id',
        'file_name',
        'original_name',
        'file_path',
        'file_size',
        'file_type',
        'uploaded_by'
    ];

    public function bill()
    {
        return $this->belongsTo(Bill::class);
    }

    public function uploader()
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }
}
