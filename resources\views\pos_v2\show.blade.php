@if (isset($sales['data']))
    <!-- Hidden fields for payment processing -->
    <input type="hidden" id="vc_name_hidden" value="{{ $details['customer']['id'] ?? 0 }}">
    <input type="hidden" id="warehouse_name_hidden" value="{{ $details['warehouse']['id'] ?? 0 }}">
    <input type="hidden" id="discount_hidden" value="{{ $sales['discount'] ?? 0 }}">
    <input type="hidden" id="delivery_user_hidden" value="{{ $details['user']['id'] ?? 0 }}">

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('POS V2 Invoice Details') }}</h5>
                </div>
                <div class="card-body">
                    <!-- Invoice Header -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6><strong>{{ __('Invoice Number') }}:</strong> {{ $details['pos_id'] }}</h6>
                            <h6><strong>{{ __('Date') }}:</strong> {{ $details['date'] }}</h6>
                        </div>
                        <div class="col-md-6 text-end">
                            @if (!empty($details['customer']))
                                <h6><strong>{{ __('Customer') }}:</strong> {{ $details['customer']['name'] }}</h6>
                                <p>{{ $details['customer']['email'] ?? '' }}</p>
                            @endif
                            @if (!empty($details['warehouse']))
                                <h6><strong>{{ __('Warehouse') }}:</strong> {{ $details['warehouse']['name'] }}</h6>
                            @endif
                        </div>
                    </div>

                    <!-- Invoice Items -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead class="thead-light">
                                <tr>
                                    <th>{{ __('Product') }}</th>
                                    <th class="text-center">{{ __('Quantity') }}</th>
                                    <th class="text-right">{{ __('Price') }}</th>
                                    <th class="text-right">{{ __('Discount') }}</th>
                                    <th class="text-right">{{ __('Tax') }}</th>
                                    <th class="text-right">{{ __('Total') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($sales['data'] as $key => $product)
                                    <tr>
                                        <td>{{ $product['name'] }}</td>
                                        <td class="text-center">{{ $product['quantity'] }}</td>
                                        <td class="text-right">{{ $product['price'] }}</td>
                                        <td class="text-right">{{ $product['discount'] }}</td>
                                        <td class="text-right">{{ $product['tax'] }}</td>
                                        <td class="text-right">{{ $product['total'] }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="5" class="text-right"><strong>{{ __('Sub Total') }}:</strong></td>
                                    <td class="text-right"><strong>{{ $sales['sub_total'] }}</strong></td>
                                </tr>
                                @if ($sales['discount'] > 0)
                                    <tr>
                                        <td colspan="5" class="text-right"><strong>{{ __('Discount') }}:</strong></td>
                                        <td class="text-right"><strong>-{{ $sales['discount'] }}</strong></td>
                                    </tr>
                                @endif
                                <tr>
                                    <td colspan="5" class="text-right"><strong>{{ __('Tax') }}:</strong></td>
                                    <td class="text-right"><strong>{{ $sales['tax_format'] }}</strong></td>
                                </tr>
                                <tr class="pos-header">
                                    <td colspan="5" class="text-right"><strong>{{ __('Total') }}:</strong></td>
                                    <td class="text-right"><strong>{{ \Auth::user()->priceFormat($sales['total']) }}</strong></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- Payment Options -->
                    @if ($details['pay'] == 'show')
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <h5>{{ __('Payment Options') }}</h5>
                                <div class="payment-options">
                                    <!-- Cash Payment -->
                                    <button class="btn btn-success payment-done-btn rounded mt-2 me-2"
                                            data-payment-type="cash"
                                            data-url="{{ route('pos_v2.process_payment') }}"
                                            data-ajax-popup="true"
                                            data-size="sm"
                                            data-bs-toggle="tooltip"
                                            data-title="{{ __('Cash Payment') }}">
                                        <i class="ti ti-cash"></i> {{ __('Cash Payment') }}
                                    </button>

                                    <!-- Network Payment -->
                                    <button class="btn btn-info payment-done-btn rounded mt-2 me-2"
                                            data-payment-type="network"
                                            data-url="{{ route('pos_v2.process_payment') }}"
                                            data-ajax-popup="true"
                                            data-size="sm"
                                            data-bs-toggle="tooltip"
                                            data-title="{{ __('Network Payment') }}">
                                        <i class="ti ti-credit-card"></i> {{ __('Network Payment') }}
                                    </button>

                                    <!-- Split Payment -->
                                    <button class="btn btn-warning payment-done-btn rounded mt-2 me-2"
                                            data-payment-type="split"
                                            data-url="{{ route('pos_v2.process_payment') }}"
                                            data-ajax-popup="true"
                                            data-size="lg"
                                            data-bs-toggle="tooltip"
                                            data-title="{{ __('Split Payment') }}">
                                        <i class="ti ti-wallet"></i> {{ __('Split Payment') }}
                                    </button>

                                    <!-- Print Preview -->
                                    <button class="btn btn-secondary rounded mt-2 me-2"
                                            onclick="printInvoice()">
                                        <i class="ti ti-printer"></i> {{ __('Print Preview') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endif

<script>
    $(document).on('click', '.payment-done-btn', function (e) {
        e.preventDefault();
        var ele = $(this);
        var paymentType = ele.data('payment-type');

        // إضافة تأكيد قبل المعالجة
        if (!confirm('{{ __("Are you sure you want to process this payment?") }}')) {
            return false;
        }

        $.ajax({
            url: "{{ route('pos_v2.data.store') }}",
            method: 'GET',
            data: {
                vc_name: $('#vc_name_hidden').val(),
                user_id: $('#delivery_user_hidden').val(),
                warehouse_name: $('#warehouse_name_hidden').val(),
                discount: $('#discount_hidden').val(),
                payment_type: paymentType,
                total_amount: {{ $sales['total'] ?? 0 }}
            },
            beforeSend: function () {
                ele.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status"></span> {{ __("Processing...") }}');
                // تعطيل جميع أزرار الدفع لمنع الضغط المتعدد
                $('.payment-done-btn').prop('disabled', true);
            },
            success: function (data) {
                if (data.code == 200) {
                    $('#carthtml').load(document.URL + ' #carthtml');
                    show_toastr('success', data.success, 'success');

                    // إذا تم إنشاء الفاتورة بنجاح، إظهار خيارات ما بعد الدفع
                    if (data.pos_id) {
                        // إخفاء أزرار الدفع
                        $('.payment-options').hide();

                        // إضافة قسم خيارات ما بعد الدفع
                        var postPaymentOptions = '<div class="post-payment-options mt-3 text-center">' +
                            '<h5 class="text-success mb-3"><i class="ti ti-check-circle"></i> {{ __("Payment Completed Successfully!") }}</h5>' +
                            '<div class="btn-group" role="group">' +
                                '<a href="{{ route("pos_v2.thermal.print", ":pos_id") }}" target="_blank" class="btn btn-success btn-sm me-2">' +
                                    '<i class="ti ti-device-mobile"></i> {{ __("🖨️ Thermal Print") }}</a>' +
                                '<button type="button" class="btn btn-info btn-sm me-2" onclick="printInvoice()">' +
                                    '<i class="ti ti-printer"></i> {{ __("Print Preview") }}</button>' +
                                '<button type="button" class="btn btn-secondary btn-sm" onclick="window.location.reload()">' +
                                    '<i class="ti ti-refresh"></i> {{ __("New Sale") }}</button>' +
                            '</div>' +
                        '</div>';

                        postPaymentOptions = postPaymentOptions.replace(':pos_id', data.pos_id);
                        $('.payment-options').after(postPaymentOptions);

                        // إغلاق النافذة المنبثقة بعد 5 ثوان مع عداد تنازلي
                        var countdown = 5;
                        var countdownInterval = setInterval(function() {
                            countdown--;
                            if (countdown <= 0) {
                                clearInterval(countdownInterval);
                                $('#commonModal').modal('hide');
                                // إعادة تحميل الصفحة لتفريغ السلة
                                window.location.reload();
                            } else {
                                $('.post-payment-options h5').html('<i class="ti ti-check-circle"></i> {{ __("Payment Completed Successfully!") }} <small class="text-muted">({{ __("Auto close in") }} ' + countdown + 's)</small>');
                            }
                        }, 1000);
                    }
                } else {
                    show_toastr('error', data.error || '{{ __("Payment failed") }}', 'error');
                    ele.prop('disabled', false).html('<i class="ti ti-cash"></i> ' + paymentType.charAt(0).toUpperCase() + paymentType.slice(1) + ' {{ __("Payment") }}');
                    $('.payment-done-btn').prop('disabled', false);
                }
            },
            error: function (xhr) {
                var data = xhr.responseJSON;
                show_toastr('error', data.error || '{{ __("An error occurred during payment processing") }}', 'error');
                ele.prop('disabled', false).html('<i class="ti ti-cash"></i> ' + paymentType.charAt(0).toUpperCase() + paymentType.slice(1) + ' {{ __("Payment") }}');
                $('.payment-done-btn').prop('disabled', false);
            }
        });
    });

    function printInvoice() {
        var printContent = document.querySelector('.card-body').innerHTML;
        var originalContent = document.body.innerHTML;

        document.body.innerHTML = '<div style="padding: 20px;">' + printContent + '</div>';
        window.print();
        document.body.innerHTML = originalContent;
        location.reload();
    }
</script>
