<?php
/**
 * ملف اختبار لفحص مشكلة الطباعة الحرارية
 * يجب تشغيل هذا الملف من خلال متصفح الويب للتحقق من المشكلة
 */

// هذا الملف للاختبار فقط - يجب حذفه بعد حل المشكلة
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مشكلة الطباعة الحرارية</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        h2 { color: #333; }
        h3 { color: #666; }
        code { background-color: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
        .step { margin: 10px 0; padding: 10px; background-color: #f8f9fa; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🔍 دليل اختبار وحل مشكلة الطباعة الحرارية</h1>
    
    <div class="test-section info">
        <h2>📋 وصف المشكلة</h2>
        <p>عدم ظهور زر الطباعة الحرارية في شاشة نجاح الدفع عندما يتم إضافة المنتجات مباشرة من شاشة إدارة المخزون.</p>
    </div>

    <div class="test-section warning">
        <h2>🔧 الحلول المطبقة</h2>
        <div class="step">
            <h3>✅ الحل الأول: إضافة سجلات stock_report</h3>
            <p>تم تعديل <code>InventoryManagementController.php</code> لإضافة سجلات في جدول <code>stock_report</code> عند:</p>
            <ul>
                <li>إضافة منتج جديد إلى المستودع</li>
                <li>تحديث كمية منتج موجود</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>🧪 خطوات الاختبار</h2>
        
        <div class="step">
            <h3>الخطوة 1: اختبار إضافة منتج جديد</h3>
            <ol>
                <li>اذهب إلى <strong>إدارة العمليات → إدارة المخزون</strong></li>
                <li>اختر مستودع</li>
                <li>أضف منتج جديد بكمية معينة</li>
                <li>تحقق من إضافة سجل في جدول <code>stock_reports</code></li>
            </ol>
        </div>

        <div class="step">
            <h3>الخطوة 2: اختبار عملية البيع</h3>
            <ol>
                <li>اذهب إلى <strong>نقاط البيع</strong></li>
                <li>اختر نفس المستودع</li>
                <li>أضف المنتج الذي تم إضافته في الخطوة 1</li>
                <li>اختر عميل وأكمل عملية الدفع</li>
                <li>تحقق من ظهور زر الطباعة الحرارية في شاشة نجاح الدفع</li>
            </ol>
        </div>

        <div class="step">
            <h3>الخطوة 3: اختبار الطباعة الحرارية</h3>
            <ol>
                <li>انقر على زر "طباعة حرارية"</li>
                <li>تحقق من فتح نافذة الطباعة الحرارية</li>
                <li>تحقق من عرض بيانات الفاتورة بشكل صحيح</li>
            </ol>
        </div>
    </div>

    <div class="test-section">
        <h2>🔍 فحص قاعدة البيانات</h2>
        
        <div class="step">
            <h3>فحص جدول stock_reports</h3>
            <p>تحقق من وجود سجلات بالنوع:</p>
            <ul>
                <li><code>manual_inventory_add</code> - للمنتجات المضافة يدوياً</li>
                <li><code>manual_inventory_increase</code> - لزيادة الكمية</li>
                <li><code>manual_inventory_decrease</code> - لتقليل الكمية</li>
            </ul>
        </div>

        <div class="step">
            <h3>فحص جدول warehouse_products</h3>
            <p>تحقق من وجود المنتج في المستودع المحدد مع الكمية الصحيحة.</p>
        </div>

        <div class="step">
            <h3>فحص جدول pos</h3>
            <p>تحقق من إنشاء فاتورة نقطة البيع بشكل صحيح مع:</p>
            <ul>
                <li>ربط صحيح بالمستودع</li>
                <li>ربط صحيح بالعميل</li>
                <li>حالة الدفع <code>is_payment_set = 1</code></li>
            </ul>
        </div>
    </div>

    <div class="test-section error">
        <h2>⚠️ مشاكل محتملة أخرى</h2>
        
        <div class="step">
            <h3>مشكلة الصلاحيات</h3>
            <p>تحقق من أن المستخدم لديه صلاحية <code>manage pos</code></p>
        </div>

        <div class="step">
            <h3>مشكلة الوردية</h3>
            <p>تحقق من وجود وردية مفتوحة للمستودع المحدد</p>
        </div>

        <div class="step">
            <h3>مشكلة JavaScript</h3>
            <p>تحقق من وجود أخطاء JavaScript في وحدة تحكم المتصفح</p>
        </div>
    </div>

    <div class="test-section success">
        <h2>✅ إذا استمرت المشكلة</h2>
        
        <div class="step">
            <h3>فحص سجلات Laravel</h3>
            <p>تحقق من ملف <code>storage/logs/laravel.log</code> للبحث عن أخطاء</p>
        </div>

        <div class="step">
            <h3>تفعيل وضع التطوير</h3>
            <p>في ملف <code>.env</code> تأكد من:</p>
            <code>APP_DEBUG=true</code>
        </div>

        <div class="step">
            <h3>مسح الذاكرة المؤقتة</h3>
            <p>تشغيل الأوامر التالية:</p>
            <ul>
                <li><code>php artisan cache:clear</code></li>
                <li><code>php artisan config:clear</code></li>
                <li><code>php artisan view:clear</code></li>
                <li><code>php artisan route:clear</code></li>
            </ul>
        </div>
    </div>

    <div class="test-section info">
        <h2>📞 للدعم الإضافي</h2>
        <p>إذا استمرت المشكلة بعد تطبيق الحلول، يرجى تقديم:</p>
        <ul>
            <li>لقطة شاشة من شاشة نجاح الدفع</li>
            <li>سجلات الأخطاء من Laravel</li>
            <li>خطوات إعادة إنتاج المشكلة بالتفصيل</li>
        </ul>
    </div>

    <script>
        console.log('🔍 دليل اختبار الطباعة الحرارية محمل بنجاح');
        console.log('تحقق من تطبيق جميع الخطوات المذكورة أعلاه');
    </script>
</body>
</html>
