using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using POS_Desktop_App.Services.CacheService;
using POS_Desktop_App.Services.DatabaseService;
using POS_Desktop_App.Services.SyncService;
using POS_Desktop_App.ViewModels;
using POS_Desktop_App.Views;
using System;
using System.IO;
using System.Windows;
using System.Windows.Threading;

namespace POS_Desktop_App
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        private IHost? _host;

        protected override async void OnStartup(StartupEventArgs e)
        {
            // Create host
            _host = CreateHostBuilder(e.Args).Build();

            // Initialize services
            await InitializeServicesAsync();

            // Start the host
            await _host.StartAsync();

            // Show main window
            var mainWindow = _host.Services.GetRequiredService<MainWindow>();
            mainWindow.Show();

            base.OnStartup(e);
        }

        protected override async void OnExit(ExitEventArgs e)
        {
            if (_host != null)
            {
                await _host.StopAsync();
                _host.Dispose();
            }

            base.OnExit(e);
        }

        private static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureServices((context, services) =>
                {
                    // Register DbContexts
                    services.AddDbContext<PosDbContext>();
                    services.AddDbContext<LocalCacheDbContext>();

                    // Register HTTP Client
                    services.AddHttpClient<SyncService>(client =>
                    {
                        client.BaseAddress = new Uri("http://localhost:8000/api/");
                        client.Timeout = TimeSpan.FromSeconds(30);
                    });

                    // Register Services
                    services.AddSingleton<SyncService>();
                    services.AddTransient<DatabaseInitializationService>();

                    // Register ViewModels
                    services.AddTransient<MainWindowViewModel>();
                    services.AddTransient<PosViewModel>();
                    services.AddTransient<ProductsViewModel>();
                    services.AddTransient<CustomersViewModel>();
                    services.AddTransient<SettingsViewModel>();

                    // Register Views
                    services.AddTransient<MainWindow>();
                    services.AddTransient<PosView>();
                    services.AddTransient<ProductsView>();
                    services.AddTransient<CustomersView>();
                    services.AddTransient<SettingsView>();

                    // Register Logging
                    services.AddLogging(builder =>
                    {
                        builder.AddConsole();
                        builder.AddDebug();
                        builder.AddFile("Logs/app-{Date}.log");
                    });
                });

        private async Task InitializeServicesAsync()
        {
            try
            {
                var logger = _host?.Services.GetRequiredService<ILogger<App>>();
                logger?.LogInformation("Initializing application services...");

                // Initialize local database
                var localDb = _host?.Services.GetRequiredService<LocalCacheDbContext>();
                if (localDb != null)
                {
                    await localDb.InitializeDatabaseAsync();
                    logger?.LogInformation("Local database initialized successfully");
                }

                // Test server connection
                var syncService = _host?.Services.GetRequiredService<SyncService>();
                if (syncService != null)
                {
                    var isServerReachable = await syncService.IsServerReachableAsync();
                    if (isServerReachable)
                    {
                        logger?.LogInformation("Server connection established");
                        // Start initial sync
                        _ = Task.Run(async () => await syncService.SyncAllAsync());
                    }
                    else
                    {
                        logger?.LogWarning("Server is not reachable. Running in offline mode.");
                        ShowOfflineModeMessage();
                    }
                }

                logger?.LogInformation("Application services initialized successfully");
            }
            catch (Exception ex)
            {
                var logger = _host?.Services.GetRequiredService<ILogger<App>>();
                logger?.LogError(ex, "Failed to initialize application services");
                
                MessageBox.Show(
                    $"فشل في تهيئة التطبيق:\n{ex.Message}",
                    "خطأ في التهيئة",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        private void ShowOfflineModeMessage()
        {
            Dispatcher.BeginInvoke(() =>
            {
                MessageBox.Show(
                    "لا يمكن الاتصال بالسيرفر.\nسيعمل التطبيق في الوضع غير المتصل.\nسيتم مزامنة البيانات عند عودة الاتصال.",
                    "وضع عدم الاتصال",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);
            });
        }

        private void Application_DispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
        {
            var logger = _host?.Services.GetRequiredService<ILogger<App>>();
            logger?.LogError(e.Exception, "Unhandled exception occurred");

            MessageBox.Show(
                $"حدث خطأ غير متوقع:\n{e.Exception.Message}",
                "خطأ",
                MessageBoxButton.OK,
                MessageBoxImage.Error);

            e.Handled = true;
        }

        private void CreateDirectories()
        {
            var directories = new[]
            {
                "Data",
                "Logs",
                "Backups",
                "Exports",
                "Reports",
                "Temp"
            };

            foreach (var directory in directories)
            {
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
            }
        }
    }

    /// <summary>
    /// Service for initializing databases
    /// </summary>
    public class DatabaseInitializationService
    {
        private readonly PosDbContext _serverDb;
        private readonly LocalCacheDbContext _localDb;
        private readonly ILogger<DatabaseInitializationService> _logger;

        public DatabaseInitializationService(
            PosDbContext serverDb,
            LocalCacheDbContext localDb,
            ILogger<DatabaseInitializationService> logger)
        {
            _serverDb = serverDb;
            _localDb = localDb;
            _logger = logger;
        }

        public async Task InitializeAsync()
        {
            try
            {
                // Initialize local database
                await _localDb.InitializeDatabaseAsync();
                _logger.LogInformation("Local database initialized");

                // Test server connection
                var isServerConnected = await _serverDb.TestConnectionAsync();
                if (isServerConnected)
                {
                    await _serverDb.InitializeDatabaseAsync();
                    _logger.LogInformation("Server database connection established");
                }
                else
                {
                    _logger.LogWarning("Cannot connect to server database");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize databases");
                throw;
            }
        }
    }
}
