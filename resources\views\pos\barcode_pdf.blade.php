<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة الباركود - {{ $companyData['company_name'] }}</title>
    <style>
        @page {
            margin: 10mm;
            size: A4;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
            direction: rtl;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
        }
        
        .company-logo {
            max-width: 80px;
            max-height: 80px;
            margin-bottom: 10px;
        }
        
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .company-info {
            font-size: 11px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .report-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .report-date {
            font-size: 11px;
            color: #666;
        }
        
        .products-grid {
            width: 100%;
            margin-top: 20px;
        }

        .product-row {
            page-break-inside: avoid;
            display: flex;
            width: 100%;
            margin-bottom: 5px;
        }

        .product-cell {
            width: 33.33%;
            padding: 3px;
            vertical-align: top;
        }
        
        .barcode-item {
            border: 3px solid #000;
            padding: 8px;
            margin: 2px;
            text-align: center;
            background: #fff;
            min-height: 110px;
            position: relative;
            page-break-inside: avoid;
            border-radius: 5px;
        }

        .product-name {
            font-weight: bold;
            font-size: 10px;
            margin-bottom: 5px;
            color: #000;
            word-wrap: break-word;
            line-height: 1.2;
            max-height: 24px;
            overflow: hidden;
        }

        .product-sku {
            font-size: 8px;
            color: #666;
            margin-bottom: 5px;
            font-family: monospace;
        }
        
        .barcode-container {
            margin: 10px 0;
            min-height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .product-price {
            font-size: 12px;
            font-weight: bold;
            color: #333;
            margin-top: 8px;
        }
        
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        /* تحسين الطباعة */
        @media print {
            .header {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .barcode-item {
                break-inside: avoid;
            }
        }
        
        /* أنماط الباركود */
        .barcode-svg {
            max-width: 100%;
            height: auto;
        }
        
        .no-products {
            text-align: center;
            padding: 50px;
            font-size: 16px;
            color: #666;
        }
    </style>
</head>
<body>
    <!-- رأس الصفحة المبسط -->
    <div class="header">
        <div class="company-name">{{ $companyData['company_name'] }}</div>
        <div class="report-title">لصاقات الأسعار والباركود</div>
        <div class="report-date">{{ date('Y-m-d') }}</div>
    </div>

    <!-- محتوى المنتجات -->
    @if($productServices->count() > 0)
        <div class="products-grid">
            @foreach($productServices->chunk(3) as $chunkIndex => $chunk)
                <div class="product-row">
                    @foreach($chunk as $productService)
                        @php
                            // حساب السعر مع الضريبة
                            $price = $productService->sale_price ?? 0;
                            $taxRate = 0;
                            if (!empty($productService->tax_id) && method_exists($productService, 'taxRate')) {
                                $taxRate = $productService->taxRate($productService->tax_id);
                            }
                            $taxAmount = ($taxRate / 100) * $price;
                            $priceWithTax = $price + $taxAmount;
                            $formattedPrice = number_format($priceWithTax, 2) . ' ر.س';
                        @endphp

                        <div class="product-cell">
                            <div class="barcode-item">
                                <div class="product-name">{{ $productService->name ?? 'منتج غير محدد' }}</div>
                                <div class="product-sku">{{ $productService->sku ?? 'N/A' }}</div>

                                <div class="barcode-container">
                                    <!-- عرض الباركود المُعد مسبقاً -->
                                    {!! $productService->barcode_html ?? '<div style="text-align: center; padding: 10px;">لا يوجد باركود</div>' !!}
                                </div>

                                <div class="product-price" style="font-size: 14px; font-weight: bold; color: #d32f2f;">
                                    {{ $formattedPrice }}
                                </div>
                            </div>
                        </div>
                    @endforeach

                    @for($i = $chunk->count(); $i < 3; $i++)
                        <div class="product-cell">
                            <!-- خلية فارغة للتوازن -->
                        </div>
                    @endfor
                </div>

                @if(($chunkIndex + 1) % 6 == 0 && !$loop->last)
                    <!-- فاصل صفحة كل 18 منتج (6 صفوف) -->
                    <div class="page-break"></div>
                @endif
            @endforeach
        </div>
    @else
        <div class="no-products">
            لا توجد منتجات متاحة لإنشاء الباركود
        </div>
    @endif

    <!-- تذييل الصفحة -->
    <div class="footer">
        <p>عدد المنتجات: {{ $productServices->count() }} منتج | {{ $companyData['company_name'] }}</p>
    </div>


</body>
</html>
