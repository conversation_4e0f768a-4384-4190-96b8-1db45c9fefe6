<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class InvoiceProduct extends Model
{
    protected $fillable = [
        'product_id',
        'product_name',
        'invoice_id',
        'quantity',
        'tax',
        'discount',
        'price',
        'total',
        'description',
    ];

    public function product(){
        return $this->hasOne('App\Models\ProductService', 'id', 'product_id');
    }

    /**
     * Get the product directly
     */
    public function getProduct()
    {
        return ProductService::find($this->product_id);
    }


}
