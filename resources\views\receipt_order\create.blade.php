@extends('layouts.admin')
@section('page-title')
    {{ __('مراقبة المستخدمين والأموال') }}
@endsection

@push('style-page')
<style>
.product-search-container {
    position: relative;
}

.product-search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.product-search-item {
    padding: 10px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
}

.product-search-item:hover {
    background-color: #f8f9fa;
}

.product-search-item:last-child {
    border-bottom: none;
}

.section-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin: -1.25rem -1.25rem 1.25rem -1.25rem;
    padding: 1rem 1.25rem;
    border-radius: 0.375rem 0.375rem 0 0;
}

.summary-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    border: none;
}

.summary-card .card-body {
    padding: 1.5rem;
}

.summary-value {
    font-size: 1.5rem;
    font-weight: bold;
    margin-top: 0.5rem;
}

.product-row-highlight {
    background-color: #fff3cd !important;
}

.return-item {
    background-color: #f8d7da !important;
}

.btn-add-product {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
}

.btn-add-product:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    color: white;
}

.form-floating-custom {
    position: relative;
}

.form-floating-custom label {
    position: absolute;
    top: 0;
    left: 0.75rem;
    height: 100%;
    padding: 1rem 0;
    pointer-events: none;
    border: 1px solid transparent;
    transform-origin: 0 0;
    transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
    color: #6c757d;
}

.quantity-badge {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.cost-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.total-badge {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-size: 1.1rem;
    font-weight: bold;
}

.table-products {
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    border-radius: 0.5rem;
    overflow: hidden;
}

.table-products thead {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.table-products thead th {
    border: none;
    padding: 1rem;
    font-weight: 600;
}

.table-products tbody td {
    padding: 0.75rem;
    vertical-align: middle;
    border-color: #e9ecef;
}

.order-type-selector {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 0.5rem;
    padding: 1rem;
    transition: all 0.3s ease;
}

.order-type-selector:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.warehouse-selector {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 0.5rem;
    padding: 1rem;
    transition: all 0.3s ease;
}

.warehouse-selector:focus-within {
    border-color: #11998e;
    box-shadow: 0 0 0 0.2rem rgba(17, 153, 142, 0.25);
}
</style>
@endpush

@push('script-page')
<script>
$(document).ready(function() {
    let productCounter = 0;
    let totalProducts = 0;
    let totalAmount = 0;

    // Order type change handler
    $('#order_type').on('change', function() {
        const orderType = $(this).val();
        if (orderType === 'استلام بضاعة') {
            $('#receipt_fields').show();
            $('#transfer_fields').hide();
            $('#exit_fields').hide();
        } else if (orderType === 'نقل بضاعة') {
            $('#receipt_fields').hide();
            $('#transfer_fields').show();
            $('#exit_fields').hide();
        } else if (orderType === 'أمر إخراج') {
            $('#receipt_fields').hide();
            $('#transfer_fields').hide();
            $('#exit_fields').show();
        } else {
            $('#receipt_fields').hide();
            $('#transfer_fields').hide();
            $('#exit_fields').hide();
        }
        clearProductsTable();
    });

    // Warehouse change handler
    $('#warehouse_id, #from_warehouse_id').on('change', function() {
        clearProductsTable();

        // Add first product row when warehouse is selected
        const warehouseId = $('#warehouse_id').val();
        const orderType = $('#order_type').val();

        if (warehouseId) {
            addProductRow();
        }
    });

    // Add product row
    $('#add_product_btn').on('click', function() {
        addProductRow();
    });

    // Product search with debounce and Enter key
    let searchTimeout;
    $('#product_search').on('keyup', function(e) {
        const search = $(this).val();
        const warehouseId = $('#warehouse_id').val();

        // Clear previous timeout
        clearTimeout(searchTimeout);

        // Search only on Enter key press
        if (e.keyCode === 13) { // Enter key
            e.preventDefault();
            if (search.length >= 1 && warehouseId) {
                searchProducts(search, warehouseId);
            }
        }
        // Or search with delay for auto-complete
        else if (search.length >= 3 && warehouseId) {
            searchTimeout = setTimeout(function() {
                searchProducts(search, warehouseId);
            }, 500); // 500ms delay
        }
    });

    // Calculate totals when inputs change
    $(document).on('input', '.quantity-input, .unit-cost-input', function() {
        calculateRowTotal($(this).closest('tr'));
        calculateGrandTotal();
    });

    // Remove product row
    $(document).on('click', '.remove-product', function() {
        $(this).closest('tr').remove();
        calculateGrandTotal();
    });

    // Return checkbox handler
    $(document).on('change', '.return-checkbox', function() {
        const row = $(this).closest('tr');
        if ($(this).is(':checked')) {
            row.addClass('table-warning');
        } else {
            row.removeClass('table-warning');
        }
    });

    function addProductRow(product = null) {
        productCounter++;
        const warehouseId = $('#warehouse_id').val();
        
        let productOptions = '<option value="">اختر المنتج</option>';
        if (product) {
            productOptions += `<option value="${product.id}" selected>${product.sku} - ${product.name}</option>`;
        }

        const row = `
            <tr id="product_row_${productCounter}">
                <td>
                    <select name="products[${productCounter}][product_id]" class="form-select product-select" data-row="${productCounter}" required>
                        ${productOptions}
                    </select>
                    <small class="text-muted product-sku">${product ? product.sku : ''}</small>
                </td>
                <td>
                    <input type="number" name="products[${productCounter}][quantity]"
                           class="form-control quantity-input" min="0.01" step="0.01" required />
                </td>
                <td>
                    <input type="number" name="products[${productCounter}][unit_cost]"
                           class="form-control unit-cost-input" min="0" step="0.01"
                           value="${product ? product.purchase_price : ''}" />
                </td>
                <td>
                    <input type="date" name="products[${productCounter}][expiry_date]"
                           class="form-control expiry-date"
                           value="${product && product.expiry_date ? product.expiry_date : ''}" />
                </td>
                <td class="text-center">
                    <input type="checkbox" name="products[${productCounter}][is_return]"
                           class="form-check-input return-checkbox" value="1" />
                </td>
                <td>
                    <span class="row-total">0.00</span>
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-danger remove-product">
                        <i class="ti ti-trash"></i>
                    </button>
                </td>
            </tr>
        `;
        
        $('#products_table tbody').append(row);
        
        // Initialize product select if no product provided
        if (!product) {
            loadProductsForSelect(productCounter, warehouseId);
        }
    }

    function loadProductsForSelect(rowId, warehouseId) {
        if (!warehouseId) {
            console.log('لا يوجد مستودع محدد');
            return;
        }

        const select = $(`select[data-row="${rowId}"]`);
        select.empty().append('<option value="">جاري التحميل...</option>');

        $.ajax({
            url: '{{ route("receipt.order.warehouse.products") }}',
            method: 'GET',
            data: { warehouse_id: warehouseId },
            success: function(response) {
                console.log('استجابة المنتجات:', response);

                select.empty().append('<option value="">اختر المنتج</option>');

                if (response.success && response.products && response.products.length > 0) {
                    response.products.forEach(function(product) {
                        select.append(`<option value="${product.id}"
                                      data-name="${product.name}"
                                      data-price="${product.purchase_price}"
                                      data-sku="${product.sku}">
                                      ${product.sku} - ${product.name}
                                      </option>`);
                    });
                } else {
                    select.append('<option value="">لا توجد منتجات متاحة</option>');
                }
            },
            error: function(xhr, status, error) {
                console.error('خطأ في تحميل المنتجات:', error);
                select.empty().append('<option value="">خطأ في التحميل</option>');
            }
        });
    }

    function searchProducts(search, warehouseId) {
        $.ajax({
            url: '{{ route("receipt.order.search.products") }}',
            method: 'GET',
            data: {
                search: search,
                warehouse_id: warehouseId
            },
            success: function(response) {
                console.log('نتائج البحث:', response);

                if (response.success && response.products && response.products.length > 0) {
                    const product = response.products[0];

                    // Check if product already exists in table
                    if (isProductAlreadyAdded(product.id)) {
                        alert('هذا المنتج موجود بالفعل في الجدول');
                        $('#product_search').val('');
                        return;
                    }

                    addProductRow(product);
                    $('#product_search').val('');
                } else {
                    console.log('لم يتم العثور على منتجات');
                    alert('لم يتم العثور على منتج بهذا الرقم أو الاسم');
                }
            },
            error: function(xhr, status, error) {
                console.error('خطأ في البحث:', error);
                alert('حدث خطأ في البحث');
            }
        });
    }

    function isProductAlreadyAdded(productId) {
        let exists = false;
        $('.product-select').each(function() {
            if ($(this).val() == productId) {
                exists = true;
                return false; // break the loop
            }
        });
        return exists;
    }

    function calculateRowTotal(row) {
        const quantity = parseFloat(row.find('.quantity-input').val()) || 0;
        const unitCost = parseFloat(row.find('.unit-cost-input').val()) || 0;
        const total = quantity * unitCost;

        row.find('.row-total').text(total.toFixed(2));
    }

    function calculateGrandTotal() {
        totalProducts = $('#products_table tbody tr').length;
        totalAmount = 0;
        totalQuantity = 0;

        $('#products_table tbody tr').each(function() {
            const rowTotal = parseFloat($(this).find('.row-total').text()) || 0;
            const quantity = parseFloat($(this).find('.quantity-input').val()) || 0;

            totalAmount += rowTotal;
            totalQuantity += quantity;
        });

        // تكلفة الوحدة = إجمالي المبلغ ÷ إجمالي الكمية
        const unitCost = totalQuantity > 0 ? (totalAmount / totalQuantity) : 0;

        $('#total_products').text(totalProducts);
        $('#total_quantity').text(totalQuantity.toFixed(2));
        $('#unit_cost').text(unitCost.toFixed(2));
        $('#total_amount').text(totalAmount.toFixed(2));
    }

    function clearProductsTable() {
        $('#products_table tbody').empty();
        productCounter = 0;
        calculateGrandTotal();
    }

    function loadWarehouseProducts() {
        const warehouseId = $('#warehouse_id').val();
        if (warehouseId) {
            // Load products for existing selects
            $('.product-select').each(function() {
                const rowId = $(this).data('row');
                loadProductsForSelect(rowId, warehouseId);
            });
        }
    }

    // Product select change handler
    $(document).on('change', '.product-select', function() {
        const selectedOption = $(this).find('option:selected');
        const selectedProductId = $(this).val();
        const currentRow = $(this).closest('tr');
        const currentRowId = currentRow.attr('id');

        // Check if this product is already selected in another row
        let isDuplicate = false;
        $('.product-select').each(function() {
            const thisRow = $(this).closest('tr');
            const thisRowId = thisRow.attr('id');

            if (thisRowId !== currentRowId && $(this).val() == selectedProductId && selectedProductId !== '') {
                isDuplicate = true;
                return false; // break the loop
            }
        });

        if (isDuplicate) {
            alert('هذا المنتج محدد بالفعل في صف آخر');
            $(this).val(''); // Reset selection
            currentRow.find('.product-sku').text('');
            currentRow.find('.unit-cost-input').val('');
            return;
        }

        // Update SKU display
        currentRow.find('.product-sku').text(selectedOption.data('sku') || '');

        // Update unit cost
        currentRow.find('.unit-cost-input').val(selectedOption.data('price') || '');

        calculateRowTotal(currentRow);
        calculateGrandTotal();
    });



    // Don't initialize with product row - wait for warehouse selection

    // Form submission handler for debugging
    $('form').on('submit', function(e) {
        console.log('🚀 Form submission started...');

        // Check required fields
        const orderType = $('#order_type').val();
        const warehouseId = $('#warehouse_id').val();
        const productsCount = $('#products_table tbody tr').length;

        console.log('📋 Form data check:');
        console.log('- Order Type:', orderType);
        console.log('- Warehouse ID:', warehouseId);
        console.log('- Products Count:', productsCount);

        // Validate basic requirements
        if (!orderType) {
            alert('يجب اختيار نوع الأمر');
            e.preventDefault();
            return false;
        }

        if (!warehouseId) {
            alert('يجب اختيار المستودع');
            e.preventDefault();
            return false;
        }

        if (productsCount === 0) {
            alert('يجب إضافة منتج واحد على الأقل');
            e.preventDefault();
            return false;
        }

        // Check each product row
        let hasValidProducts = true;
        $('#products_table tbody tr').each(function(index) {
            const productId = $(this).find('.product-select').val();
            const quantity = $(this).find('.quantity-input').val();

            console.log(`- Product ${index + 1}: ID=${productId}, Quantity=${quantity}`);

            if (!productId || !quantity || quantity <= 0) {
                hasValidProducts = false;
                return false;
            }
        });

        if (!hasValidProducts) {
            alert('يجب اختيار منتج وإدخال كمية صحيحة لجميع الصفوف');
            e.preventDefault();
            return false;
        }

        // Additional validation for exit orders
        if (orderType === 'أمر إخراج') {
            const exitReason = $('#exit_reason').val();
            if (!exitReason) {
                alert('يجب اختيار حالة الإخراج');
                e.preventDefault();
                return false;
            }
            console.log('- Exit Reason:', exitReason);
        }

        // Additional validation for transfer orders
        if (orderType === 'نقل بضاعة') {
            const fromWarehouseId = $('#from_warehouse_id').val();
            if (!fromWarehouseId) {
                alert('يجب اختيار المستودع المصدر');
                e.preventDefault();
                return false;
            }
            console.log('- From Warehouse ID:', fromWarehouseId);
        }



        console.log('✅ All validations passed, submitting form...');
        return true;
    });
});
</script>
@endpush

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ route('receipt-order.index') }}">{{ __('أوامر الاستلام') }}</a></li>
    <li class="breadcrumb-item">{{ __('إنشاء أمر استلام') }}</li>
@endsection

@section('content')
<form action="{{ route('receipt-order.store') }}" method="POST">
    @csrf
    
    <!-- القسم الأول: البيانات الأساسية -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('البيانات الأساسية') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="order_type">{{ __('نوع الأمر') }} <span class="text-danger">*</span></label>
                                <select name="order_type" id="order_type" class="form-select" required>
                                    <option value="">{{ __('اختر نوع الأمر') }}</option>
                                    <option value="استلام بضاعة">{{ __('استلام بضاعة') }}</option>
                                    <option value="نقل بضاعة">{{ __('نقل بضاعة') }}</option>
                                    <option value="أمر إخراج">{{ __('أمر إخراج') }}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="warehouse_id">{{ __('المستودع') }} <span class="text-danger">*</span></label>
                                <select name="warehouse_id" id="warehouse_id" class="form-select" required>
                                    <option value="">{{ __('اختر المستودع') }}</option>
                                    @foreach($warehouses as $warehouse)
                                        <option value="{{ $warehouse->id }}">{{ $warehouse->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- حقول استلام البضاعة -->
                    <div id="receipt_fields" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="vendor_id">{{ __('اسم المورد') }}</label>
                                    <select name="vendor_id" id="vendor_id" class="form-select">
                                        <option value="">{{ __('اختر المورد') }}</option>
                                        @foreach($vendors as $vendor)
                                            <option value="{{ $vendor->id }}">{{ $vendor->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="invoice_number">{{ __('رقم الفاتورة') }}</label>
                                    <input type="text" name="invoice_number" id="invoice_number" class="form-control">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="invoice_total">{{ __('إجمالي مبلغ الفاتورة') }}</label>
                                    <input type="number" name="invoice_total" id="invoice_total" class="form-control" step="0.01">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="invoice_date">{{ __('تاريخ الفاتورة') }}</label>
                                    <input type="date" name="invoice_date" id="invoice_date" class="form-control" value="{{ date('Y-m-d') }}">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="has_return">{{ __('هل يوجد مرتجع؟') }}</label>
                                    <select name="has_return" id="has_return" class="form-select">
                                        <option value="0">{{ __('لا') }}</option>
                                        <option value="1">{{ __('نعم') }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- حقول نقل البضاعة -->
                    <div id="transfer_fields" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="from_warehouse_id">{{ __('من المستودع') }}</label>
                                    <select name="from_warehouse_id" id="from_warehouse_id" class="form-select">
                                        <option value="">{{ __('اختر المستودع المصدر') }}</option>
                                        @foreach($warehouses as $warehouse)
                                            <option value="{{ $warehouse->id }}">{{ $warehouse->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- حقول أمر الإخراج -->
                    <div id="exit_fields" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="exit_reason">{{ __('حالة الإخراج') }} <span class="text-danger">*</span></label>
                                    <select name="exit_reason" id="exit_reason" class="form-select">
                                        <option value="">{{ __('اختر حالة الإخراج') }}</option>
                                        <option value="فقدان">{{ __('فقدان') }}</option>
                                        <option value="منتهي الصلاحية">{{ __('منتهي الصلاحية') }}</option>
                                        <option value="تلف/خراب">{{ __('تلف/خراب') }}</option>
                                        <option value="بيع بالتجزئة">{{ __('بيع بالتجزئة') }}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="exit_notes">{{ __('ملاحظات الإخراج') }}</label>
                                    <textarea name="exit_notes" id="exit_notes" class="form-control" rows="3"
                                              placeholder="{{ __('اكتب ملاحظات إضافية حول سبب الإخراج...') }}"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="exit_date">{{ __('تاريخ الإخراج') }}</label>
                                    <input type="date" name="exit_date" id="exit_date" class="form-control" value="{{ date('Y-m-d') }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="responsible_person">{{ __('الشخص المسؤول') }}</label>
                                    <input type="text" name="responsible_person" id="responsible_person" class="form-control"
                                           placeholder="{{ __('اسم الشخص المسؤول عن الإخراج') }}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- القسم الثاني: المنتجات -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5>{{ __('المنتجات') }}</h5>
                    <div>
                        <input type="text" id="product_search" class="form-control d-inline-block"
                               style="width: 300px;" placeholder="{{ __('اكتب الباركود واضغط Enter أو اكتب 3+ أحرف من اسم المنتج') }}">
                        <button type="button" id="add_product_btn" class="btn btn-primary">
                            <i class="ti ti-plus"></i> {{ __('إضافة منتج') }}
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="products_table">
                            <thead>
                                <tr>
                                    <th>{{ __('اسم المنتج') }}</th>
                                    <th>{{ __('الكمية') }}</th>
                                    <th>{{ __('تكلفة الوحدة') }}</th>
                                    <th>{{ __('تاريخ الصلاحية') }}</th>
                                    <th>{{ __('مرتجع') }}</th>
                                    <th>{{ __('الإجمالي') }}</th>
                                    <th>{{ __('الإجراءات') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Product rows will be added here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- القسم الثالث: الملخص -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('ملخص الأمر') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>{{ __('عدد المنتجات') }}</label>
                                <div class="form-control-plaintext">
                                    <span id="total_products">0</span> {{ __('منتج') }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>{{ __('إجمالي الكمية') }}</label>
                                <div class="form-control-plaintext">
                                    <span id="total_quantity">0</span> {{ __('وحدة') }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>{{ __('سعر الوحدة الواحدة') }}</label>
                                <div class="form-control-plaintext">
                                    <span id="unit_cost">0.00</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>{{ __('إجمالي المبلغ') }}</label>
                                <div class="form-control-plaintext">
                                    <strong><span id="total_amount">0.00</span></strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار الحفظ -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body text-end">
                    <a href="{{ route('receipt-order.index') }}" class="btn btn-secondary">
                        {{ __('إلغاء') }}
                    </a>
                    <button type="submit" class="btn btn-primary">
                        {{ __('حفظ أمر الاستلام') }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>
@endsection
