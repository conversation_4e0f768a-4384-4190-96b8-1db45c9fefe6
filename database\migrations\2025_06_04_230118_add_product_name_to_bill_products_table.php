<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bill_products', function (Blueprint $table) {
            $table->string('product_name')->nullable()->after('product_id')->comment('اسم المنتج المكتوب يدوياً');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bill_products', function (Blueprint $table) {
            $table->dropColumn('product_name');
        });
    }
};
