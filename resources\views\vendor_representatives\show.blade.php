@extends('layouts.admin')

@section('page-title')
    {{ __('تفاصيل المندوب') }} - {{ $representative->name }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('الرئيسية') }}</a></li>
    <li class="breadcrumb-item">{{ __('إدارة عمليات الفروع') }}</li>
    <li class="breadcrumb-item"><a href="{{ route('vendor.representatives.index') }}">{{ __('تسجيل المندوبين والمردين') }}</a></li>
    <li class="breadcrumb-item">{{ __('تفاصيل المندوب') }}</li>
@endsection

@section('action-btn')
    <div class="float-end">
        @if(Auth::user()->type == 'company')
            <a href="{{ route('vendor.representatives.edit', $representative->id) }}" class="btn btn-sm btn-primary" data-ajax-popup="true" data-title="{{ __('تعديل المندوب') }}" data-bs-toggle="tooltip" title="{{ __('تعديل') }}">
                <i class="ti ti-pencil"></i>
            </a>
        @endif
        <a href="{{ route('vendor.representatives.index') }}" class="btn btn-sm btn-secondary" data-bs-toggle="tooltip" title="{{ __('العودة للقائمة') }}">
            <i class="ti ti-arrow-left"></i>
        </a>
    </div>
@endsection

@section('content')
    <div class="row">
        <!-- Representative Details Card -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('معلومات المندوب') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-6 col-md-6">
                            <div class="detail-group">
                                <label class="form-label">{{ __('اسم المندوب') }}</label>
                                <div class="detail-value">
                                    <div class="d-flex align-items-center">
                                        <div class="theme-avtar bg-primary me-2">
                                            <i class="ti ti-user"></i>
                                        </div>
                                        <span class="fw-bold">{{ $representative->name }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6 col-md-6">
                            <div class="detail-group">
                                <label class="form-label">{{ __('رقم الهاتف') }}</label>
                                <div class="detail-value">
                                    <span class="text-primary">
                                        <i class="ti ti-phone me-1"></i>{{ $representative->phone }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6 col-md-6">
                            <div class="detail-group">
                                <label class="form-label">{{ __('الشركة الموردة') }}</label>
                                <div class="detail-value">
                                    @if($representative->vendor)
                                        <span class="badge bg-info-light text-info">{{ $representative->vendor->name }}</span>
                                        @if($representative->vendor->contact)
                                            <br><small class="text-muted">{{ __('هاتف الشركة') }}: {{ $representative->vendor->contact }}</small>
                                        @endif
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6 col-md-6">
                            <div class="detail-group">
                                <label class="form-label">{{ __('فئة المنتجات') }}</label>
                                <div class="detail-value">
                                    @if($representative->category)
                                        <span class="badge bg-warning-light text-warning">{{ $representative->category->name }}</span>
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6 col-md-6">
                            <div class="detail-group">
                                <label class="form-label">{{ __('الحالة') }}</label>
                                <div class="detail-value">
                                    @if($representative->is_active)
                                        <span class="badge bg-success">{{ __('نشط') }}</span>
                                    @else
                                        <span class="badge bg-danger">{{ __('غير نشط') }}</span>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6 col-md-6">
                            <div class="detail-group">
                                <label class="form-label">{{ __('تاريخ التسجيل') }}</label>
                                <div class="detail-value">
                                    <span class="text-muted">{{ $representative->created_at->format('Y-m-d H:i') }}</span>
                                </div>
                            </div>
                        </div>

                        @if($representative->notes)
                            <div class="col-lg-12">
                                <div class="detail-group">
                                    <label class="form-label">{{ __('الملاحظات') }}</label>
                                    <div class="detail-value">
                                        <div class="alert alert-info">
                                            {{ $representative->notes }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions Card -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('الإجراءات') }}</h5>
                </div>
                <div class="card-body">
                    @if(Auth::user()->type == 'company')
                        <div class="d-grid gap-2">
                            <a href="{{ route('vendor.representatives.edit', $representative->id) }}" class="btn btn-primary" data-ajax-popup="true" data-title="{{ __('تعديل المندوب') }}">
                                <i class="ti ti-pencil me-1"></i>{{ __('تعديل المندوب') }}
                            </a>

                            <button type="button" class="btn btn-warning toggle-status" data-id="{{ $representative->id }}">
                                <i class="ti ti-toggle-{{ $representative->is_active ? 'right' : 'left' }} me-1"></i>
                                {{ $representative->is_active ? __('إلغاء التفعيل') : __('تفعيل') }}
                            </button>

                            <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                                <i class="ti ti-trash me-1"></i>{{ __('حذف المندوب') }}
                            </button>

                            {!! Form::open(['method' => 'DELETE', 'route' => ['vendor.representatives.destroy', $representative->id], 'id' => 'delete-form', 'style' => 'display:none']) !!}
                            {!! Form::close() !!}
                        </div>
                    @endif

                    <hr>

                    <div class="d-grid">
                        <a href="{{ route('vendor.representatives.index') }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left me-1"></i>{{ __('العودة للقائمة') }}
                        </a>
                    </div>
                </div>
            </div>

            <!-- Creator Info Card -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5>{{ __('معلومات إضافية') }}</h5>
                </div>
                <div class="card-body">
                    <div class="detail-group">
                        <label class="form-label">{{ __('منشئ السجل') }}</label>
                        <div class="detail-value">
                            @if($representative->creator)
                                <span class="text-muted">{{ $representative->creator->name }}</span>
                            @else
                                <span class="text-muted">-</span>
                            @endif
                        </div>
                    </div>

                    <div class="detail-group">
                        <label class="form-label">{{ __('آخر تحديث') }}</label>
                        <div class="detail-value">
                            <span class="text-muted">{{ $representative->updated_at->format('Y-m-d H:i') }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
<script>
    // Toggle Status
    $('.toggle-status').on('click', function(e) {
        e.preventDefault();
        var id = $(this).data('id');
        
        $.ajax({
            url: '{{ route("vendor.representatives.toggle.status", ":id") }}'.replace(':id', id),
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    show_toastr('Success', response.message, 'success');
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                }
            },
            error: function(xhr) {
                show_toastr('Error', 'حدث خطأ أثناء تغيير الحالة', 'error');
            }
        });
    });

    // Confirm Delete
    function confirmDelete() {
        if (confirm('{{ __("هل أنت متأكد من حذف هذا المندوب؟") }}')) {
            document.getElementById('delete-form').submit();
        }
    }
</script>

<style>
    .detail-group {
        margin-bottom: 1rem;
    }
    
    .detail-group label {
        font-weight: 600;
        color: #6c757d;
        margin-bottom: 0.25rem;
    }
    
    .detail-value {
        padding: 0.5rem 0;
    }
</style>
@endpush
