@extends('layouts.admin')

@section('page-title')
    {{ __('توزيع الحسابات') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item">{{ __('توزيع الحسابات') }}</li>
@endsection

@section('action-btn')
    <div class="float-end">
        <button class="btn btn-sm btn-primary" onclick="refreshTable()">
            <i class="ti ti-refresh"></i> {{ __('تحديث') }}
        </button>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('توزيع الحسابات للمنتجات والخدمات') }}</h5>
                    <small class="text-muted">{{ __('يمكنك تعديل حسابات الدخل والصرف للمنتجات مباشرة من هذه الصفحة') }}</small>
                </div>
                <div class="card-body table-border-style">
                    <div class="table-responsive">
                        <table class="table datatable" id="accountDistributionTable">
                            <thead>
                                <tr>
                                    <th>{{ __('الاسم') }}</th>
                                    <th>{{ __('SKU') }}</th>
                                    <th>{{ __('سعر البيع') }}</th>
                                    <th>{{ __('سعر الشراء') }}</th>
                                    <th>{{ __('حساب الدخل') }}</th>
                                    <th>{{ __('حساب الصرف') }}</th>
                                    <th>{{ __('الفئة') }}</th>
                                    <th>{{ __('النوع') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($products as $product)
                                    <tr data-product-id="{{ $product->id }}">
                                        <td>
                                            <strong>{{ $product->name }}</strong>
                                        </td>
                                        <td>
                                            <span class="badge badge-secondary">{{ $product->sku }}</span>
                                        </td>
                                        <td>
                                            <span class="text-success fw-bold">
                                                {{ Auth::user()->priceFormat($product->sale_price) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="text-warning fw-bold">
                                                {{ Auth::user()->priceFormat($product->purchase_price) }}
                                            </span>
                                        </td>
                                        <td>
                                            <select class="form-control account-select"
                                                    data-field="sale_chartaccount_id"
                                                    data-product-id="{{ $product->id }}"
                                                    data-current-value="{{ $product->sale_chartaccount_id }}">
                                                <option value="">{{ __('اختر حساب الدخل') }}</option>
                                                @foreach ($chartAccounts as $account)
                                                    <option value="{{ $account->id }}"
                                                            {{ $product->sale_chartaccount_id == $account->id ? 'selected' : '' }}>
                                                        {{ $account->name }} ({{ $account->code }})
                                                    </option>
                                                @endforeach
                                            </select>
                                            @if($product->saleAccount)
                                                <small class="text-muted d-block mt-1">
                                                    {{ __('الحالي') }}: {{ $product->saleAccount->name }}
                                                </small>
                                            @endif
                                        </td>
                                        <td>
                                            <select class="form-control account-select"
                                                    data-field="expense_chartaccount_id"
                                                    data-product-id="{{ $product->id }}"
                                                    data-current-value="{{ $product->expense_chartaccount_id }}">
                                                <option value="">{{ __('اختر حساب الصرف') }}</option>
                                                @foreach ($chartAccounts as $account)
                                                    <option value="{{ $account->id }}"
                                                            {{ $product->expense_chartaccount_id == $account->id ? 'selected' : '' }}>
                                                        {{ $account->name }} ({{ $account->code }})
                                                    </option>
                                                @endforeach
                                            </select>
                                            @if($product->expenseAccount)
                                                <small class="text-muted d-block mt-1">
                                                    {{ __('الحالي') }}: {{ $product->expenseAccount->name }}
                                                </small>
                                            @endif
                                        </td>
                                        <td>
                                            <select class="form-control account-select"
                                                    data-field="category_id"
                                                    data-product-id="{{ $product->id }}"
                                                    data-current-value="{{ $product->category_id }}">
                                                <option value="">{{ __('اختر الفئة') }}</option>
                                                @foreach ($categories as $category)
                                                    <option value="{{ $category->id }}"
                                                            {{ $product->category_id == $category->id ? 'selected' : '' }}>
                                                        {{ $category->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @if($product->category)
                                                <small class="text-muted d-block mt-1">
                                                    {{ __('الحالي') }}: {{ $product->category->name }}
                                                </small>
                                            @endif
                                        </td>
                                        <td>
                                            <select class="form-control account-select"
                                                    data-field="type"
                                                    data-product-id="{{ $product->id }}"
                                                    data-current-value="{{ $product->type }}">
                                                <option value="product" {{ $product->type == 'product' ? 'selected' : '' }}>
                                                    {{ __('منتج') }}
                                                </option>
                                                <option value="service" {{ $product->type == 'service' ? 'selected' : '' }}>
                                                    {{ __('خدمة') }}
                                                </option>
                                            </select>
                                            <small class="text-muted d-block mt-1">
                                                {{ __('الحالي') }}:
                                                <span class="badge {{ $product->type == 'product' ? 'badge-primary' : 'badge-success' }}">
                                                    {{ $product->type == 'product' ? __('منتج') : __('خدمة') }}
                                                </span>
                                            </small>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Modal -->
    <div class="modal fade" id="loadingModal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">{{ __('جاري التحديث...') }}</span>
                    </div>
                    <p class="mt-2">{{ __('جاري حفظ التغييرات...') }}</p>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#accountDistributionTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json"
        },
        "pageLength": 25,
        "order": [[0, "asc"]],
        "columnDefs": [
            { "orderable": false, "targets": [4, 5, 6, 7] } // Disable sorting for select columns
        ]
    });

    // Handle account selection change
    $('.account-select').on('change', function() {
        const $select = $(this);
        const productId = $select.data('product-id');
        const field = $select.data('field');
        const value = $select.val();
        const currentValue = $select.data('current-value');

        // If no change, return
        if (value == currentValue) {
            return;
        }

        // Show loading
        $('#loadingModal').modal('show');

        // Make AJAX request
        $.ajax({
            url: '{{ route("account.distribution.update") }}',
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                id: productId,
                field: field,
                value: value
            },
            success: function(response) {
                $('#loadingModal').modal('hide');
                
                if (response.success) {
                    // Update the current value
                    $select.data('current-value', value);

                    // Update display based on field type
                    if (field === 'category_id' && response.category_name) {
                        const $currentDisplay = $select.siblings('small').find('span');
                        if ($currentDisplay.length === 0) {
                            $select.siblings('small').html('{{ __("الحالي") }}: ' + response.category_name);
                        } else {
                            $currentDisplay.text(response.category_name);
                        }
                    } else if (field === 'type' && response.type_name) {
                        const $currentDisplay = $select.siblings('small').find('span');
                        if ($currentDisplay.length > 0) {
                            $currentDisplay.text(response.type_name);
                            // Update badge color
                            $currentDisplay.removeClass('badge-primary badge-success');
                            $currentDisplay.addClass(value === 'product' ? 'badge-primary' : 'badge-success');
                        }
                    } else if ((field === 'sale_chartaccount_id' || field === 'expense_chartaccount_id') && response.account_name) {
                        const $currentDisplay = $select.siblings('small');
                        if ($currentDisplay.length > 0) {
                            $currentDisplay.html('{{ __("الحالي") }}: ' + response.account_name);
                        }
                    }

                    // Show success message
                    show_toastr('{{ __("نجح") }}', response.message, 'success');
                } else {
                    // Revert selection
                    $select.val(currentValue);
                    show_toastr('{{ __("خطأ") }}', response.message, 'error');
                }
            },
            error: function(xhr) {
                $('#loadingModal').modal('hide');
                
                // Revert selection
                $select.val(currentValue);
                
                let errorMessage = '{{ __("حدث خطأ أثناء التحديث") }}';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                
                show_toastr('{{ __("خطأ") }}', errorMessage, 'error');
            }
        });
    });
});

function refreshTable() {
    location.reload();
}
</script>
@endpush
