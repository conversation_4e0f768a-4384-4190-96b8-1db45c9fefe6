# ملخص مصادر البيانات - قسم المبيعات في وحدة المراقبة

## 📊 **ملخص النقاط الرئيسية**

### **🎯 الهدف من قسم المبيعات**
متابعة وتحليل عمليات البيع بشكل شامل مع عرض تفاصيل العملاء والمستخدمين والمستودعات

---

## 🗄️ **الجداول المستخدمة ومصادر البيانات**

### **1️⃣ الجداول الرئيسية:**

#### **📋 جدول `pos`**
- **الغرض**: الجدول الرئيسي لفواتير المبيعات
- **البيانات المستخدمة**:
  - `id` - معرف الفاتورة
  - `pos_date` - تاريخ البيع
  - `customer_id` - معرف العميل
  - `warehouse_id` - معرف المستودع
  - `created_by` - المستخدم الذي أنشأ الفاتورة
- **الاستخدام**: حساب إجمالي المبيعات، ربط العملاء، تصنيف حسب المستودعات

#### **💰 جدول `pos_payments`**
- **الغرض**: مدفوعات فواتير المبيعات
- **البيانات المستخدمة**:
  - `pos_id` - معرف الفاتورة المرتبطة
  - `amount` - مبلغ الدفع
  - `payment_type` - نوع الدفع (نقد/شبكة)
- **الاستخدام**: حساب إجمالي المبالغ، متوسط البيع، تصنيف المدفوعات

#### **📦 جدول `pos_products`**
- **الغرض**: المنتجات المباعة في كل فاتورة
- **البيانات المستخدمة**:
  - `pos_id` - معرف الفاتورة
  - `product_id` - معرف المنتج
  - `quantity` - الكمية المباعة
  - `total` - إجمالي المبلغ للمنتج
- **الاستخدام**: تحليل المنتجات الأكثر مبيعاً، حساب الكميات

#### **👥 جدول `customers`**
- **الغرض**: بيانات العملاء
- **البيانات المستخدمة**:
  - `id` - معرف العميل
  - `name` - اسم العميل
  - `email` - البريد الإلكتروني
  - `contact` - رقم الهاتف
- **الاستخدام**: عرض أسماء العملاء، تفاصيل الاتصال

#### **👤 جدول `users`**
- **الغرض**: بيانات المستخدمين
- **البيانات المستخدمة**:
  - `id` - معرف المستخدم
  - `name` - اسم المستخدم
  - `created_by` - المنشئ (للتحقق من الصلاحيات)
- **الاستخدام**: تحديد أعلى المستخدمين إصداراً للفواتير

#### **🏪 جدول `warehouses`**
- **الغرض**: بيانات المستودعات
- **البيانات المستخدمة**:
  - `id` - معرف المستودع
  - `name` - اسم المستودع
- **الاستخدام**: تصنيف المبيعات حسب المستودعات

#### **🛍️ جدول `product_services`**
- **الغرض**: بيانات المنتجات والخدمات
- **البيانات المستخدمة**:
  - `id` - معرف المنتج
  - `name` - اسم المنتج
  - `sku` - رمز المنتج
- **الاستخدام**: عرض تفاصيل المنتجات الأكثر مبيعاً

---

## 📈 **البيانات المعروضة ومصادرها**

### **🔢 الإحصائيات الرئيسية:**

#### **1. إجمالي المبيعات**
- **المصدر**: `COUNT(pos.id)`
- **الجداول**: `pos`
- **الوصف**: عدد الفواتير الإجمالي

#### **2. إجمالي المبلغ**
- **المصدر**: `SUM(pos_payments.amount)`
- **الجداول**: `pos` + `pos_payments`
- **الوصف**: مجموع جميع المدفوعات

#### **3. متوسط البيع**
- **المصدر**: `إجمالي المبلغ ÷ إجمالي المبيعات`
- **الجداول**: `pos` + `pos_payments`
- **الوصف**: متوسط قيمة الفاتورة الواحدة

#### **4. عدد العملاء**
- **المصدر**: `COUNT(DISTINCT customers.id)`
- **الجداول**: `pos` + `customers`
- **الوصف**: عدد العملاء الذين اشتروا في الفترة

---

### **👥 أعلى العملاء شراءً:**

#### **البيانات المعروضة:**
- **اسم العميل**: من `customers.name`
- **عدد الطلبات**: `COUNT(DISTINCT pos.id)`
- **إجمالي المبلغ**: `SUM(pos_payments.amount)`

#### **الجداول المستخدمة:**
```sql
pos 
JOIN customers ON pos.customer_id = customers.id
JOIN pos_payments ON pos.id = pos_payments.pos_id
```

#### **الترتيب**: حسب إجمالي المبلغ تنازلياً

---

### **📋 تفاصيل العملاء الشاملة:**

#### **البيانات المعروضة:**
- **اسم العميل**: `customers.name`
- **رقم الهاتف**: `customers.contact`
- **البريد الإلكتروني**: `customers.email`
- **عدد الطلبات**: `COUNT(DISTINCT pos.id)`
- **إجمالي الإنفاق**: `SUM(pos_payments.amount)`
- **متوسط قيمة الطلب**: `AVG(pos_payments.amount)`
- **تاريخ آخر شراء**: `MAX(pos.pos_date)`

#### **الجداول المستخدمة:**
```sql
pos 
JOIN customers ON pos.customer_id = customers.id
JOIN pos_payments ON pos.id = pos_payments.pos_id
```

---

### **👤 أعلى المستخدمين إصداراً للفواتير:**

#### **البيانات المعروضة:**
- **اسم المستخدم**: `users.name`
- **عدد الفواتير**: `COUNT(DISTINCT pos.id)`
- **إجمالي المبلغ**: `SUM(pos_payments.amount)`

#### **الجداول المستخدمة:**
```sql
pos 
JOIN users ON pos.created_by = users.id
JOIN pos_payments ON pos.id = pos_payments.pos_id
```

---

### **🏪 مبيعات المستودعات:**

#### **البيانات المعروضة:**
- **اسم المستودع**: `warehouses.name`
- **عدد المبيعات**: `COUNT(DISTINCT pos.id)`
- **إجمالي المبلغ**: `SUM(pos_payments.amount)`

#### **الجداول المستخدمة:**
```sql
pos 
JOIN warehouses ON pos.warehouse_id = warehouses.id
JOIN pos_payments ON pos.id = pos_payments.pos_id
```

---

## 🔍 **الفلاتر المطبقة**

### **1. فلتر المستودع:**
```sql
WHERE pos.warehouse_id = :warehouse_id
```

### **2. فلتر التاريخ:**
```sql
WHERE pos.pos_date BETWEEN :date_from AND :date_to
```

### **3. فلتر المنشئ:**
```sql
WHERE pos.created_by = :creator_id
```

---

## 📊 **ملخص العرض في الواجهة**

### **🎨 العناصر المرئية:**

#### **1. ملخص مصادر البيانات**
- عرض الجداول المستخدمة في مربع معلومات
- توضيح الغرض من كل جدول

#### **2. الإحصائيات السريعة**
- إجمالي المبيعات (من جدول pos)
- إجمالي المبلغ (من جدول pos_payments)
- متوسط البيع
- عدد العملاء
- مؤشر مصدر البيانات

#### **3. الجداول التفصيلية**
- أعلى العملاء مع مصدر البيانات
- تفاصيل العملاء الشاملة
- أعلى المستخدمين مع مصدر البيانات

---

## ✅ **التأكيدات**

- ✅ **جميع البيانات من جدول `pos`** وليس `pos_v2`
- ✅ **أسماء العملاء مربوطة** من جدول `customers`
- ✅ **ترتيب حسب الإجمالي** في جميع القوائم
- ✅ **عرض مصادر البيانات** بوضوح في الواجهة
- ✅ **فلاتر تعمل** على جميع الجداول المرتبطة
- ✅ **أداء محسن** مع استعلامات محسنة

---

## 🎯 **النتيجة النهائية**

قسم المبيعات في وحدة المراقبة يعرض الآن:
- **بيانات دقيقة** من الجداول الصحيحة
- **مصادر واضحة** لكل معلومة معروضة
- **ترتيب منطقي** حسب الأهمية والإجمالي
- **تفاصيل شاملة** للعملاء والمستخدمين
- **واجهة احترافية** مع توضيح مصادر البيانات
