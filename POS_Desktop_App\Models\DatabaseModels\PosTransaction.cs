using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace POS_Desktop_App.Models.DatabaseModels
{
    /// <summary>
    /// نموذج فاتورة POS V2 - يطابق جدول pos_v2 في قاعدة البيانات
    /// </summary>
    [Table("pos_v2")]
    public class PosTransaction
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("pos_id")]
        public long PosId { get; set; }

        [Column("customer_id")]
        public long CustomerId { get; set; }

        [Column("warehouse_id")]
        public long WarehouseId { get; set; }

        [Column("pos_date")]
        public DateTime? PosDate { get; set; }

        [Column("category_id")]
        public int CategoryId { get; set; }

        [Column("status")]
        public int Status { get; set; }

        [Column("status_type")]
        [StringLength(50)]
        public string StatusType { get; set; } = "normal";

        [Column("shipping_display")]
        public int ShippingDisplay { get; set; } = 1;

        [Column("created_by")]
        public long CreatedBy { get; set; }

        [Column("is_payment_set")]
        public bool IsPaymentSet { get; set; } = false;

        [Column("user_id")]
        public long? UserId { get; set; }

        [Column("shift_id")]
        public long? ShiftId { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; }

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; }

        // Navigation Properties
        public virtual Customer Customer { get; set; }
        public virtual Warehouse Warehouse { get; set; }
        public virtual User User { get; set; }
        public virtual Shift Shift { get; set; }
        public virtual User CreatedByUser { get; set; }
        public virtual PosPayment Payment { get; set; }
        public virtual ICollection<PosProduct> Products { get; set; } = new List<PosProduct>();

        // Calculated Properties
        [NotMapped]
        public decimal SubTotal => Products?.Sum(p => p.Total) ?? 0;

        [NotMapped]
        public decimal TotalTax => Products?.Sum(p => p.TaxAmount) ?? 0;

        [NotMapped]
        public decimal TotalDiscount => Products?.Sum(p => p.Discount) ?? 0;

        [NotMapped]
        public decimal GrandTotal => SubTotal + TotalTax - TotalDiscount;

        [NotMapped]
        public int TotalItems => Products?.Sum(p => p.Quantity) ?? 0;

        // Status Types Constants
        public static class StatusTypes
        {
            public const string Normal = "normal";
            public const string Returned = "returned";
            public const string Cancelled = "cancelled";
        }

        // Helper Methods
        public string GetStatusTypeDisplayName()
        {
            return StatusType switch
            {
                StatusTypes.Normal => "عادي",
                StatusTypes.Returned => "مرتجع بضاعة",
                StatusTypes.Cancelled => "ملغية",
                _ => "غير محدد"
            };
        }

        public bool IsCompleted()
        {
            return Status == 1 && IsPaymentSet;
        }

        public bool CanBeModified()
        {
            return Status == 0 || !IsPaymentSet;
        }

        public void CalculateTotals()
        {
            // This method can be called to recalculate totals after product changes
            // The actual calculation is done in the NotMapped properties
        }

        public void AddProduct(PosProduct product)
        {
            if (Products == null)
                Products = new List<PosProduct>();

            var existingProduct = Products.FirstOrDefault(p => p.ProductId == product.ProductId);
            if (existingProduct != null)
            {
                existingProduct.Quantity += product.Quantity;
                existingProduct.CalculateTotal();
            }
            else
            {
                product.PosId = this.Id;
                Products.Add(product);
            }
        }

        public void RemoveProduct(long productId)
        {
            var product = Products?.FirstOrDefault(p => p.ProductId == productId);
            if (product != null)
            {
                Products.Remove(product);
            }
        }

        public void UpdateProductQuantity(long productId, int newQuantity)
        {
            var product = Products?.FirstOrDefault(p => p.ProductId == productId);
            if (product != null)
            {
                if (newQuantity <= 0)
                {
                    RemoveProduct(productId);
                }
                else
                {
                    product.Quantity = newQuantity;
                    product.CalculateTotal();
                }
            }
        }

        public void ApplyDiscount(decimal discountAmount, bool isPercentage = false)
        {
            if (Products == null || !Products.Any()) return;

            decimal totalBeforeDiscount = Products.Sum(p => p.Price * p.Quantity);
            
            foreach (var product in Products)
            {
                decimal productTotal = product.Price * product.Quantity;
                decimal productRatio = productTotal / totalBeforeDiscount;
                
                if (isPercentage)
                {
                    product.Discount = (productTotal * discountAmount) / 100;
                }
                else
                {
                    product.Discount = discountAmount * productRatio;
                }
                
                product.CalculateTotal();
            }
        }

        public void ClearDiscount()
        {
            if (Products != null)
            {
                foreach (var product in Products)
                {
                    product.Discount = 0;
                    product.CalculateTotal();
                }
            }
        }

        // Validation
        public List<string> Validate()
        {
            var errors = new List<string>();

            if (CustomerId <= 0)
                errors.Add("يجب اختيار العميل");

            if (WarehouseId <= 0)
                errors.Add("يجب اختيار المستودع");

            if (Products == null || !Products.Any())
                errors.Add("يجب إضافة منتج واحد على الأقل");

            if (Products?.Any(p => p.Quantity <= 0) == true)
                errors.Add("جميع المنتجات يجب أن تحتوي على كمية أكبر من صفر");

            if (GrandTotal <= 0)
                errors.Add("إجمالي الفاتورة يجب أن يكون أكبر من صفر");

            return errors;
        }

        public bool IsValid()
        {
            return !Validate().Any();
        }
    }
}
