<?php

namespace App\Services;

use App\Models\FinancialRecord;
use App\Models\DeliveryFinancialRecord;
use App\Models\Pos;
use App\Models\Shift;
use Illuminate\Support\Facades\Auth;

class FinancialRecordService
{

    //handle the update in both finacial record for sales and delivery
    public function handleDeliveryBill($request)
    {
        // dd($request);
        $user = Auth::user();
        $payment_type = $request['payment_type'];
        $pos_id = $request['pos_id'];
        $total_price = $request['total_price'];

        try {
            if (!$user->can('manage delevery')) {
                throw new \Exception(__('Permission denied.'));
            }

            // Handle split payment
            if ($payment_type === 'split') {
                // Validate split payment data
                if (!isset($request['split_cash_amount']) || !isset($request['split_network_amount']) || !isset($request['split_transaction_number'])) {
                    throw new \Exception(__('Split payment requires cash amount, network amount, and transaction number'));
                }

                $cash_amount = floatval($request['split_cash_amount']);
                $network_amount = floatval($request['split_network_amount']);

                // Verify that the sum equals the total price
                $total_split = $cash_amount + $network_amount;
                if (abs($total_split - floatval($total_price)) > 0.01) {
                    throw new \Exception(__('Split payment amounts must equal the total price'));
                }

                // Update financial records for split payment
                $result = $this->updateDeliveryFinancialRecordForSplitPayment(
                    $user,
                    $cash_amount,
                    $network_amount
                );

                if (!$result['success']) {
                    throw new \Exception($result['message']);
                }

                $financialRecord = $result;
                $deliveryFinancialRecord = $result;
            } else {
                // Handle regular cash or network payment
                $financialRecord = $this->updateDeliveryCashInFinancialRecord($user, $payment_type, $total_price);

                if (!$financialRecord['success']) {
                    throw new \Exception($financialRecord['message']);
                }

                $deliveryFinancialRecord = $this->updatefinancialRecordForDelivery($user, $payment_type, $total_price);

                if (!$deliveryFinancialRecord['success']) {
                    throw new \Exception($deliveryFinancialRecord['message']);
                }
            }

            // Update POS payment status
            $pos = Pos::where('id', $pos_id)->update(['is_payment_set' => true]);

            if (!$pos) {
                throw new \Exception(__('Failed to update POS payment status'));
            }

            return [
                'success' => true,
                'data' => ['financial_record' => $financialRecord, 'pos_id' => $pos_id],
                'message' => __('Delivery has been paid successfully'),
            ];
        } catch (\Exception $e) {
            throw $e;
        }
    }

    public function openNewDeliveryFinancialRecord($user)
    {
        $warehouse = $user->warehouse_id;

        if(!$warehouse) {
            throw new \Exception(__('User has no warehouse'));
        }

        try {
            // Fetch open shift
            $openShift = Shift::whereNull('closed_at')
                ->where('warehouse_id', $warehouse)
                ->first();

            if (!$openShift) {
                return null;
            }

            $deliveryFinancialRecord = DeliveryFinancialRecord::where('shift_id', $openShift->id)->where('created_by', $user->id)->first();

            if (!$deliveryFinancialRecord) {
                // Create record using mass assignment
                $deliveryFinancialRecord = DeliveryFinancialRecord::create([
                    'shift_id' => $openShift->id,
                    'warehouse_id' => $warehouse,
                    'created_by' => Auth::user()->id,
                ]);
            }

            return $deliveryFinancialRecord;
        } catch (\Exception $e) {
            throw $e;
        }
    }

    //get the open financial record for the current user's warehouse
    public function getWarehouseOpenedFinancialRecord($user)
    {
        $warehouse = $user->warehouse_id;

        if(!$warehouse && $user->type != 'company') {
            throw new \Exception(__('User has no warehouse'));
        }

        try {

            // Fetch open shift
            $openShift = Shift::whereNull('closed_at')
                ->where('warehouse_id', $warehouse)
                ->first();

            if (!$openShift) {
                return null;
            }

            if ($user->type == 'company') {
                $financialRecord = FinancialRecord::with('shift')->first();
            }

            $financialRecord = FinancialRecord::where('shift_id', $openShift->id)->with('shift')->first();

            return $financialRecord;
        } catch (\Exception $e) {
            throw $e;
        }
    }

    //update the fiancial record for the current open casher in the warehouse
    public function updateDeliveryCashInFinancialRecord($user, $payment_type, $total_price)
    {
        $warehouse = $user->warehouse_id;

        if(!$warehouse) {
            throw new \Exception(__('User has no warehouse'));
        }

        try {

            $openShift = Shift::whereNull('closed_at')->where('warehouse_id', $warehouse)->first();
            // dd($openShift);
            if (!$openShift) {
                throw new \Exception(__('No open shift found'));
            }

            $openShiftFinancialRecord = FinancialRecord::where('shift_id', $openShift->id)->first();

            if ($payment_type === 'cash') {
                // لا نضيف المبلغ النقدي إلى عمود الكاشير (current_cash)
                // بل نضيفه فقط إلى عمود النقد لدى موظف التوصيل (delivery_cash)
                $currentCash = $openShiftFinancialRecord->current_cash; // لا نضيف المبلغ هنا
                $deliveryCash = $openShiftFinancialRecord->delivery_cash + $total_price;
                $overNetworkCash = $openShiftFinancialRecord->overnetwork_cash;
                $totalCash = $openShiftFinancialRecord->opening_balance + $currentCash + $deliveryCash;
                $deficit = $openShiftFinancialRecord->deficit + $total_price;

                $financialRecord = FinancialRecord::updateOrCreate(
                    ['id' => $openShiftFinancialRecord->id],
                    [
                        'current_cash' => $currentCash,
                        'delivery_cash' => $deliveryCash,
                        'total_cash' => $totalCash,
                        'deficit' => $deficit,
                    ]
                );
            }
            if ($payment_type === 'network') {
                $currentCash = $openShiftFinancialRecord->current_cash;
                $deliveryCash = $openShiftFinancialRecord->delivery_cash;
                $overNetworkCash = $openShiftFinancialRecord->overnetwork_cash + $total_price;
                $totalCash = $openShiftFinancialRecord->opening_balance + $currentCash + $deliveryCash ;

                $financialRecord = FinancialRecord::updateOrCreate(
                    ['id' => $openShiftFinancialRecord->id],
                    [
                        'overnetwork_cash' => $overNetworkCash,
                        'total_cash' => $totalCash,
                    ]
                );
            }

            return [
                'success' => true,
                'data' => ['financial_record' => $financialRecord],
                'message' => __('Delivery has been paid successfully'),
            ];
        } catch (\Exception $e) {
            throw $e;
        }
    }

    //update the delivery financial record for the current open casher in the warehouse
    public function updatefinancialRecordForDelivery($user, $payment_type, $total_price)
    {
        $warehouse = $user->warehouse_id;

        if(!$warehouse) {
            throw new \Exception(__('User has no warehouse'));
        }

        try {

            $openShift = Shift::whereNull('closed_at')->where('warehouse_id', $warehouse)->first();

            if (!$openShift) {
                throw new \Exception(__('No open shift found'));
            }

            //check if there is an open delivery financial record for the current delivery user
            $openShiftFinancialRecord = DeliveryFinancialRecord::where('shift_id', $openShift->id)->where('created_by', $user->id)->first();

            //create new delivery financial record if there is no open one.
            if (!$openShiftFinancialRecord) {
                $openShiftFinancialRecord =  $this->openNewDeliveryFinancialRecord($user);
            }

            if ($payment_type === 'cash') {

                $deliveryCash = $openShiftFinancialRecord->delivery_cash + $total_price;
                $overNetworkCash = $openShiftFinancialRecord->overnetwork_cash;

                $financialRecord = DeliveryFinancialRecord::updateOrCreate(
                    ['id' => $openShiftFinancialRecord->id],
                    [
                        'delivery_cash' => $deliveryCash,
                    ]
                );
            }
            if ($payment_type === 'network') {

                $deliveryCash = $openShiftFinancialRecord->delivery_cash;
                $overNetworkCash = $openShiftFinancialRecord->overnetwork_cash + $total_price;

                $financialRecord = DeliveryFinancialRecord::updateOrCreate(
                    ['id' => $openShiftFinancialRecord->id],
                    [
                        'overnetwork_cash' => $overNetworkCash,
                    ]
                );
            }

            return [
                'success' => true,
                'data' => ['financial_record' => $financialRecord],
                'message' => __('Delivery has been paid successfully'),
            ];
        } catch (\Exception $e) {
            throw $e;
        }
    }

    public function updateCurrentCashOnPaymentVoucher($user, $payment_amount, $payment_method)
    {
        $warehouse = $user->warehouse_id;

        if(!$warehouse) {
            throw new \Exception(__('User has no warehouse'));
        }

        try {

            $openShift = Shift::whereNull('closed_at')->where('warehouse_id', $warehouse)->first();

            if (!$openShift) {
                throw new \Exception(__('No open shift found'));
            }

            $openShiftFinancialRecord = FinancialRecord::where('shift_id', $openShift->id)->first();

            if ($payment_method === 'cash') {
                $currentCash = $openShiftFinancialRecord->current_cash - $payment_amount;
                $deliveryCash = $openShiftFinancialRecord->delivery_cash;
                $totalCash = $openShiftFinancialRecord->opening_balance + $currentCash + $deliveryCash;


                $financialRecord = FinancialRecord::updateOrCreate(
                    ['id' => $openShiftFinancialRecord->id],
                    [
                        'current_cash' => $currentCash,
                        'total_cash' => $totalCash,
                    ]
                );
            }
            if ($payment_method === 'bank_transfer') {
                $currentCash = $openShiftFinancialRecord->current_cash;
                $overNetworkCash = $openShiftFinancialRecord->overnetwork_cash - $payment_amount;
                $deliveryCash = $openShiftFinancialRecord->delivery_cash;
                $totalCash = $openShiftFinancialRecord->opening_balance + $currentCash + $deliveryCash;

                $financialRecord = FinancialRecord::updateOrCreate(
                    ['id' => $openShiftFinancialRecord->id],
                    [
                        'overnetwork_cash' => $overNetworkCash,
                        'total_cash' => $totalCash,
                    ]
                );
            }

            return [
                'success' => true,
                'data' => ['financial_record' => $financialRecord],
                'message' => __('Payment voucher has been updated successfully'),
            ];
        } catch (\Exception $e) {
            throw $e;
        }
    }

    public function updateCurrentCashOnReceiptVoucher($user, $payment_amount, $payment_method,$receipt_voucher)
    {
        $warehouse = $user->warehouse_id;
        // dd($receipt_voucher->receipt_from_user_id);
        if(!$warehouse) {
            throw new \Exception(__('User has no warehouse'));
        }

        try {

            $openShift = Shift::whereNull('closed_at')->where('warehouse_id', $warehouse)->first();

            if (!$openShift) {
                throw new \Exception(__('No open shift found'));
            }

            $openShiftFinancialRecord = FinancialRecord::where('shift_id', $openShift->id)->first();

            if ($payment_method === 'cash') {
                // التحقق من المستلم: هل هو نفس المنشئ أم مستخدم آخر
                $isReceiptFromSelf = ($receipt_voucher->receipt_from_user_id == $user->id);

                if ($isReceiptFromSelf) {
                    // إذا كان المستلم هو نفس المنشئ، أضف للنقد الحالي مباشرة
                    $currentCash = $openShiftFinancialRecord->current_cash + $payment_amount;
                    $deliveryCash = $openShiftFinancialRecord->delivery_cash;
                    $totalCash = $openShiftFinancialRecord->opening_balance + $currentCash + $deliveryCash;

                    $financialRecord = FinancialRecord::updateOrCreate(
                        ['id' => $openShiftFinancialRecord->id],
                        [
                            'current_cash' => $currentCash,
                            'total_cash' => $totalCash,
                        ]
                    );

                    return [
                        'success' => true,
                        'data' => ['financial_record' => $financialRecord],
                        'message' => __('Receipt voucher processed successfully - Self receipt'),
                    ];
                } else {
                    // إذا كان المستلم مستخدم آخر، أضف لسجل التوصيل الخاص به
                    $currentCash = $openShiftFinancialRecord->current_cash;
                    $deliveryCash = $openShiftFinancialRecord->delivery_cash;
                    $deliveryCash = $deliveryCash - $payment_amount;
                    $totalCash = $openShiftFinancialRecord->opening_balance + $currentCash + $deliveryCash;

                    // تحديث السجل المالي الرئيسي
                    $financialRecord = FinancialRecord::updateOrCreate(
                        ['id' => $openShiftFinancialRecord->id],
                        [
                            'delivery_cash'=>$deliveryCash,
                            'deficit'=>$deliveryCash,
                            'total_cash' => $totalCash,
                        ]
                    );

                    // تحديث أو إنشاء سجل التوصيل للمستخدم المستلم
                    $deliveryFinantialRecord = DeliveryFinancialRecord::updateOrCreate(
                        ['shift_id' => $openShift->id,'created_by'=>$receipt_voucher->receipt_from_user_id],
                        [
                            'delivery_cash'=>$payment_amount,
                        ]
                    );

                    return [
                        'success' => true,
                        'data' => ['financial_record' => $financialRecord, 'delivery_record' => $deliveryFinantialRecord],
                        'message' => __('Receipt voucher processed successfully - Other user receipt'),
                    ];
                }
            }
            if ($payment_method === 'bank_transfer') {
                $currentCash = $openShiftFinancialRecord->current_cash;
                $overNetworkCash = $openShiftFinancialRecord->overnetwork_cash ;
                $deliveryCash = $openShiftFinancialRecord->delivery_cash;
                $totalCash = $openShiftFinancialRecord->opening_balance + $currentCash + $deliveryCash;

                $financialRecord = FinancialRecord::updateOrCreate(
                    ['id' => $openShiftFinancialRecord->id],
                    [
                        'overnetwork_cash' => $overNetworkCash,
                        'total_cash' => $totalCash,
                    ]
                );
                $deliveryFinantialRecord = DeliveryFinancialRecord::updateOrCreate(
                    ['shift_id' => $openShift->id,'created_by'=>$receipt_voucher->receipt_from_user_id],
                    [
                        'overnetwork_cash'=>$overNetworkCash - $financialRecord->overnetwork_cash,
                    ]
                );
            }

            return [
                'success' => true,
                'data' => ['financial_record' => $financialRecord],
                'message' => __('Payment voucher has been updated successfully'),
            ];
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * Update financial records for split payments
     *
     * @param \App\Models\User $user The authenticated user
     * @param float $cash_amount The cash portion of the payment
     * @param float $network_amount The network/card portion of the payment
     * @return array Result of the operation
     */
    public function updateFinancialRecordForSplitPayment($user, $cash_amount, $network_amount)
    {
        $warehouse = $user->warehouse_id;

        if(!$warehouse) {
            throw new \Exception(__('User has no warehouse'));
        }

        try {
            $openShift = Shift::whereNull('closed_at')->where('warehouse_id', $warehouse)->first();

            if (!$openShift) {
                throw new \Exception(__('No open shift found'));
            }

            $openShiftFinancialRecord = FinancialRecord::where('shift_id', $openShift->id)->first();

            // Update cash portion
            $currentCash = $openShiftFinancialRecord->current_cash + $cash_amount;

            // Update network portion
            $overNetworkCash = $openShiftFinancialRecord->overnetwork_cash + $network_amount;

            // Calculate total
            $deliveryCash = $openShiftFinancialRecord->delivery_cash;
            $totalCash = $openShiftFinancialRecord->opening_balance + $currentCash + $deliveryCash;
            $deficit = $openShiftFinancialRecord->deficit;

            $financialRecord = FinancialRecord::updateOrCreate(
                ['id' => $openShiftFinancialRecord->id],
                [
                    'current_cash' => $currentCash,
                    'overnetwork_cash' => $overNetworkCash,
                    'total_cash' => $totalCash,
                    'deficit' => $deficit
                ]
            );

            return [
                'success' => true,
                'data' => ['financial_record' => $financialRecord],
                'message' => __('Split payment has been processed successfully'),
            ];
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * Update delivery financial records for split payments
     *
     * @param \App\Models\User $user The authenticated user
     * @param float $cash_amount The cash portion of the payment
     * @param float $network_amount The network/card portion of the payment
     * @return array Result of the operation
     */
    public function updateDeliveryFinancialRecordForSplitPayment($user, $cash_amount, $network_amount)
    {
        $warehouse = $user->warehouse_id;

        if(!$warehouse) {
            throw new \Exception(__('User has no warehouse'));
        }

        try {
            $openShift = Shift::whereNull('closed_at')->where('warehouse_id', $warehouse)->first();

            if (!$openShift) {
                throw new \Exception(__('No open shift found'));
            }

            $openShiftFinancialRecord = FinancialRecord::where('shift_id', $openShift->id)->first();

            // لا نضيف المبلغ النقدي إلى عمود الكاشير (current_cash)
            // بل نضيفه فقط إلى عمود النقد لدى موظف التوصيل (delivery_cash)
            $currentCash = $openShiftFinancialRecord->current_cash; // لا نضيف المبلغ النقدي هنا
            $deliveryCash = $openShiftFinancialRecord->delivery_cash + $cash_amount;

            // Update network portion in main financial record
            $overNetworkCash = $openShiftFinancialRecord->overnetwork_cash + $network_amount;

            // Calculate total
            $totalCash = $openShiftFinancialRecord->opening_balance + $currentCash + $deliveryCash;
            $deficit = $openShiftFinancialRecord->deficit + $cash_amount;

            $financialRecord = FinancialRecord::updateOrCreate(
                ['id' => $openShiftFinancialRecord->id],
                [
                    'current_cash' => $currentCash,
                    'delivery_cash' => $deliveryCash,
                    'overnetwork_cash' => $overNetworkCash,
                    'total_cash' => $totalCash,
                    'deficit' => $deficit
                ]
            );

            // Update delivery financial record
            $openShiftDeliveryFinancialRecord = DeliveryFinancialRecord::where('shift_id', $openShift->id)
                ->where('created_by', $user->id)
                ->first();

            if (!$openShiftDeliveryFinancialRecord) {
                $openShiftDeliveryFinancialRecord = $this->openNewDeliveryFinancialRecord($user);
            }

            // Update cash portion in delivery financial record
            $deliveryRecordCash = $openShiftDeliveryFinancialRecord->delivery_cash + $cash_amount;

            // Update network portion in delivery financial record
            $deliveryRecordNetworkCash = $openShiftDeliveryFinancialRecord->overnetwork_cash + $network_amount;

            $deliveryFinancialRecord = DeliveryFinancialRecord::updateOrCreate(
                ['id' => $openShiftDeliveryFinancialRecord->id],
                [
                    'delivery_cash' => $deliveryRecordCash,
                    'overnetwork_cash' => $deliveryRecordNetworkCash,
                ]
            );

            return [
                'success' => true,
                'data' => [
                    'financial_record' => $financialRecord,
                    'delivery_financial_record' => $deliveryFinancialRecord
                ],
                'message' => __('Split payment for delivery has been processed successfully'),
            ];
        } catch (\Exception $e) {
            throw $e;
        }
    }


    // هنا قمنا بالتعديل  على  مايجري بها 
    public function handlePayment($user, $payment_type, $total_price, $payment_data = [])
    {
        try {
            \DB::beginTransaction();
            
            // Handle regular cash or network payment
            if ($payment_type === 'cash' || $payment_type === 'network') {
                $result = $this->updateMainFinancialRecord($user, $payment_type, $total_price);
            }
            // Handle split payment
            else if ($payment_type === 'split') {
                $cash_amount = floatval($payment_data['split_cash_amount']);
                $network_amount = floatval($payment_data['split_network_amount']);
                
                // Verify split payment total
                if (abs(($cash_amount + $network_amount) - $total_price) > 0.01) {
                    throw new \Exception(__('Split payment amounts must equal the total price'));
                }
                
                $result = $this->updateSplitFinancialRecord($user, $cash_amount, $network_amount);
            }

            \DB::commit();
            return $result;
        } catch (\Exception $e) {
            \DB::rollback();
            throw $e;
        }
    }

    protected function updateMainFinancialRecord($user, $payment_type, $total_price)
    {
        $openShift = $this->getOpenShift($user);
        $record = FinancialRecord::where('shift_id', $openShift->id)->first();

        // نسخة احتياطية من القيم الحالية
        $current_cash = $record->current_cash;
        $delivery_cash = $record->delivery_cash;
        $overnetwork_cash = $record->overnetwork_cash;
        
        if ($payment_type === 'cash') {
            // تحديث النقد الحالي فقط
            $current_cash = $current_cash + $total_price;
        } else {
            // تحديث الدفع بالشبكة فقط
            $overnetwork_cash = $overnetwork_cash + $total_price;
        }
        
        // حساب الإجمالي دون تكرار
        $total_cash = $record->opening_balance + $current_cash + $delivery_cash;
        
        // تحديث جميع القيم
        $record->current_cash = $current_cash;
        $record->overnetwork_cash = $overnetwork_cash;
        $record->total_cash = $total_cash;
        $record->save();
        
        return [
            'success' => true,
            'data' => ['financial_record' => $record],
            'message' => __('Payment processed successfully')
        ];
    }

    protected function updateSplitFinancialRecord($user, $cash_amount, $network_amount) 
    {
        $openShift = $this->getOpenShift($user);
        $record = FinancialRecord::where('shift_id', $openShift->id)->first();
        
        // Update cash portion
        $current_cash = $record->current_cash + $cash_amount;
        
        // Update network portion 
        $network_cash = $record->overnetwork_cash + $network_amount;
        
        // Calculate new total
        $total_cash = $record->opening_balance + $current_cash + $record->delivery_cash;
        
        $record->current_cash = $current_cash;
        $record->overnetwork_cash = $network_cash;
        $record->total_cash = $total_cash;
        $record->save();
        
        return [
            'success' => true,
            'data' => ['financial_record' => $record],
            'message' => __('Split payment processed successfully')
        ];
    }

    protected function getOpenShift($user)
    {
        $warehouse = $user->warehouse_id;
        if(!$warehouse) {
            throw new \Exception(__('User has no warehouse'));
        }

        $openShift = Shift::whereNull('closed_at')
            ->where('warehouse_id', $warehouse)
            ->first();

        if (!$openShift) {
            throw new \Exception(__('No open shift found'));
        }

        return $openShift;
    }
}

/**
 * الكود القديم للمرجعية:
 * 
 * معالجة تحديث السجلات المالية للمبيعات والتوصيل
 * handleDeliveryBill - يتعامل مع فواتير التوصيل
 * - يتحقق من صلاحيات المستخدم
 * - يعالج الدفع المجزأ (نقدي + شبكة)
 * - يحدث سجلات التوصيل المالية
 * 
 * openNewDeliveryFinancialRecord - يفتح سجل مالي جديد للتوصيل
 * - يتحقق من المستودع
 * - يتحقق من الوردية المفتوحة
 * - ينشئ سجل جديد إذا لم يكن موجود
 * 
 * getWarehouseOpenedFinancialRecord - يجلب السجل المالي المفتوح للمستودع
 * 
 * updateDeliveryCashInFinancialRecord - يحدث النقد في السجل المالي
 * - يتعامل مع الدفع النقدي والشبكة
 * - يحسب المجاميع والعجز
 * 
 * updatefinancialRecordForDelivery - يحدث سجل التوصيل المالي
 * - يتعامل مع الدفع النقدي والشبكة
 * - يحدث سجلات التوصيل
 * 
 * updateCurrentCashOnPaymentVoucher - يحدث النقد عند سند الصرف
 * 
 * updateCurrentCashOnReceiptVoucher - يحدث النقد عند سند القبض
 * 
 * updateFinancialRecordForSplitPayment - يحدث السجل المالي للدفع المجزأ
 * 
 * updateDeliveryFinancialRecordForSplitPayment - يحدث سجل التوصيل للدفع المجزأ
 */
