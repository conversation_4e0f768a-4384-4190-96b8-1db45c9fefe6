@echo off
echo ========================================
echo       تشغيل نظام نقاط البيع
echo ========================================
echo.

echo التحقق من وجود .NET SDK...
dotnet --version
if %errorlevel% neq 0 (
    echo خطأ: .NET SDK غير مثبت
    echo يرجى تثبيت .NET 6.0 SDK من:
    echo https://dotnet.microsoft.com/download/dotnet/6.0
    pause
    exit /b 1
)

echo.
echo استعادة الحزم...
dotnet restore
if %errorlevel% neq 0 (
    echo خطأ في استعادة الحزم
    pause
    exit /b 1
)

echo.
echo بناء المشروع...
dotnet build --configuration Debug
if %errorlevel% neq 0 (
    echo خطأ في بناء المشروع
    pause
    exit /b 1
)

echo.
echo تشغيل التطبيق...
dotnet run
if %errorlevel% neq 0 (
    echo خطأ في تشغيل التطبيق
    pause
    exit /b 1
)

pause
