@extends('layouts.admin')
@section('page-title')
    {{ __('مراقبة المستخدمين والأموال') }}
@endsection

@push('style-page')
<style>
/* إخفاء العنصر المحدد بدقة */
body > div.dash-container > div > div.row > div:nth-child(1) > div > div.card-header.d-flex.justify-content-between.align-items-center {
    display: none !important;
}

/* إخفاء أي card-header غير مرغوب فيه في هذه الصفحة */
.card-header.d-flex.justify-content-between.align-items-center {
    display: none !important;
}


.filter-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.date-filter-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-bottom: 1rem;
}

.date-filter-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #dee2e6;
    background: white;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.date-filter-btn:hover {
    background: #e9ecef;
}

.date-filter-btn.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.table-monitoring {
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 2rem;
}

.table-monitoring thead {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.table-monitoring thead th {
    border: none;
    padding: 1rem;
    font-weight: 600;
    text-align: center;
}

.table-monitoring tbody td {
    padding: 0.75rem;
    vertical-align: middle;
    border-color: #e9ecef;
    text-align: center;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-open {
    background: #d4edda;
    color: #155724;
}

.status-closed {
    background: #f8d7da;
    color: #721c24;
}

.amount-badge {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 500;
}

.network-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 500;
}

.cash-badge {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 500;
}

.section-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 10px 10px 0 0;
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.auto-refresh-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    z-index: 1000;
    display: none;
}

.refresh-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.refresh-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    color: white;
}

.export-btn {
    background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    transition: all 0.3s ease;
    margin-left: 0.5rem;
}

.export-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    color: white;
}

.details-btn {
    background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
    border: none;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.details-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    color: white;
}

/* Avatar styles */
.avatar-sm {
    width: 32px;
    height: 32px;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
}

/* Enhanced table styles */
.table-monitoring tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.table-monitoring .badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

/* Custom scrollbar for tables */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 10px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Loading animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
    .date-filter-buttons {
        justify-content: center;
    }

    .table-monitoring {
        font-size: 0.85rem;
    }

    .avatar-sm {
        width: 24px;
        height: 24px;
    }

    .avatar-title {
        font-size: 0.7rem;
    }

    .badge {
        font-size: 0.7rem !important;
        padding: 0.25rem 0.5rem !important;
    }
}
</style>
@endpush

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item">{{ __('مراقبة المستخدمين والأموال') }}</li>
@endsection

@section('content')
<div class="auto-refresh-indicator" id="refreshIndicator">
    <i class="fas fa-sync-alt fa-spin"></i> جاري التحديث...
</div>

<!-- الإحصائيات المخصصة -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <h4 class="text-primary">{{ number_format($summaryStats['total_cash'], 2) }}</h4>
                        <p class="text-muted mb-0">إجمالي النقد</p>
                        <small class="text-success">{{ number_format($summaryStats['cash_percentage'], 1) }}%</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-info">{{ number_format($summaryStats['total_network'], 2) }}</h4>
                        <p class="text-muted mb-0">إجمالي الشبكة</p>
                        <small class="text-info">{{ number_format($summaryStats['network_percentage'], 1) }}%</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-success">{{ number_format($summaryStats['total_sales'], 2) }}</h4>
                        <p class="text-muted mb-0">إجمالي المبيعات</p>
                        <small class="text-warning">{{ $summaryStats['transactions_count'] }} معاملة</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-danger">{{ $summaryStats['open_shifts_count'] }}</h4>
                        <p class="text-muted mb-0">الشفتات المفتوحة</p>
                        <small class="text-secondary">متوسط: {{ number_format($summaryStats['avg_invoice_value'], 2) }}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر التحكم -->
<div class="filter-card">
    <form method="GET" action="{{ route('user-monitoring.index') }}" id="filterForm">
        <!-- فلترة التاريخ السريعة -->
        <div class="date-filter-buttons">
            <button type="button" class="date-filter-btn {{ $dateFilter == 'today' ? 'active' : '' }}" 
                    onclick="setDateFilter('today')">اليوم</button>
            <button type="button" class="date-filter-btn {{ $dateFilter == 'yesterday' ? 'active' : '' }}" 
                    onclick="setDateFilter('yesterday')">أمس</button>
            <button type="button" class="date-filter-btn {{ $dateFilter == 'last3days' ? 'active' : '' }}" 
                    onclick="setDateFilter('last3days')">آخر 3 أيام</button>
            <button type="button" class="date-filter-btn {{ $dateFilter == 'last7days' ? 'active' : '' }}" 
                    onclick="setDateFilter('last7days')">آخر أسبوع</button>
            <button type="button" class="date-filter-btn {{ $dateFilter == 'last30days' ? 'active' : '' }}" 
                    onclick="setDateFilter('last30days')">آخر شهر</button>
            <button type="button" class="date-filter-btn {{ $dateFilter == 'custom' ? 'active' : '' }}" 
                    onclick="setDateFilter('custom')">فترة مخصصة</button>
        </div>
        
        <input type="hidden" name="date_filter" id="dateFilterInput" value="{{ $dateFilter }}">
        
        <!-- فترة مخصصة -->
        <div id="customDateRange" style="display: {{ $dateFilter == 'custom' ? 'block' : 'none' }};">
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="start_date">من تاريخ:</label>
                    <input type="date" name="start_date" id="start_date" class="form-control" 
                           value="{{ request('start_date') }}">
                </div>
                <div class="col-md-6">
                    <label for="end_date">إلى تاريخ:</label>
                    <input type="date" name="end_date" id="end_date" class="form-control" 
                           value="{{ request('end_date') }}">
                </div>
            </div>
        </div>
        
        <!-- الفلاتر الأخرى -->
        <div class="row">
            <div class="col-md-3">
                <label for="warehouse_id">المستودع:</label>
                <select name="warehouse_id" id="warehouse_id" class="form-select">
                    <option value="">جميع المستودعات</option>
                    @foreach($warehouses as $warehouse)
                        <option value="{{ $warehouse->id }}" {{ $warehouseId == $warehouse->id ? 'selected' : '' }}>
                            {{ $warehouse->name }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-3">
                <label for="user_id">المستخدم:</label>
                <select name="user_id" id="user_id" class="form-select">
                    <option value="">جميع المستخدمين</option>
                    @foreach($users as $user)
                        <option value="{{ $user->id }}" {{ $userId == $user->id ? 'selected' : '' }}>
                            {{ $user->name }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-3">
                <label for="payment_method">طريقة الدفع:</label>
                <select name="payment_method" id="payment_method" class="form-select">
                    <option value="">جميع الطرق</option>
                    <option value="cash" {{ $paymentMethod == 'cash' ? 'selected' : '' }}>نقدي</option>
                    <option value="network" {{ $paymentMethod == 'network' ? 'selected' : '' }}>شبكة</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="shift_status">حالة الشفت:</label>
                <select name="shift_status" id="shift_status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="open" {{ $shiftStatus == 'open' ? 'selected' : '' }}>مفتوح</option>
                    <option value="closed" {{ $shiftStatus == 'closed' ? 'selected' : '' }}>مغلق</option>
                </select>
            </div>
        </div>
        
        <div class="row mt-3">
            <div class="col-md-12 text-end">
                <button type="submit" class="btn refresh-btn">
                    <i class="fas fa-search"></i> تطبيق الفلاتر
                </button>
                <button type="button" class="btn export-btn" onclick="exportData('excel')">
                    <i class="fas fa-file-excel"></i> تصدير Excel
                </button>
                <button type="button" class="btn export-btn" onclick="exportData('pdf')">
                    <i class="fas fa-file-pdf"></i> تصدير PDF
                </button>
            </div>
        </div>
    </form>
</div>

<!-- جدول مراقبة المستخدمين الرئيسي -->
<div class="card table-monitoring">
    <div class="section-header">
        <i class="fas fa-users"></i> مراقبة المستخدمين والأموال
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>اسم المستخدم</th>
                        <th>المستودع</th>
                        <th>حالة الشفت</th>
                        <th>النقد الحالي</th>
                        <th>الشبكة الحالية</th>
                        <th>إجمالي المبيعات</th>
                        <th>إجمالي الشبكة</th>
                        <th>عدد المعاملات</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($usersData as $userData)
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm me-2">
                                    <div class="avatar-title bg-primary rounded-circle">
                                        {{ substr($userData['user']->name, 0, 1) }}
                                    </div>
                                </div>
                                <div>
                                    <strong>{{ $userData['user']->name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ $userData['user']->email }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-secondary">
                                {{ $userData['warehouse'] ? $userData['warehouse']->name : 'غير محدد' }}
                            </span>
                        </td>
                        <td>
                            <span class="status-badge {{ $userData['shift_status'] == 'مفتوح' ? 'status-open' : 'status-closed' }}">
                                {{ $userData['shift_status'] == 'مفتوح' ? '🟢' : '🔴' }} {{ $userData['shift_status'] }}
                            </span>
                            @if($userData['current_shift'])
                                <br>
                                <small class="text-muted">الشفت #{{ $userData['current_shift']->id }}</small>
                            @endif
                        </td>
                        <td>
                            <span class="cash-badge">{{ number_format($userData['current_cash'], 2) }}</span>
                            <br>
                            <small class="text-muted">ريال سعودي</small>
                        </td>
                        <td>
                            <span class="network-badge">{{ number_format($userData['current_network'], 2) }}</span>
                            <br>
                            <small class="text-muted">ريال سعودي</small>
                        </td>
                        <td>
                            <span class="amount-badge">{{ number_format($userData['total_sales'], 2) }}</span>
                            <br>
                            <small class="text-muted">إجمالي الفترة</small>
                        </td>
                        <td>
                            <span class="network-badge">{{ number_format($userData['total_network_sales'], 2) }}</span>
                            <br>
                            <small class="text-muted">شبكة الفترة</small>
                        </td>
                        <td>
                            <strong class="text-primary">{{ $userData['transactions_count'] }}</strong>
                            <br>
                            <small class="text-muted">متوسط: {{ number_format($userData['avg_invoice_value'], 2) }}</small>
                        </td>
                        <td>
                            <button type="button" class="btn btn-sm btn-outline-primary"
                                    onclick="showUserDetails({{ $userData['user']->id }})">
                                <i class="fas fa-eye"></i> تفاصيل
                            </button>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="9" class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <br>
                            <h5 class="text-muted">لا توجد بيانات مستخدمين للعرض</h5>
                            <p class="text-muted">تأكد من اختيار الفلاتر المناسبة</p>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- جدول الشفتات -->
<div class="card table-monitoring">
    <div class="section-header">
        <i class="fas fa-clock"></i> جدول الشفتات
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>رقم الشفت</th>
                        <th>اسم المستخدم</th>
                        <th>المستودع</th>
                        <th>رصيد البداية</th>
                        <th>النقد الحالي</th>
                        <th>الشبكة الحالية</th>
                        <th>إجمالي النقد والشبكة</th>
                        <th>وقت الفتح</th>
                        <th>وقت الإغلاق</th>
                        <th>مدة الشفت</th>
                        <th>حالة الشفت</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($shiftsData as $shift)
                    @php
                        $currentCash = $shift->financialRecord ? $shift->financialRecord->current_cash : 0;
                        $currentNetwork = $shift->financialRecord ? $shift->financialRecord->overnetwork_cash : 0;
                        $totalCashNetwork = $currentCash + $currentNetwork;

                        // حساب مدة الشفت
                        $duration = '';
                        if ($shift->opened_at) {
                            $endTime = $shift->closed_at ? $shift->closed_at : now();
                            $diff = $shift->opened_at->diff($endTime);
                            $duration = $diff->format('%h ساعة %i دقيقة');
                        }
                    @endphp
                    <tr>
                        <td>
                            <strong class="text-primary">#{{ $shift->id }}</strong>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm me-2">
                                    <div class="avatar-title bg-success rounded-circle">
                                        {{ substr($shift->creator->name, 0, 1) }}
                                    </div>
                                </div>
                                <div>
                                    <strong>{{ $shift->creator->name }}</strong>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-info">
                                {{ $shift->warehouse ? $shift->warehouse->name : 'غير محدد' }}
                            </span>
                        </td>
                        <td>
                            <span class="amount-badge">{{ number_format($shift->shift_opening_balance, 2) }}</span>
                            <br>
                            <small class="text-muted">رصيد البداية</small>
                        </td>
                        <td>
                            <span class="cash-badge">{{ number_format($currentCash, 2) }}</span>
                            <br>
                            <small class="text-muted">نقدي</small>
                        </td>
                        <td>
                            <span class="network-badge">{{ number_format($currentNetwork, 2) }}</span>
                            <br>
                            <small class="text-muted">شبكة</small>
                        </td>
                        <td>
                            <span class="badge bg-warning text-dark">{{ number_format($totalCashNetwork, 2) }}</span>
                            <br>
                            <small class="text-muted">الإجمالي</small>
                        </td>
                        <td>
                            <strong>{{ $shift->opened_at ? $shift->opened_at->format('Y-m-d') : 'غير محدد' }}</strong>
                            <br>
                            <small class="text-muted">{{ $shift->opened_at ? $shift->opened_at->format('H:i') : '' }}</small>
                        </td>
                        <td>
                            @if($shift->closed_at)
                                <strong>{{ $shift->closed_at->format('Y-m-d') }}</strong>
                                <br>
                                <small class="text-muted">{{ $shift->closed_at->format('H:i') }}</small>
                            @else
                                <span class="text-warning">لم يُغلق بعد</span>
                            @endif
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ $duration }}</span>
                        </td>
                        <td>
                            <span class="status-badge {{ !$shift->is_closed ? 'status-open' : 'status-closed' }}">
                                {{ !$shift->is_closed ? '🟢 مفتوح' : '🔴 مغلق' }}
                            </span>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="11" class="text-center py-5">
                            <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                            <br>
                            <h5 class="text-muted">لا توجد شفتات للعرض</h5>
                            <p class="text-muted">تأكد من اختيار الفلاتر المناسبة</p>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- جدول إدارة النقد والسندات -->
<div class="card table-monitoring">
    <div class="section-header">
        <i class="fas fa-money-bill-wave"></i> جدول إدارة النقد والسندات
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>نوع المعاملة</th>
                        <th>اسم المستخدم</th>
                        <th>المستودع</th>
                        <th>المبلغ الإجمالي</th>
                        <th>مبلغ نقدي</th>
                        <th>مبلغ شبكة</th>
                        <th>طريقة الدفع</th>
                        <th>الغرض/السبب</th>
                        <th>التاريخ والوقت</th>
                        <th>رقم الشفت</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($transactionsData as $transaction)
                    <tr>
                        <td>
                            @if($transaction->transaction_type == 'sales')
                                <span class="badge bg-success">
                                    <i class="fas fa-shopping-cart"></i> مبيعة
                                </span>
                            @elseif($transaction->transaction_type == 'receipt_voucher')
                                <span class="badge bg-primary">
                                    <i class="fas fa-arrow-down"></i> سند قبض
                                </span>
                            @elseif($transaction->transaction_type == 'payment_voucher')
                                <span class="badge bg-danger">
                                    <i class="fas fa-arrow-up"></i> سند صرف
                                </span>
                            @else
                                <span class="badge bg-secondary">{{ $transaction->transaction_type }}</span>
                            @endif
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm me-2">
                                    <div class="avatar-title bg-warning rounded-circle">
                                        {{ substr($transaction->creator->name, 0, 1) }}
                                    </div>
                                </div>
                                <div>
                                    <strong>{{ $transaction->creator->name }}</strong>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-info">
                                {{ $transaction->shift && $transaction->shift->warehouse ? $transaction->shift->warehouse->name : 'غير محدد' }}
                            </span>
                        </td>
                        <td>
                            <span class="amount-badge">{{ number_format($transaction->cash_amount, 2) }}</span>
                            <br>
                            <small class="text-muted">ريال سعودي</small>
                        </td>
                        <td>
                            @if($transaction->payment_method == 'cash')
                                <span class="cash-badge">{{ number_format($transaction->cash_amount, 2) }}</span>
                            @else
                                <span class="text-muted">0.00</span>
                            @endif
                        </td>
                        <td>
                            @if($transaction->payment_method == 'bank_transfer')
                                <span class="network-badge">{{ number_format($transaction->cash_amount, 2) }}</span>
                            @else
                                <span class="text-muted">0.00</span>
                            @endif
                        </td>
                        <td>
                            @if($transaction->payment_method == 'cash')
                                <span class="badge bg-success">
                                    <i class="fas fa-money-bill"></i> نقدي
                                </span>
                            @elseif($transaction->payment_method == 'bank_transfer')
                                <span class="badge bg-info">
                                    <i class="fas fa-credit-card"></i> شبكة
                                </span>
                            @else
                                <span class="badge bg-secondary">{{ $transaction->payment_method }}</span>
                            @endif
                        </td>
                        <td>
                            <span class="text-muted">
                                @if($transaction->transaction_type == 'sales')
                                    عملية بيع
                                @elseif($transaction->transaction_type == 'receipt_voucher')
                                    استلام مبلغ
                                @elseif($transaction->transaction_type == 'payment_voucher')
                                    صرف مبلغ
                                @else
                                    {{ $transaction->transaction_type }}
                                @endif
                            </span>
                        </td>
                        <td>
                            <strong>{{ $transaction->created_at->format('Y-m-d') }}</strong>
                            <br>
                            <small class="text-muted">{{ $transaction->created_at->format('H:i:s') }}</small>
                        </td>
                        <td>
                            <span class="badge bg-primary">#{{ $transaction->shift_id }}</span>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="10" class="text-center py-5">
                            <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                            <br>
                            <h5 class="text-muted">لا توجد معاملات مالية للعرض</h5>
                            <p class="text-muted">تأكد من اختيار الفلاتر المناسبة</p>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- جدول مبيعات POS -->
<div class="card table-monitoring">
    <div class="section-header">
        <i class="fas fa-cash-register"></i> جدول مبيعات POS الكلاسيكي
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>اسم المستخدم</th>
                        <th>المستودع</th>
                        <th>اسم العميل</th>
                        <th>إجمالي الفاتورة</th>
                        <th>مبلغ نقدي</th>
                        <th>مبلغ شبكة</th>
                        <th>طريقة الدفع</th>
                        <th>عدد الأصناف</th>
                        <th>رقم الشفت</th>
                        <th>التاريخ والوقت</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($posData as $pos)
                    @php
                        $posProducts = App\Models\PosProduct::where('pos_id', $pos->id)->get();
                        $total = 0;
                        $itemsCount = $posProducts->count();
                        foreach ($posProducts as $product) {
                            $total += ($product->price * $product->quantity);
                        }
                    @endphp
                    <tr>
                        <td>
                            <strong class="text-primary">#POS-{{ $pos->id }}</strong>
                            <br>
                            <small class="text-muted">{{ $pos->pos_date ? $pos->pos_date : $pos->created_at->format('Y-m-d') }}</small>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm me-2">
                                    <div class="avatar-title bg-success rounded-circle">
                                        {{ substr($pos->createdBy->name, 0, 1) }}
                                    </div>
                                </div>
                                <div>
                                    <strong>{{ $pos->createdBy->name }}</strong>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-info">
                                {{ $pos->warehouse ? $pos->warehouse->name : 'غير محدد' }}
                            </span>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm me-2">
                                    <div class="avatar-title bg-secondary rounded-circle">
                                        <i class="fas fa-user"></i>
                                    </div>
                                </div>
                                <div>
                                    <strong>{{ $pos->customer ? $pos->customer->name : 'عميل نقدي' }}</strong>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="amount-badge">{{ number_format($total, 2) }}</span>
                            <br>
                            <small class="text-muted">ريال سعودي</small>
                        </td>
                        <td>
                            @if($pos->payment_method == 'cash')
                                <span class="cash-badge">{{ number_format($total, 2) }}</span>
                            @else
                                <span class="text-muted">0.00</span>
                            @endif
                        </td>
                        <td>
                            @if(in_array($pos->payment_method, ['network', 'bank_transfer']))
                                <span class="network-badge">{{ number_format($total, 2) }}</span>
                            @else
                                <span class="text-muted">0.00</span>
                            @endif
                        </td>
                        <td>
                            @if($pos->payment_method == 'cash')
                                <span class="badge bg-success">
                                    <i class="fas fa-money-bill"></i> نقدي
                                </span>
                            @elseif(in_array($pos->payment_method, ['network', 'bank_transfer']))
                                <span class="badge bg-info">
                                    <i class="fas fa-credit-card"></i> شبكة
                                </span>
                            @else
                                <span class="badge bg-secondary">{{ $pos->payment_method ?? 'غير محدد' }}</span>
                            @endif
                        </td>
                        <td>
                            <span class="badge bg-warning text-dark">{{ $itemsCount }}</span>
                            <br>
                            <small class="text-muted">صنف</small>
                        </td>
                        <td>
                            @if($pos->shift_id)
                                <span class="badge bg-primary">#{{ $pos->shift_id }}</span>
                            @else
                                <span class="text-muted">غير محدد</span>
                            @endif
                        </td>
                        <td>
                            <strong>{{ $pos->created_at->format('Y-m-d') }}</strong>
                            <br>
                            <small class="text-muted">{{ $pos->created_at->format('H:i:s') }}</small>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="11" class="text-center py-5">
                            <i class="fas fa-cash-register fa-3x text-muted mb-3"></i>
                            <br>
                            <h5 class="text-muted">لا توجد مبيعات POS للعرض</h5>
                            <p class="text-muted">تأكد من اختيار الفلاتر المناسبة</p>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>
@endsection

@push('script-page')
<script>
$(document).ready(function() {
    // Auto refresh every 30 seconds
    let autoRefreshInterval;

    function startAutoRefresh() {
        autoRefreshInterval = setInterval(function() {
            $('#refreshIndicator').show();
            location.reload();
        }, 30000); // 30 seconds
    }

    function stopAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
        }
    }

    // Start auto refresh
    startAutoRefresh();

    // Stop auto refresh when user interacts with filters
    $('#filterForm input, #filterForm select').on('focus', function() {
        stopAutoRefresh();
    });

    // Restart auto refresh after form submission
    $('#filterForm').on('submit', function() {
        setTimeout(startAutoRefresh, 1000);
    });
});

// Date filter functions
function setDateFilter(filter) {
    $('#dateFilterInput').val(filter);

    // Update active button
    $('.date-filter-btn').removeClass('active');
    $(`button[onclick="setDateFilter('${filter}')"]`).addClass('active');

    // Show/hide custom date range
    if (filter === 'custom') {
        $('#customDateRange').show();
    } else {
        $('#customDateRange').hide();
        // Auto submit for non-custom filters
        $('#filterForm').submit();
    }
}

// Show user details modal
function showUserDetails(userId) {
    // You can implement a modal here to show detailed user information
    alert('تفاصيل المستخدم رقم: ' + userId + '\n\nسيتم تطوير هذه الميزة قريباً');
}

// Export data functions
function exportData(format) {
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.set('export', format);

    // Create a temporary link and click it
    const link = document.createElement('a');
    link.href = currentUrl.toString();
    link.download = `user-monitoring-${new Date().toISOString().split('T')[0]}.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Real-time updates for amounts (you can enhance this with WebSockets)
function updateAmounts() {
    // This function can be enhanced to fetch real-time data via AJAX
    console.log('Updating amounts...');
}

// Format numbers with Arabic locale
function formatNumber(number) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'decimal',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(number);
}

// Add loading states to buttons
function addLoadingState(button) {
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
    button.disabled = true;

    return function() {
        button.innerHTML = originalText;
        button.disabled = false;
    };
}

// Enhanced table interactions
$(document).ready(function() {
    // Add hover effects to table rows
    $('.table-monitoring tbody tr').hover(
        function() {
            $(this).addClass('table-active');
        },
        function() {
            $(this).removeClass('table-active');
        }
    );

    // Add click to copy functionality for amounts
    $('.amount-badge, .cash-badge, .network-badge').click(function() {
        const text = $(this).text().replace(/[^\d.]/g, '');
        navigator.clipboard.writeText(text).then(function() {
            // Show temporary tooltip
            const badge = $(this);
            const originalText = badge.text();
            badge.text('تم النسخ!');
            setTimeout(function() {
                badge.text(originalText);
            }, 1000);
        }.bind(this));
    });

    // Add search functionality to tables
    $('#searchInput').on('keyup', function() {
        const value = $(this).val().toLowerCase();
        $('.table-monitoring tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });
});

// Print functionality
function printTable(tableSelector) {
    const printWindow = window.open('', '_blank');
    const table = $(tableSelector).clone();

    printWindow.document.write(`
        <html>
        <head>
            <title>طباعة التقرير</title>
            <style>
                body { font-family: Arial, sans-serif; direction: rtl; }
                table { width: 100%; border-collapse: collapse; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                th { background-color: #f2f2f2; }
                .amount-badge, .cash-badge, .network-badge {
                    background: none !important;
                    color: black !important;
                    padding: 0 !important;
                }
            </style>
        </head>
        <body>
            <h2>تقرير مراقبة المستخدمين والأموال</h2>
            <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
            ${table[0].outerHTML}
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.print();
}

// Keyboard shortcuts
$(document).keydown(function(e) {
    // Ctrl+R for refresh
    if (e.ctrlKey && e.keyCode === 82) {
        e.preventDefault();
        location.reload();
    }

    // Ctrl+E for export
    if (e.ctrlKey && e.keyCode === 69) {
        e.preventDefault();
        exportData('excel');
    }

    // Ctrl+P for print
    if (e.ctrlKey && e.keyCode === 80) {
        e.preventDefault();
        printTable('.table-monitoring');
    }
});
</script>
@endpush
