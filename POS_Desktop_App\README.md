# نظام نقاط البيع - تطبيق سطح المكتب

## 📋 نظرة عامة

تطبيق سطح مكتب متطور لإدارة نقاط البيع مع مزامنة تلقائية مع السيرفر، مبني باستخدام WPF و .NET 6.

## ✨ الميزات الرئيسية

- **واجهة مستخدم حديثة** مع Material Design
- **نظام تخزين مؤقت محلي** باستخدام SQLite
- **مزامنة تلقائية** مع قاعدة البيانات على السيرفر
- **دعم العمل بدون اتصال** (Offline Mode)
- **إدارة شاملة للمنتجات والعملاء**
- **نظام فواتير متقدم**
- **تقارير مفصلة**

## 🛠️ المتطلبات

### البرمجيات المطلوبة:
- **Windows 10/11** (64-bit)
- **.NET 6.0 SDK** أو أحدث
- **Visual Studio 2022** (للتطوير)
- **MySQL Server** (للقاعدة الرئيسية)

### تثبيت .NET 6.0 SDK:
```bash
# تحميل من الرابط الرسمي:
https://dotnet.microsoft.com/download/dotnet/6.0

# أو استخدام winget:
winget install Microsoft.DotNet.SDK.6
```

## 🚀 التثبيت والتشغيل

### الطريقة الأولى: تشغيل مباشر
```bash
# 1. فتح مجلد المشروع
cd POS_Desktop_App

# 2. استعادة الحزم
dotnet restore

# 3. بناء المشروع
dotnet build

# 4. تشغيل التطبيق
dotnet run
```

### الطريقة الثانية: استخدام ملف التشغيل
```bash
# تشغيل ملف run.bat
run.bat
```

### الطريقة الثالثة: Visual Studio
1. افتح ملف `POS_Desktop_App.csproj` في Visual Studio
2. اضغط F5 أو اختر Debug > Start Debugging

## ⚙️ الإعدادات

### إعداد قاعدة البيانات:
قم بتحديث سلسلة الاتصال في ملف `App.config`:

```xml
<connectionStrings>
  <add name="DefaultConnection" 
       connectionString="Server=localhost;Database=pos_database;Uid=root;Pwd=your_password;CharSet=utf8mb4;SslMode=none;" 
       providerName="MySql.Data.MySqlClient" />
</connectionStrings>
```

### إعداد السيرفر:
```xml
<appSettings>
  <add key="ServerUrl" value="http://your-server.com:8000" />
  <add key="ApiBaseUrl" value="http://your-server.com:8000/api" />
</appSettings>
```

## 📁 هيكل المشروع

```
POS_Desktop_App/
├── Models/
│   ├── DatabaseModels/     # نماذج قاعدة البيانات الرئيسية
│   └── LocalCache/         # نماذج التخزين المؤقت
├── Services/
│   ├── DatabaseService/    # خدمات قاعدة البيانات
│   ├── CacheService/       # خدمات التخزين المؤقت
│   └── SyncService/        # خدمات المزامنة
├── ViewModels/             # نماذج العرض (MVVM)
├── Views/                  # واجهات المستخدم
├── App.xaml               # إعدادات التطبيق
├── App.config             # ملف التكوين
└── POS_Desktop_App.csproj # ملف المشروع
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

#### 1. خطأ: .NET SDK غير موجود
```
الحل: تثبيت .NET 6.0 SDK من الموقع الرسمي
```

#### 2. خطأ في الاتصال بقاعدة البيانات
```
الحل: التأكد من تشغيل MySQL وصحة سلسلة الاتصال
```

#### 3. خطأ في استعادة الحزم
```
الحل: تشغيل dotnet restore مرة أخرى
```

#### 4. خطأ في بناء المشروع
```
الحل: التأكد من وجود جميع الملفات المطلوبة
```

## 📊 الحالة الحالية

### ✅ مكتمل:
- [x] الهيكل الأساسي للمشروع
- [x] نماذج قاعدة البيانات
- [x] نظام التخزين المؤقت
- [x] خدمة المزامنة
- [x] الواجهة الرئيسية
- [x] شاشة POS الأساسية
- [x] شاشة إدارة المنتجات

### 🔄 قيد التطوير:
- [ ] منطق العمل (Business Logic)
- [ ] ربط البيانات (Data Binding)
- [ ] خدمة الطباعة
- [ ] التقارير

### 📅 مخطط لها:
- [ ] دعم الباركود
- [ ] الطباعة الحرارية
- [ ] تقارير متقدمة
- [ ] نظام النسخ الاحتياطي

## 🎯 كيفية الاستخدام

### 1. بدء التشغيل:
- شغل التطبيق باستخدام إحدى الطرق المذكورة أعلاه
- ستظهر الواجهة الرئيسية مع القائمة الجانبية

### 2. شاشة البيع:
- اختر "شاشة البيع" من القائمة الجانبية
- ابحث عن المنتجات وأضفها للسلة
- أتمم عملية الدفع

### 3. إدارة المنتجات:
- اختر "المنتجات" من القائمة الجانبية
- عرض وإدارة جميع المنتجات

### 4. المزامنة:
- اضغط على "مزامنة البيانات" لمزامنة البيانات مع السيرفر
- المزامنة تتم تلقائياً كل 30 ثانية

## 🔒 الأمان

- تشفير البيانات المحلية
- مصادقة المستخدم
- تسجيل العمليات
- نسخ احتياطية آمنة

## 📞 الدعم

للحصول على المساعدة:
- راجع ملف `POS_DESKTOP_INSTALLATION_GUIDE.md`
- تحقق من ملفات السجلات في مجلد `Logs/`
- تواصل مع فريق التطوير

## 📄 الترخيص

هذا المشروع مطور خصيصاً لاحتياجاتك ويمكن تخصيصه حسب المتطلبات.

---

**ملاحظة**: هذا الإصدار الأولي من التطبيق. المزيد من الميزات قيد التطوير.
