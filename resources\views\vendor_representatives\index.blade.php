@extends('layouts.admin')

@section('page-title')
    {{ __('تسجيل المندوبين والمردين') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('الرئيسية') }}</a></li>
    <li class="breadcrumb-item">{{ __('إدارة عمليات الفروع') }}</li>
    <li class="breadcrumb-item">{{ __('تسجيل المندوبين والمردين') }}</li>
@endsection

@section('action-btn')
    <div class="float-end">
        @if(Auth::user()->type == 'company' || Auth::user()->hasRole('SUPER FIESR'))
            <a href="{{ route('vendor.representatives.create') }}" class="btn btn-sm btn-primary" data-ajax-popup="true" data-title="{{ __('إضافة مندوب جديد') }}" data-bs-toggle="tooltip" title="{{ __('إضافة مندوب جديد') }}">
                <i class="ti ti-plus"></i>
            </a>
        @endif
    </div>
@endsection

@section('content')
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center justify-content-between">
                        <div class="col-auto mb-3 mb-sm-0">
                            <div class="d-flex align-items-center">
                                <div class="theme-avtar bg-primary">
                                    <i class="ti ti-users"></i>
                                </div>
                                <div class="ms-3">
                                    <small class="text-muted">{{ __('إجمالي المندوبين') }}</small>
                                    <h6 class="m-0">{{ $stats['total'] }}</h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center justify-content-between">
                        <div class="col-auto mb-3 mb-sm-0">
                            <div class="d-flex align-items-center">
                                <div class="theme-avtar bg-success">
                                    <i class="ti ti-user-check"></i>
                                </div>
                                <div class="ms-3">
                                    <small class="text-muted">{{ __('المندوبين النشطين') }}</small>
                                    <h6 class="m-0">{{ $stats['active'] }}</h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center justify-content-between">
                        <div class="col-auto mb-3 mb-sm-0">
                            <div class="d-flex align-items-center">
                                <div class="theme-avtar bg-danger">
                                    <i class="ti ti-user-x"></i>
                                </div>
                                <div class="ms-3">
                                    <small class="text-muted">{{ __('المندوبين غير النشطين') }}</small>
                                    <h6 class="m-0">{{ $stats['inactive'] }}</h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center justify-content-between">
                        <div class="col-auto mb-3 mb-sm-0">
                            <div class="d-flex align-items-center">
                                <div class="theme-avtar bg-info">
                                    <i class="ti ti-building-store"></i>
                                </div>
                                <div class="ms-3">
                                    <small class="text-muted">{{ __('الشركات الموردة') }}</small>
                                    <h6 class="m-0">{{ $stats['vendors_count'] }}</h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('قائمة المندوبين والمردين') }}</h5>
                </div>
                <div class="card-body table-border-style">
                    <div class="table-responsive">
                        <table class="table datatable">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>{{ __('اسم المندوب') }}</th>
                                    <th>{{ __('رقم الهاتف') }}</th>
                                    <th>{{ __('الشركة الموردة') }}</th>
                                    <th>{{ __('فئة المنتجات') }}</th>
                                    <th>{{ __('تاريخ التسجيل') }}</th>
                                    <th>{{ __('الحالة') }}</th>
                                    <th>{{ __('الإجراءات') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($representatives as $key => $representative)
                                    <tr>
                                        <td>{{ $key + 1 }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="theme-avtar bg-primary me-2">
                                                    <i class="ti ti-user"></i>
                                                </div>
                                                <span class="fw-bold">{{ $representative->name }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="text-primary">
                                                <i class="ti ti-phone me-1"></i>{{ $representative->phone }}
                                            </span>
                                        </td>
                                        <td>
                                            @if($representative->vendor)
                                                <span class="badge bg-info-light text-info">{{ $representative->vendor->name }}</span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($representative->category)
                                                <span class="badge bg-warning-light text-warning">{{ $representative->category->name }}</span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ $representative->created_at->format('Y-m-d') }}</small>
                                        </td>
                                        <td>
                                            @if($representative->is_active)
                                                <span class="badge bg-success">{{ __('نشط') }}</span>
                                            @else
                                                <span class="badge bg-danger">{{ __('غير نشط') }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="action-btn bg-info ms-2">
                                                <a href="{{ route('vendor.representatives.show', $representative->id) }}" class="mx-3 btn btn-sm align-items-center" data-bs-toggle="tooltip" title="{{ __('عرض') }}">
                                                    <i class="ti ti-eye text-white"></i>
                                                </a>
                                            </div>
                                            
                                            @if(Auth::user()->type == 'company')
                                                <div class="action-btn bg-primary ms-2">
                                                    <a href="{{ route('vendor.representatives.edit', $representative->id) }}" class="mx-3 btn btn-sm align-items-center" data-ajax-popup="true" data-title="{{ __('تعديل المندوب') }}" data-bs-toggle="tooltip" title="{{ __('تعديل') }}">
                                                        <i class="ti ti-pencil text-white"></i>
                                                    </a>
                                                </div>

                                                <div class="action-btn bg-warning ms-2">
                                                    <a href="#" class="mx-3 btn btn-sm align-items-center toggle-status" data-id="{{ $representative->id }}" data-bs-toggle="tooltip" title="{{ __('تغيير الحالة') }}">
                                                        <i class="ti ti-toggle-{{ $representative->is_active ? 'right' : 'left' }} text-white"></i>
                                                    </a>
                                                </div>

                                                <div class="action-btn bg-danger ms-2">
                                                    {!! Form::open(['method' => 'DELETE', 'route' => ['vendor.representatives.destroy', $representative->id], 'id' => 'delete-form-' . $representative->id, 'style' => 'display:inline']) !!}
                                                    <a href="#" class="mx-3 btn btn-sm align-items-center bs-pass-para" data-bs-toggle="tooltip" title="{{ __('حذف') }}">
                                                        <i class="ti ti-trash text-white"></i>
                                                    </a>
                                                    {!! Form::close() !!}
                                                </div>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4">
                        {{ $representatives->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
<script>
    $(document).ready(function() {
        // Toggle Status
        $('.toggle-status').on('click', function(e) {
            e.preventDefault();
            var id = $(this).data('id');
            var button = $(this);

            $.ajax({
                url: '{{ route("vendor.representatives.toggle.status", ":id") }}'.replace(':id', id),
                type: 'POST',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        show_toastr('Success', response.message, 'success');
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    }
                },
                error: function(xhr) {
                    show_toastr('Error', 'حدث خطأ أثناء تغيير الحالة', 'error');
                }
            });
        });
    });
</script>
@endpush
