<UserControl x:Class="POS_Desktop_App.Views.ProductsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">
    
    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="إدارة المنتجات" 
                             FontSize="24" 
                             FontWeight="Medium"
                             Margin="0,0,0,8"/>
                    <TextBlock Text="عرض وإدارة جميع المنتجات في النظام" 
                             FontSize="14"
                             Foreground="{StaticResource TextSecondaryBrush}"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Style="{StaticResource PrimaryButtonStyle}"
                          Content="منتج جديد"
                          Margin="8,0">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Plus" 
                                                       Width="16" 
                                                       Height="16" 
                                                       Margin="0,0,4,0"/>
                                <TextBlock Text="منتج جديد"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Content -->
        <materialDesign:Card Grid.Row="1" Style="{StaticResource CardStyle}" Margin="0,16,0,0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Search and Filters -->
                <Grid Grid.Row="0" Margin="0,0,0,16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBox Grid.Column="0"
                           Style="{StaticResource MaterialTextBoxStyle}"
                           materialDesign:HintAssist.Hint="البحث عن منتج..."
                           Margin="0,0,8,0"/>

                    <ComboBox Grid.Column="1"
                            Style="{StaticResource MaterialComboBoxStyle}"
                            materialDesign:HintAssist.Hint="الفئة"
                            Width="150"
                            Margin="8,0">
                        <ComboBoxItem Content="جميع الفئات"/>
                        <ComboBoxItem Content="مشروبات"/>
                        <ComboBoxItem Content="وجبات خفيفة"/>
                        <ComboBoxItem Content="حلويات"/>
                    </ComboBox>

                    <Button Grid.Column="2"
                          Style="{StaticResource MaterialDesignIconButton}"
                          ToolTip="تحديث"
                          Margin="8,0,0,0">
                        <materialDesign:PackIcon Kind="Refresh" Width="24" Height="24"/>
                    </Button>
                </Grid>

                <!-- Products DataGrid -->
                <DataGrid Grid.Row="1"
                        Style="{StaticResource MaterialDataGridStyle}"
                        AutoGenerateColumns="False">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="الرمز" Binding="{Binding Sku}" Width="100"/>
                        <DataGridTextColumn Header="الاسم" Binding="{Binding Name}" Width="*"/>
                        <DataGridTextColumn Header="الفئة" Binding="{Binding CategoryName}" Width="120"/>
                        <DataGridTextColumn Header="سعر البيع" Binding="{Binding SalePrice, StringFormat='{}{0:C2}'}" Width="100"/>
                        <DataGridTextColumn Header="المخزون" Binding="{Binding Quantity}" Width="80"/>
                        <DataGridTemplateColumn Header="الحالة" Width="80">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <materialDesign:Chip Content="نشط" 
                                                       Background="{StaticResource SuccessBrush}"
                                                       Foreground="White"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Header="الإجراءات" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                              ToolTip="تعديل">
                                            <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"/>
                                        </Button>
                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                              ToolTip="حذف">
                                            <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>
    </Grid>
</UserControl>
