using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace POS_Desktop_App.Models.DatabaseModels
{
    [Table("users")]
    public class User
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("name")]
        [Required]
        [StringLength(255)]
        public string Name { get; set; }

        [Column("email")]
        [Required]
        [StringLength(255)]
        public string Email { get; set; }

        [Column("password")]
        [Required]
        [StringLength(255)]
        public string Password { get; set; }

        [Column("type")]
        [Required]
        [StringLength(50)]
        public string Type { get; set; }

        [Column("avatar")]
        [StringLength(100)]
        public string Avatar { get; set; } = "";

        [Column("lang")]
        [StringLength(10)]
        public string Lang { get; set; } = "ar";

        [Column("created_by")]
        public int CreatedBy { get; set; } = 0;

        [Column("warehouse_id")]
        public long? WarehouseId { get; set; }

        [Column("is_active")]
        public int IsActive { get; set; } = 1;

        [Column("created_at")]
        public DateTime CreatedAt { get; set; }

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; }

        // Navigation Properties
        public virtual Warehouse Warehouse { get; set; }

        // Calculated Properties
        [NotMapped]
        public bool IsActiveUser => IsActive == 1;

        [NotMapped]
        public string WarehouseName => Warehouse?.Name ?? "غير محدد";

        [NotMapped]
        public string AvatarUrl
        {
            get
            {
                if (string.IsNullOrEmpty(Avatar))
                    return "/Images/default-avatar.png";
                return $"/uploads/avatars/{Avatar}";
            }
        }

        public override string ToString()
        {
            return Name;
        }
    }
}
