<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص التعديل المباشر</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; }
        .error { background-color: #f8d7da; }
        .warning { background-color: #fff3cd; }
        .info { background-color: #d1ecf1; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center">🔍 تشخيص التعديل المباشر</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="debug-section info">
                    <h5>📋 خطوات التشخيص</h5>
                    <ol>
                        <li>افتح صفحة معالجة فواتير المستودع</li>
                        <li>اضغط F12 لفتح Developer Tools</li>
                        <li>انتقل إلى تبويب Console</li>
                        <li>انقر على أي حقل قابل للتعديل</li>
                        <li>انسخ الرسائل من Console وألصقها هنا</li>
                    </ol>
                    
                    <a href="/warehouse-purchase-processing" class="btn btn-primary" target="_blank">
                        📊 افتح صفحة معالجة فواتير المستودع
                    </a>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="debug-section warning">
                    <h5>⚠️ أكواد التشخيص</h5>
                    <p>انسخ هذه الأكواد وألصقها في Console:</p>
                    
                    <h6>1. فحص jQuery:</h6>
                    <pre>console.log('jQuery:', typeof $ !== 'undefined');</pre>
                    
                    <h6>2. فحص الحقول القابلة للتعديل:</h6>
                    <pre>console.log('Editable fields:', $('.editable-field').length);
$('.editable-field').each(function(i) {
    console.log('Field ' + i + ':', $(this).data());
});</pre>
                    
                    <h6>3. فحص معالج الأحداث:</h6>
                    <pre>$('.editable-field').each(function(i) {
    console.log('Field ' + i + ' events:', $._data(this, 'events'));
});</pre>
                    
                    <h6>4. اختبار النقر المباشر:</h6>
                    <pre>$('.editable-field').first().trigger('click');</pre>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <div class="debug-section">
                    <h5>📝 منطقة لصق نتائج Console</h5>
                    <textarea id="consoleOutput" class="form-control" rows="15" placeholder="ألصق هنا رسائل Console بعد النقر على الحقول القابلة للتعديل..."></textarea>
                    <button class="btn btn-success mt-2" onclick="analyzeOutput()">تحليل النتائج</button>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <div id="analysisResult" class="debug-section" style="display: none;">
                    <h5>📊 نتيجة التحليل</h5>
                    <div id="analysisContent"></div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <div class="debug-section info">
                    <h5>✅ الرسائل المتوقعة عند النقر</h5>
                    <pre>
=== Editable field clicked! ===
Cell element: &lt;td class="editable-field"...&gt;
Cell HTML: اسم المورد &lt;i class="ti ti-edit edit-icon"&gt;&lt;/i&gt;
Cell classes: editable-field
Field data: {field: "vender_id", type: "select", value: "1", purchaseId: "123"}
Row element: &lt;tr data-purchase-id="123"&gt;
Row data-purchase-id: 123
Validation passed. Starting edit...
Creating select editor...
Loading options for field: vender_id
Options response: {success: true, options: [...]}
                    </pre>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function analyzeOutput() {
            const output = document.getElementById('consoleOutput').value;
            const resultDiv = document.getElementById('analysisResult');
            const contentDiv = document.getElementById('analysisContent');
            
            let analysis = '';
            
            // Check for jQuery
            if (output.includes('jQuery: true')) {
                analysis += '<div class="alert alert-success">✅ jQuery محمل بشكل صحيح</div>';
            } else {
                analysis += '<div class="alert alert-danger">❌ jQuery غير محمل أو لا يعمل</div>';
            }
            
            // Check for editable fields
            if (output.includes('Editable fields found:')) {
                const match = output.match(/Editable fields found: (\d+)/);
                if (match && parseInt(match[1]) > 0) {
                    analysis += `<div class="alert alert-success">✅ تم العثور على ${match[1]} حقل قابل للتعديل</div>`;
                } else {
                    analysis += '<div class="alert alert-danger">❌ لم يتم العثور على حقول قابلة للتعديل</div>';
                }
            }
            
            // Check for click events
            if (output.includes('Editable field clicked!')) {
                analysis += '<div class="alert alert-success">✅ تم تشغيل معالج النقر</div>';
            } else {
                analysis += '<div class="alert alert-danger">❌ لم يتم تشغيل معالج النقر</div>';
            }
            
            // Check for field data
            if (output.includes('Field data:')) {
                analysis += '<div class="alert alert-success">✅ تم استخراج بيانات الحقل</div>';
            } else {
                analysis += '<div class="alert alert-danger">❌ فشل في استخراج بيانات الحقل</div>';
            }
            
            // Check for validation
            if (output.includes('Validation passed')) {
                analysis += '<div class="alert alert-success">✅ تم تمرير التحقق من صحة البيانات</div>';
            } else if (output.includes('Field is missing') || output.includes('Purchase ID is missing')) {
                analysis += '<div class="alert alert-danger">❌ فشل التحقق من صحة البيانات</div>';
            }
            
            // Check for editor creation
            if (output.includes('Creating select editor') || output.includes('Creating date editor') || output.includes('Creating text editor')) {
                analysis += '<div class="alert alert-success">✅ تم إنشاء محرر التعديل</div>';
            } else {
                analysis += '<div class="alert alert-warning">⚠️ لم يتم إنشاء محرر التعديل</div>';
            }
            
            // Check for AJAX
            if (output.includes('Loading options for field')) {
                analysis += '<div class="alert alert-success">✅ تم بدء تحميل الخيارات</div>';
            }
            
            if (output.includes('Options response:')) {
                analysis += '<div class="alert alert-success">✅ تم تحميل الخيارات بنجاح</div>';
            }
            
            // Provide recommendations
            analysis += '<div class="alert alert-info"><h6>💡 التوصيات:</h6>';
            
            if (!output.includes('Editable field clicked!')) {
                analysis += '<p>• تأكد من أن معالج الأحداث مرتبط بالعناصر</p>';
                analysis += '<p>• جرب النقر مباشرة على النص وليس على الأيقونة</p>';
            }
            
            if (output.includes('Field is missing')) {
                analysis += '<p>• تحقق من أن data-field موجود في HTML</p>';
            }
            
            if (output.includes('Purchase ID is missing')) {
                analysis += '<p>• تحقق من أن data-purchase-id موجود في الصف</p>';
            }
            
            analysis += '</div>';
            
            contentDiv.innerHTML = analysis;
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html>
