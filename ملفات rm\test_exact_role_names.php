<?php
/**
 * اختبار الأسماء الصحيحة للأدوار - مطابقة تماماً
 */

// تحديد المسار الأساسي
$basePath = __DIR__;

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>اختبار الأسماء الصحيحة للأدوار</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }";
echo ".success { color: #28a745; background: #d4edda; padding: 15px; border-radius: 4px; margin: 10px 0; }";
echo ".error { color: #dc3545; background: #f8d7da; padding: 15px; border-radius: 4px; margin: 10px 0; }";
echo ".info { color: #17a2b8; background: #d1ecf1; padding: 15px; border-radius: 4px; margin: 10px 0; }";
echo ".warning { color: #856404; background: #fff3cd; padding: 15px; border-radius: 4px; margin: 10px 0; }";
echo "table { width: 100%; border-collapse: collapse; margin: 15px 0; }";
echo "th, td { border: 1px solid #ddd; padding: 12px; text-align: right; }";
echo "th { background: #f8f9fa; font-weight: bold; }";
echo ".code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }";
echo ".badge { padding: 4px 8px; border-radius: 4px; font-size: 12px; margin: 2px; }";
echo ".badge-success { background: #28a745; color: white; }";
echo ".badge-danger { background: #dc3545; color: white; }";
echo ".badge-warning { background: #ffc107; color: black; }";
echo ".badge-info { background: #17a2b8; color: white; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🎯 اختبار الأسماء الصحيحة للأدوار</h1>";

// الأسماء الصحيحة المطلوبة
$correctRoleNames = [
    'accountant',
    'delivery', 
    'SUPER FIESR',
    'SUPER FIESR BIG',
    'Cashier',
    'all'
];

echo "<div class='success'>";
echo "<h2>✅ الأسماء الصحيحة المطلوبة:</h2>";
echo "<table>";
echo "<tr><th>الترتيب</th><th>اسم الدور</th><th>ملاحظات</th></tr>";

foreach ($correctRoleNames as $index => $roleName) {
    $notes = '';
    if ($roleName === 'accountant') {
        $notes = 'حروف صغيرة';
    } elseif ($roleName === 'delivery') {
        $notes = 'حروف صغيرة';
    } elseif ($roleName === 'SUPER FIESR') {
        $notes = 'حروف كبيرة + مسافة';
    } elseif ($roleName === 'SUPER FIESR BIG') {
        $notes = 'حروف كبيرة + مسافات';
    } elseif ($roleName === 'Cashier') {
        $notes = 'حرف كبير أول + باقي صغيرة';
    } elseif ($roleName === 'all') {
        $notes = 'حروف صغيرة';
    }
    
    echo "<tr>";
    echo "<td>" . ($index + 1) . "</td>";
    echo "<td><span class='badge badge-info'>{$roleName}</span></td>";
    echo "<td>{$notes}</td>";
    echo "</tr>";
}
echo "</table>";
echo "</div>";

// فحص قاعدة البيانات
try {
    $pdo = new PDO("mysql:host=localhost;dbname=up20251", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='info'>";
    echo "<h2>🔍 فحص الأدوار في قاعدة البيانات:</h2>";
    
    // فحص جدول roles
    $stmt = $pdo->query("SELECT name FROM roles ORDER BY name");
    $dbRoles = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h3>الأدوار الموجودة في جدول roles:</h3>";
    echo "<table>";
    echo "<tr><th>اسم الدور</th><th>الحالة</th></tr>";
    
    foreach ($dbRoles as $dbRole) {
        $isCorrect = in_array($dbRole, $correctRoleNames);
        $statusClass = $isCorrect ? 'badge-success' : 'badge-warning';
        $statusText = $isCorrect ? '✅ صحيح' : '⚠️ غير مطابق';
        
        echo "<tr>";
        echo "<td><span class='badge badge-info'>{$dbRole}</span></td>";
        echo "<td><span class='badge {$statusClass}'>{$statusText}</span></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // فحص النماذج والأدوار المخصصة لها
    echo "<h3>النماذج والأدوار المخصصة لها:</h3>";
    $stmt = $pdo->query("SELECT id, name, visible_to_roles FROM forms ORDER BY created_at DESC");
    $forms = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($forms) > 0) {
        echo "<table>";
        echo "<tr><th>ID</th><th>اسم النموذج</th><th>الأدوار المخصصة</th><th>حالة التطابق</th></tr>";
        
        foreach ($forms as $form) {
            $visibleRoles = json_decode($form['visible_to_roles'], true);
            $allCorrect = true;
            $rolesHtml = '';
            
            if (is_array($visibleRoles)) {
                foreach ($visibleRoles as $role) {
                    $isCorrect = in_array($role, $correctRoleNames);
                    if (!$isCorrect) $allCorrect = false;
                    
                    $badgeClass = $isCorrect ? 'badge-success' : 'badge-danger';
                    $rolesHtml .= "<span class='badge {$badgeClass}'>{$role}</span> ";
                }
            }
            
            $statusClass = $allCorrect ? 'badge-success' : 'badge-danger';
            $statusText = $allCorrect ? '✅ جميع الأدوار صحيحة' : '❌ يوجد أدوار خاطئة';
            
            echo "<tr>";
            echo "<td>{$form['id']}</td>";
            echo "<td>{$form['name']}</td>";
            echo "<td>{$rolesHtml}</td>";
            echo "<td><span class='badge {$statusClass}'>{$statusText}</span></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='warning'>⚠️ لا توجد نماذج في قاعدة البيانات</div>";
    }
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div class='error'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</div>";
}

// فحص الكود
echo "<div class='warning'>";
echo "<h2>🔧 فحص الكود:</h2>";

echo "<h3>1. FormController.php - validation rules:</h3>";
echo "<div class='code'>";
echo "'visible_to_roles.*' => 'in:accountant,delivery,SUPER FIESR,SUPER FIESR BIG,Cashier,all'";
echo "</div>";

echo "<h3>2. create.blade.php - checkbox values:</h3>";
echo "<div class='code'>";
echo "{{ Form::checkbox('visible_to_roles[]', 'accountant', false, ...) }}<br>";
echo "{{ Form::checkbox('visible_to_roles[]', 'delivery', false, ...) }}<br>";
echo "{{ Form::checkbox('visible_to_roles[]', 'SUPER FIESR', false, ...) }}<br>";
echo "{{ Form::checkbox('visible_to_roles[]', 'SUPER FIESR BIG', false, ...) }}<br>";
echo "{{ Form::checkbox('visible_to_roles[]', 'Cashier', false, ...) }}<br>";
echo "{{ Form::checkbox('visible_to_roles[]', 'all', false, ...) }}";
echo "</div>";

echo "<h3>3. index.blade.php - switch cases:</h3>";
echo "<div class='code'>";
echo "@case('accountant') {{ 'accountant' }} @break<br>";
echo "@case('delivery') {{ 'delivery' }} @break<br>";
echo "@case('SUPER FIESR') {{ 'SUPER FIESR' }} @break<br>";
echo "@case('SUPER FIESR BIG') {{ 'SUPER FIESR BIG' }} @break<br>";
echo "@case('Cashier') {{ 'Cashier' }} @break<br>";
echo "@case('all') {{ 'all' }} @break";
echo "</div>";
echo "</div>";

// اختبار إنشاء نموذج
echo "<div class='info'>";
echo "<h2>🧪 اختبار إنشاء نموذج:</h2>";
echo "<p>لاختبار النظام، جرب إنشاء نموذج جديد واختر الأدوار التالية:</p>";
echo "<ul>";
foreach ($correctRoleNames as $role) {
    echo "<li><strong>{$role}</strong></li>";
}
echo "</ul>";
echo "<p>يجب أن يتم قبول النموذج بدون أخطاء validation.</p>";
echo "</div>";

// إرشادات التصحيح
echo "<div class='success'>";
echo "<h2>📋 إرشادات التصحيح:</h2>";
echo "<ol>";
echo "<li><strong>تحديث قاعدة البيانات:</strong> تأكد من أن الأدوار في جدول roles تطابق الأسماء الصحيحة</li>";
echo "<li><strong>تحديث النماذج الموجودة:</strong> قم بتحديث النماذج التي تحتوي على أسماء أدوار خاطئة</li>";
echo "<li><strong>اختبار الإنشاء:</strong> جرب إنشاء نموذج جديد بالأدوار الصحيحة</li>";
echo "<li><strong>اختبار العرض:</strong> تأكد من أن المستخدمين يرون النماذج المناسبة لأدوارهم</li>";
echo "</ol>";
echo "</div>";

// SQL لتصحيح قاعدة البيانات
echo "<div class='warning'>";
echo "<h2>🛠️ SQL لتصحيح قاعدة البيانات:</h2>";
echo "<div class='code'>";
echo "-- تحديث الأدوار في جدول roles<br>";
echo "UPDATE roles SET name = 'accountant' WHERE name = 'Accountant';<br>";
echo "UPDATE roles SET name = 'delivery' WHERE name = 'Delivery';<br>";
echo "UPDATE roles SET name = 'Cashier' WHERE name = 'cashier';<br><br>";

echo "-- تحديث النماذج الموجودة<br>";
echo "UPDATE forms SET visible_to_roles = JSON_REPLACE(visible_to_roles, '$[*]', <br>";
echo "&nbsp;&nbsp;CASE <br>";
echo "&nbsp;&nbsp;&nbsp;&nbsp;WHEN JSON_EXTRACT(visible_to_roles, '$[*]') = 'Accountant' THEN 'accountant'<br>";
echo "&nbsp;&nbsp;&nbsp;&nbsp;WHEN JSON_EXTRACT(visible_to_roles, '$[*]') = 'Delivery' THEN 'delivery'<br>";
echo "&nbsp;&nbsp;&nbsp;&nbsp;ELSE JSON_EXTRACT(visible_to_roles, '$[*]')<br>";
echo "&nbsp;&nbsp;END<br>";
echo ");";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
