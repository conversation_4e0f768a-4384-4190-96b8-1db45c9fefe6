<UserControl x:Class="POS_Desktop_App.Views.PosView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">
    
    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="شاشة نقاط البيع" 
                             FontSize="24" 
                             FontWeight="Medium"
                             Margin="0,0,0,8"/>
                    <TextBlock Text="اختر المنتجات وأضفها إلى السلة لإتمام عملية البيع" 
                             FontSize="14"
                             Foreground="{StaticResource TextSecondaryBrush}"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Style="{StaticResource PrimaryButtonStyle}"
                          Content="فاتورة جديدة"
                          Margin="8,0">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Plus" 
                                                       Width="16" 
                                                       Height="16" 
                                                       Margin="0,0,4,0"/>
                                <TextBlock Text="فاتورة جديدة"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="0,16,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Products Section -->
            <materialDesign:Card Grid.Column="0" Style="{StaticResource CardStyle}" Margin="0,0,8,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Search Bar -->
                    <TextBox Grid.Row="0"
                           Style="{StaticResource MaterialTextBoxStyle}"
                           materialDesign:HintAssist.Hint="البحث عن منتج (الاسم أو الباركود)"
                           Margin="0,0,0,16">
                        <TextBox.InputBindings>
                            <KeyBinding Key="Enter" Command="{Binding SearchCommand}"/>
                        </TextBox.InputBindings>
                    </TextBox>

                    <!-- Categories -->
                    <ScrollViewer Grid.Row="1" 
                                HorizontalScrollBarVisibility="Auto" 
                                VerticalScrollBarVisibility="Disabled"
                                Margin="0,0,0,16">
                        <StackPanel Orientation="Horizontal">
                            <Button Content="الكل" 
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Margin="0,0,8,0"/>
                            <Button Content="مشروبات" 
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Margin="0,0,8,0"/>
                            <Button Content="وجبات خفيفة" 
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Margin="0,0,8,0"/>
                            <Button Content="حلويات" 
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Margin="0,0,8,0"/>
                        </StackPanel>
                    </ScrollViewer>

                    <!-- Products Grid -->
                    <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
                        <ItemsControl>
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <UniformGrid Columns="4"/>
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.Items>
                                <!-- Sample Products -->
                                <materialDesign:Card Margin="4" 
                                                   materialDesign:ShadowAssist.ShadowDepth="Depth1"
                                                   Cursor="Hand">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="120"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <Rectangle Grid.Row="0" 
                                                 Fill="{StaticResource BackgroundBrush}"
                                                 Margin="8"/>
                                        
                                        <TextBlock Grid.Row="1" 
                                                 Text="كوكا كولا" 
                                                 FontWeight="Medium"
                                                 TextAlignment="Center"
                                                 Margin="8,4"/>
                                        
                                        <TextBlock Grid.Row="2" 
                                                 Text="5.00 ر.س" 
                                                 FontSize="16"
                                                 FontWeight="Bold"
                                                 Foreground="{StaticResource PrimaryBrush}"
                                                 TextAlignment="Center"
                                                 Margin="8,0"/>
                                        
                                        <TextBlock Grid.Row="3" 
                                                 Text="متوفر: 25" 
                                                 FontSize="12"
                                                 Foreground="{StaticResource TextSecondaryBrush}"
                                                 TextAlignment="Center"
                                                 Margin="8,0,8,8"/>
                                    </Grid>
                                </materialDesign:Card>

                                <!-- Add more sample products -->
                                <materialDesign:Card Margin="4" 
                                                   materialDesign:ShadowAssist.ShadowDepth="Depth1"
                                                   Cursor="Hand">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="120"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <Rectangle Grid.Row="0" 
                                                 Fill="{StaticResource BackgroundBrush}"
                                                 Margin="8"/>
                                        
                                        <TextBlock Grid.Row="1" 
                                                 Text="شيبس ليز" 
                                                 FontWeight="Medium"
                                                 TextAlignment="Center"
                                                 Margin="8,4"/>
                                        
                                        <TextBlock Grid.Row="2" 
                                                 Text="3.50 ر.س" 
                                                 FontSize="16"
                                                 FontWeight="Bold"
                                                 Foreground="{StaticResource PrimaryBrush}"
                                                 TextAlignment="Center"
                                                 Margin="8,0"/>
                                        
                                        <TextBlock Grid.Row="3" 
                                                 Text="متوفر: 15" 
                                                 FontSize="12"
                                                 Foreground="{StaticResource TextSecondaryBrush}"
                                                 TextAlignment="Center"
                                                 Margin="8,0,8,8"/>
                                    </Grid>
                                </materialDesign:Card>
                            </ItemsControl.Items>
                        </ItemsControl>
                    </ScrollViewer>
                </Grid>
            </materialDesign:Card>

            <!-- Cart Section -->
            <materialDesign:Card Grid.Column="1" Style="{StaticResource CardStyle}" Margin="8,0,0,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Cart Header -->
                    <StackPanel Grid.Row="0" Margin="0,0,0,16">
                        <TextBlock Text="سلة التسوق" 
                                 FontSize="18" 
                                 FontWeight="Medium"/>
                        <TextBlock Text="0 منتج" 
                                 FontSize="12"
                                 Foreground="{StaticResource TextSecondaryBrush}"/>
                    </StackPanel>

                    <!-- Cart Items -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <StackPanel>
                            <TextBlock Text="السلة فارغة" 
                                     TextAlignment="Center"
                                     Foreground="{StaticResource TextSecondaryBrush}"
                                     Margin="0,32"/>
                        </StackPanel>
                    </ScrollViewer>

                    <!-- Totals -->
                    <StackPanel Grid.Row="2" Margin="0,16">
                        <Separator Margin="0,0,0,8"/>
                        
                        <Grid Margin="0,4">
                            <TextBlock Text="المجموع الفرعي:" HorizontalAlignment="Right"/>
                            <TextBlock Text="0.00 ر.س" HorizontalAlignment="Left"/>
                        </Grid>
                        
                        <Grid Margin="0,4">
                            <TextBlock Text="الضريبة:" HorizontalAlignment="Right"/>
                            <TextBlock Text="0.00 ر.س" HorizontalAlignment="Left"/>
                        </Grid>
                        
                        <Grid Margin="0,4">
                            <TextBlock Text="الخصم:" HorizontalAlignment="Right"/>
                            <TextBlock Text="0.00 ر.س" HorizontalAlignment="Left"/>
                        </Grid>
                        
                        <Separator Margin="0,8,0,4"/>
                        
                        <Grid Margin="0,4">
                            <TextBlock Text="الإجمالي:" 
                                     FontWeight="Bold" 
                                     FontSize="16"
                                     HorizontalAlignment="Right"/>
                            <TextBlock Text="0.00 ر.س" 
                                     FontWeight="Bold" 
                                     FontSize="16"
                                     Foreground="{StaticResource PrimaryBrush}"
                                     HorizontalAlignment="Left"/>
                        </Grid>
                    </StackPanel>

                    <!-- Action Buttons -->
                    <StackPanel Grid.Row="3">
                        <Button Style="{StaticResource PrimaryButtonStyle}"
                              Content="الدفع"
                              IsEnabled="False"
                              Margin="0,8,0,4">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="CreditCard" 
                                                           Width="16" 
                                                           Height="16" 
                                                           Margin="0,0,4,0"/>
                                    <TextBlock Text="الدفع"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>
                        
                        <Button Style="{StaticResource SecondaryButtonStyle}"
                              Content="مسح الكل"
                              IsEnabled="False"
                              Margin="0,4">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Delete" 
                                                           Width="16" 
                                                           Height="16" 
                                                           Margin="0,0,4,0"/>
                                    <TextBlock Text="مسح الكل"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>
        </Grid>
    </Grid>
</UserControl>
