<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير جرد المخزون - الفروع - {{ $warehouse->name }}</title>
    <style>
        @page {
            margin: 15mm 10mm 20mm 10mm;
            size: A4;
            @bottom-center {
                content: "صفحة " counter(page) " من " counter(pages);
                font-family: 'DejaVu Sans', sans-serif;
                font-size: 9px;
                color: #555;
            }
        }

        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                margin: 0;
                padding: 0;
            }
            .page-break {
                page-break-before: always;
            }
            .no-print {
                display: none;
            }
            .print-button {
                display: none;
            }
            .products-table {
                page-break-inside: avoid;
            }
            .products-table tr {
                page-break-inside: avoid;
            }
        }

        .print-button {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-family: Arial, sans-serif;
        }

        .print-button:hover {
            background: #0056b3;
        }

        body {
            font-family: 'DejaVu Sans', sans-serif;
            font-size: 11px;
            line-height: 1.3;
            color: #000;
            margin: 0;
            padding: 0;
            direction: rtl;
            background: white;
        }
        
        .header {
            position: relative;
            text-align: center;
            margin-bottom: 25px;
            border: 3px solid #000;
            padding: 15px;
            background-color: #f8f9fa;
        }

        .header h1 {
            font-size: 20px;
            font-weight: bold;
            margin: 0 0 8px 0;
            color: #000;
            text-transform: uppercase;
        }

        .header h2 {
            font-size: 16px;
            margin: 0;
            color: #333;
            font-weight: bold;
        }

        .logo {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 80px;
            height: 80px;
        }

        .company-info {
            margin-top: 10px;
            font-size: 12px;
        }

        .company-info p {
            margin: 5px 0;
            font-weight: bold;
        }

        .filter-info {
            background-color: #e8f4fd;
            border: 1px solid #bee5eb;
            padding: 8px;
            margin: 10px 0;
            border-radius: 4px;
            font-size: 11px;
        }
        
        .products-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 25px;
            font-size: 10px;
            border: 2px solid #000;
        }

        .products-table th {
            background-color: #000;
            color: white;
            padding: 8px 4px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #000;
            font-size: 10px;
        }

        .products-table td {
            padding: 6px 4px;
            text-align: center;
            border: 1px solid #000;
            vertical-align: middle;
            min-height: 25px;
        }

        .products-table tbody tr:nth-child(even) {
            background-color: #f5f5f5;
        }

        .product-name-cell {
            text-align: right;
            padding-right: 8px;
            font-weight: bold;
        }

        .barcode-cell {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            font-size: 9px;
            background-color: #fff;
            border: 2px solid #000;
            padding: 4px;
        }

        .current-quantity-cell {
            font-weight: bold;
            font-size: 11px;
            background-color: #e8f5e8;
        }

        .actual-quantity-cell {
            background-color: #fff;
            border: 2px solid #000;
            min-height: 35px;
            position: relative;
        }

        .signature-cell {
            background-color: #fff;
            border: 2px solid #000;
            min-height: 35px;
        }

        .signature-section {
            margin-top: 30px;
            border-top: 3px solid #000;
            padding-top: 15px;
        }

        .signature-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            border: 2px solid #000;
        }

        .signature-table td {
            padding: 12px 8px;
            text-align: center;
            border: 1px solid #000;
            vertical-align: top;
            height: 70px;
            background-color: #fff;
        }

        .signature-label {
            font-weight: bold;
            font-size: 12px;
            margin-bottom: 8px;
            color: #000;
        }

        .signature-line {
            margin: 15px 5px 8px 5px;
            border-bottom: 1px solid #000;
            height: 25px;
        }

        .signature-info {
            font-size: 10px;
            color: #000;
            font-weight: bold;
        }

        .page-break {
            page-break-before: always;
        }

        .no-products {
            text-align: center;
            padding: 30px;
            color: #000;
            border: 2px solid #000;
            background-color: #f8f9fa;
            margin: 20px 0;
        }

        .no-products h3 {
            font-size: 14px;
            margin-bottom: 10px;
            color: #000;
        }

        .no-products p {
            font-size: 12px;
            color: #333;
        }

        .report-footer {
            margin-top: 20px;
            text-align: center;
            font-size: 9px;
            color: #666;
            border-top: 1px solid #ccc;
            padding-top: 10px;
        }

        .branch-badge {
            background-color: #007bff;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: bold;
            display: inline-block;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <!-- Print Button -->
    <button class="print-button no-print" onclick="window.print()">🖨️ طباعة التقرير</button>

    <!-- Header -->
    <div class="header">
        @if(isset($companyLogo))
            {!! $companyLogo !!}
        @endif
        <h1>تقرير جرد المخزون - الفروع</h1>
        <span class="branch-badge">Branch Inventory Report</span>
        <h2>{{ $warehouse->name }}</h2>
        <div class="company-info">
            <p>التاريخ | Date: {{ $inventory_date ?? now()->format('Y-m-d') }}</p>
            <p>الوقت | Time: {{ $inventory_time ?? now()->format('H:i:s') }}</p>
            
            @if(isset($search) || isset($statusFilter))
                <div class="filter-info">
                    <strong>التصفية المطبقة | Applied Filters:</strong>
                    @if(isset($search) && $search)
                        البحث | Search: "{{ $search }}"
                    @endif
                    @if(isset($statusFilter) && $statusFilter)
                        @php
                            $statusNames = [
                                'out_of_stock' => 'غير متوفر | Out of Stock',
                                'low_stock' => 'منخفض | Low Stock',
                                'normal' => 'طبيعي | Normal'
                            ];
                        @endphp
                        الحالة | Status: {{ $statusNames[$statusFilter] ?? $statusFilter }}
                    @endif
                </div>
            @endif
            
            <p>الموظف المسؤول | Responsible Employee: {{ $user->name ?? 'غير محدد' }}</p>
        </div>
    </div>

    <!-- Products Table -->
    @if(isset($products) && count($products) > 0)
        <table class="products-table">
            <thead>
                <tr>
                    <th style="width: 8%;">#</th>
                    <th style="width: 45%;">اسم المنتج | Product Name</th>
                    <th style="width: 15%;">باركود | Barcode</th>
                    <th style="width: 12%;">الكمية الحالية | Current Qty</th>
                    <th style="width: 12%;">الكمية الفعلية | Actual Qty</th>
                    <th style="width: 8%;">التأشير | Check</th>
                </tr>
            </thead>
            <tbody>
                @foreach($products as $index => $product)
                <tr>
                    <td><strong>{{ $index + 1 }}</strong></td>
                    <td class="product-name-cell">{{ $product->name }}</td>
                    <td class="barcode-cell">{{ $product->sku }}</td>
                    <td class="current-quantity-cell">
                        <strong>{{ $product->current_quantity ?? $product->warehouse_quantity ?? 0 }}</strong>
                    </td>
                    <td class="actual-quantity-cell">
                        <div class="signature-line"></div>
                    </td>
                    <td class="signature-cell">
                        <div class="signature-line"></div>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    @else
        <div class="no-products">
            <h3>لا توجد منتجات متوفرة في هذا المستودع</h3>
            <p>جميع المنتجات في هذا المستودع لها كمية صفر أو غير موجودة.</p>
        </div>
    @endif

    <!-- Footer Section -->
    <div class="signature-section">
        <table class="signature-table">
            <tr>
                <td style="width: 33.33%;">
                    <div class="signature-label">اسم الموظف | Employee Name</div>
                    <div class="signature-line"></div>
                    <div class="signature-info">{{ $user->name ?? 'غير محدد' }}</div>
                </td>
                <td style="width: 33.33%;">
                    <div class="signature-label">رقم الموظف | Employee ID</div>
                    <div class="signature-line"></div>
                    <div class="signature-info">{{ isset($employee->employee_id) ? $employee->employee_id : 'غير محدد' }}</div>
                </td>
                <td style="width: 33.33%;">
                    <div class="signature-label">التوقيع | Signature</div>
                    <div class="signature-line"></div>
                    <div class="signature-info">التاريخ | Date: {{ $inventory_date ?? now()->format('Y-m-d') }}</div>
                </td>
            </tr>
            <tr>
                <td colspan="3" style="padding-top: 20px;">
                    <div class="signature-label">اسم المدقق | Auditor Name</div>
                    <div class="signature-line"></div>
                    <div style="margin-top: 10px;">
                        <span style="margin-left: 50px;">التاريخ | Date: _______________</span>
                        <span>التوقيع | Signature: _______________</span>
                    </div>
                </td>
            </tr>
        </table>

        <!-- Print Instructions -->
        <div class="report-footer">
            <p><strong>تعليمات | Instructions:</strong> يرجى ملء الكمية الفعلية بعد العد الفعلي للمخزون ووضع التأشير في العمود المخصص</p>
            <p>Please fill in the actual quantity after physical counting and mark in the designated column</p>
            <p>تم إنشاء هذا التقرير تلقائياً في {{ now()->format('Y-m-d H:i:s') }} | Generated automatically on {{ now()->format('Y-m-d H:i:s') }}</p>
        </div>
    </div>
</body>
</html>
