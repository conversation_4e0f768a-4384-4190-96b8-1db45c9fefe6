{{ Form::model($productService, array('route' => array('productservice.update', $productService->id), 'method' => 'PUT','enctype' => "multipart/form-data", 'class'=>'needs-validation', 'novalidate')) }}
<div class="modal-body">
    {{-- start for ai module--}}
    @php
        $plan= \App\Models\Utility::getChatGPTSettings();
    @endphp
    @if($plan->chatgpt == 1)
    <div class="text-end">
        <a href="#" data-size="md" class="btn  btn-primary btn-icon btn-sm" data-ajax-popup-over="true" data-url="{{ route('generate',['productservice']) }}"
           data-bs-placement="top" data-title="{{ __('Generate content with AI') }}">
            <i class="fas fa-robot"></i> <span>{{__('Generate with AI')}}</span>
        </a>
    </div>
    @endif
    {{-- end for ai module--}}
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                {{ Form::label('name', __('Name'),['class'=>'form-label']) }}<x-required></x-required>
                {{ Form::text('name',null, array('class' => 'form-control','required'=>'required', 'placeholder' => __('Enter Name'))) }}
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                {{ Form::label('sku', __('SKU'),['class'=>'form-label']) }}<x-required></x-required>
                {{ Form::text('sku', null, array('class' => 'form-control','required'=>'required', 'placeholder' => __('Enter Sku'))) }}
            </div>
        </div>

        <div class="col-md-6">
            <div class="form-group">
                {{ Form::label('sale_value_helper', __('Sale Value (Including VAT)'),['class'=>'form-label']) }}
                <small class="text-muted d-block">{{ __('Enter the total amount including VAT - the system will extract the VAT and show the price before VAT') }}</small>
                @php
                    // Calculate sale value including VAT from current sale price and taxes
                    $currentSalePrice = $productService->sale_price ?? 0;
                    $currentTaxRate = 0;
                    try {
                        if (!empty($productService->tax_id)) {
                            $currentTaxRate = \App\Models\Utility::totalTaxRate($productService->tax_id);
                        }
                    } catch (Exception $e) {
                        $currentTaxRate = 0;
                    }
                    // Calculate total amount including VAT: sale_price * (1 + tax_rate/100)
                    $saleValueIncludingVAT = $currentTaxRate > 0 ? $currentSalePrice * (1 + ($currentTaxRate / 100)) : $currentSalePrice;
                @endphp
                {{ Form::number('sale_value_helper', number_format($saleValueIncludingVAT, 2, '.', ''), array('class' => 'form-control','step'=>'0.01', 'placeholder' => __('Enter Total Amount Including VAT'), 'id' => 'sale_value_helper')) }}
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                {{ Form::label('sale_price', __('Sale Price (Before VAT)'),['class'=>'form-label']) }}<x-required></x-required>
                {{ Form::number('sale_price', null, array('class' => 'form-control','required'=>'required','step'=>'0.01', 'placeholder' => __('Enter Sale Price'), 'id' => 'sale_price')) }}
                <small class="text-muted">{{ __('This will be calculated automatically when you enter the total amount above') }}</small>
            </div>
        </div>
        <div class="form-group col-md-6">
            {{ Form::label('sale_chartaccount_id', __('Income Account'),['class'=>'form-label']) }}<x-required></x-required>
            {{-- {{ Form::select('sale_chartaccount_id',$incomeChartAccounts,null, array('class' => 'form-control select','required'=>'required')) }} --}}
            <select name="sale_chartaccount_id" class="form-control" required="required">
                @foreach ($incomeChartAccounts as $key => $chartAccount)
                    <option value="{{ $key }}" class="subAccount" {{ ($productService->sale_chartaccount_id == $key) ? 'selected' : ''}}>{{ $chartAccount }}</option>
                    @foreach ($incomeSubAccounts as $subAccount)
                        @if ($key == $subAccount['account'])
                            <option value="{{ $subAccount['id'] }}" class="ms-5" {{ ($productService->sale_chartaccount_id == $subAccount['id']) ? 'selected' : ''}}> &nbsp; &nbsp;&nbsp; {{ isset($subAccount['code_name']) ? $subAccount['code_name'] : '' }}</option>
                        @endif
                    @endforeach
                @endforeach
            </select>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                {{ Form::label('purchase_price', __('Purchase Price'),['class'=>'form-label']) }}<x-required></x-required>
                {{ Form::number('purchase_price', null, array('class' => 'form-control','required'=>'required','step'=>'0.01', 'placeholder' => __('Enter Purchase Price'))) }}
            </div>
        </div>
        <div class="form-group col-md-6">
            {{ Form::label('expense_chartaccount_id', __('Expense Account'),['class'=>'form-label']) }}
            {{-- {{ Form::select('expense_chartaccount_id',$expenseChartAccounts,null, array('class' => 'form-control select','required'=>'required')) }} --}}
            <select name="expense_chartaccount_id" class="form-control" required="required">
                @foreach ($expenseChartAccounts as $key => $chartAccount)
                    <option value="{{ $key }}" class="subAccount" {{ ($productService->expense_chartaccount_id == $key) ? 'selected' : ''}}>{{ $chartAccount }}</option>
                    @foreach ($expenseSubAccounts as $subAccount)
                        @if ($key == $subAccount['account'])
                            <option value="{{ $subAccount['id'] }}" class="ms-5" {{ ($productService->expense_chartaccount_id == $subAccount['id']) ? 'selected' : ''}}> &nbsp; &nbsp;&nbsp; {{ $subAccount['code_name'] }}</option>
                        @endif
                    @endforeach
                @endforeach
            </select>
        </div>

        <div class="form-group  col-md-6">
            {{ Form::label('tax_id', __('Tax'),['class'=>'form-label']) }}
            {{ Form::select('tax_id[]', $tax,explode(',',$productService->tax_id), array('class' => 'form-control choices','id'=>'choices-multiple1','multiple'=>'','placeholder' => __('Select Tax'))) }}
        </div>

        <div class="form-group  col-md-6">
            {{ Form::label('category_id', __('Category'),['class'=>'form-label']) }}<x-required></x-required>
            {{ Form::select('category_id', $category,null, array('class' => 'form-control select','required'=>'required')) }}
        </div>
        <div class="form-group  col-md-6">
            {{ Form::label('unit_id', __('Unit'),['class'=>'form-label']) }}<x-required></x-required>
            {{ Form::select('unit_id', $unit,null, array('class' => 'form-control select','required'=>'required')) }}
        </div>

        <div class="col-md-6 form-group">
            {{Form::label('pro_image',__('Product Image'),['class'=>'form-label'])}}
            <div class="choose-file ">
                <label for="pro_image" class="form-label">
                    <input type="file" class="form-control file-validate" name="pro_image" id="pro_image" data-filename="pro_image_create">
                <p id="" class="file-error text-danger"></p>
                    {{-- <img id="image"  class="mt-3" width="100" src="@if($productService->pro_image){{asset(Storage::url('uploads/pro_image/'.$productService->pro_image))}}@else{{asset(Storage::url('uploads/pro_image/user-2_1654779769.jpg'))}}@endif" /> --}}
                    <img id="image" class="mt-3" width="100" src="{{ $productService->pro_image ? \App\Models\Utility::get_file('uploads/pro_image/'.$productService->pro_image) : asset(Storage::url('uploads/pro_image/user-2_1654779769.jpg'))}}" />
                </label>
            </div>
        </div>



        <div class="col-md-6">
            <div class="form-group">
                <label class="d-block form-label">{{__('Type')}}</label>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check form-check-inline">
                            <input type="radio" class="form-check-input type" id="customRadio5" name="type" value="product" @if($productService->type=='product') checked @endif >
                            <label class="custom-control-label form-label" for="customRadio5">{{__('Product')}}</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check form-check-inline">
                            <input type="radio" class="form-check-input type" id="customRadio6" name="type" value="service" @if($productService->type=='service') checked @endif >
                            <label class="custom-control-label form-label" for="customRadio6">{{__('Service')}}</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-group col-md-6 quantity {{$productService->type=='service' ? 'd-none':''}}">
            {{ Form::label('quantity', __('Quantity'),['class'=>'form-label']) }}<x-required></x-required>
            {{ Form::text('quantity',null, array('class' => 'form-control','required'=>'required')) }}
        </div>
        <div class="form-group  col-md-12">
            {{ Form::label('description', __('Description'),['class'=>'form-label']) }}
            {!! Form::textarea('description', null, ['class'=>'form-control','rows'=>'2']) !!}
        </div>


    </div>
    @if(!$customFields->isEmpty())
        {{-- <div class="col-md-6"> --}}
            {{-- <div class="tab-pane fade show" id="tab-2" role="tabpanel"> --}}
                @include('customFields.formBuilder')
            {{-- </div> --}}
        {{-- </div> --}}
    @endif
</div>
<div class="modal-footer">
    <input type="button" value="{{__('Cancel')}}" class="btn  btn-secondary" data-bs-dismiss="modal">
    <input type="submit" value="{{__('Update')}}" class="btn  btn-primary">
</div>
{{Form::close()}}
<script>
    document.getElementById('pro_image').onchange = function () {
        var src = URL.createObjectURL(this.files[0])
        document.getElementById('image').src = src
    }

    //hide & show quantity

    $(document).on('click', '.type', function ()
    {
        var type = $(this).val();
        if (type == 'product') {
            $('.quantity').removeClass('d-none')
            $('.quantity').addClass('d-block');
            $('input[name="quantity"]').prop('required', true);
        } else {
            $('.quantity').addClass('d-none')
            $('.quantity').removeClass('d-block');
            $('input[name="quantity"]').val('').prop('required', false);
        }
    });

    // Calculate sale price (before VAT) from sale value helper (including VAT)
    function calculateSalePrice() {
        var saleValueHelper = parseFloat($('#sale_value_helper').val()) || 0;

        if (saleValueHelper <= 0) {
            $('#sale_price').val('');
            return;
        }

        var selectedTaxes = $('#choices-multiple1').val() || [];
        var totalTaxRate = 0;

        // Calculate total tax rate from selected taxes
        if (selectedTaxes.length > 0) {
            // Get tax rates via AJAX
            $.ajax({
                url: '{{ route("get.tax.rates") }}',
                type: 'POST',
                data: {
                    '_token': '{{ csrf_token() }}',
                    'tax_ids': selectedTaxes
                },
                success: function(response) {
                    if (response.success) {
                        totalTaxRate = response.total_rate || 0;

                        // Calculate sale price before VAT: total_amount / (1 + tax_rate/100)
                        // Example: 115 / (1 + 15/100) = 115 / 1.15 = 100
                        var salePriceBeforeVAT = saleValueHelper / (1 + (totalTaxRate / 100));
                        $('#sale_price').val(salePriceBeforeVAT.toFixed(2));
                    }
                },
                error: function() {
                    console.log('Error getting tax rates');
                    // If AJAX fails, just use the sale value as sale price
                    $('#sale_price').val(saleValueHelper.toFixed(2));
                }
            });
        } else {
            // No tax selected, sale price equals sale value (no VAT to extract)
            $('#sale_price').val(saleValueHelper.toFixed(2));
        }
    }

    // Calculate sale value (including VAT) from sale price (before VAT) - reverse calculation
    function calculateSaleValueFromPrice() {
        var salePrice = parseFloat($('#sale_price').val()) || 0;
        var selectedTaxes = $('#choices-multiple1').val() || [];

        if (salePrice <= 0) {
            $('#sale_value_helper').val('');
            return;
        }

        if (selectedTaxes.length > 0) {
            // Get tax rates via AJAX
            $.ajax({
                url: '{{ route("get.tax.rates") }}',
                type: 'POST',
                data: {
                    '_token': '{{ csrf_token() }}',
                    'tax_ids': selectedTaxes
                },
                success: function(response) {
                    if (response.success) {
                        var totalTaxRate = response.total_rate || 0;

                        // Calculate total amount including VAT: sale_price * (1 + tax_rate/100)
                        // Example: 100 * (1 + 15/100) = 100 * 1.15 = 115
                        var totalAmountIncludingVAT = salePrice * (1 + (totalTaxRate / 100));
                        $('#sale_value_helper').val(totalAmountIncludingVAT.toFixed(2));
                    }
                },
                error: function() {
                    console.log('Error getting tax rates for reverse calculation');
                }
            });
        } else {
            // No tax selected, total amount equals sale price (no VAT to add)
            $('#sale_value_helper').val(salePrice.toFixed(2));
        }
    }

    // Wait for modal to be shown and choices.js to be initialized
    $(document).ready(function() {
        // Wait a bit for modal to be fully loaded
        setTimeout(function() {
            // Trigger calculation when sale value helper changes
            $(document).on('input', '#sale_value_helper', function() {
                calculateSalePrice();
            });

            // Trigger reverse calculation when sale price changes manually
            $(document).on('input', '#sale_price', function() {
                // Only do reverse calculation if sale_value_helper is empty or user is editing sale_price directly
                if ($('#sale_value_helper').val() === '' || $(this).is(':focus')) {
                    calculateSaleValueFromPrice();
                }
            });

            // Trigger calculation when tax selection changes (for choices.js)
            $(document).on('change', '#choices-multiple1', function() {
                setTimeout(function() {
                    // If sale_value_helper has value, recalculate sale_price
                    if ($('#sale_value_helper').val() !== '') {
                        calculateSalePrice();
                    } else if ($('#sale_price').val() !== '') {
                        // If only sale_price has value, recalculate sale_value_helper
                        calculateSaleValueFromPrice();
                    }
                }, 200); // Delay to ensure choices.js has updated
            });
        }, 500); // Wait for modal to be fully loaded
    });
</script>

