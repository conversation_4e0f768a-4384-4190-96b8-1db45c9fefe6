<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CustomerSegment extends Model
{
    protected $fillable = [
        'customer_id',
        'segment_type',
        'rfm_score',
        'total_spent',
        'purchase_frequency',
        'last_purchase_date',
        'predicted_next_purchase',
        'average_order_value',
        'days_since_last_purchase',
        'created_by',
    ];

    protected $casts = [
        'total_spent' => 'decimal:2',
        'average_order_value' => 'decimal:2',
        'last_purchase_date' => 'date',
        'predicted_next_purchase' => 'date',
    ];

    /**
     * أنواع التصنيفات المتاحة
     */
    public static $segmentTypes = [
        'VIP' => 'عميل مميز',
        'Regular' => 'عميل منتظم',
        'New' => 'عميل جديد',
        'Inactive' => 'عميل غير نشط',
    ];

    /**
     * العلاقة مع العميل
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * العلاقة مع المستخدم المنشئ
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * الحصول على اسم التصنيف بالعربية
     */
    public function getSegmentNameAttribute(): string
    {
        return self::$segmentTypes[$this->segment_type] ?? $this->segment_type;
    }

    /**
     * الحصول على لون التصنيف
     */
    public function getSegmentColorAttribute(): string
    {
        return match($this->segment_type) {
            'VIP' => 'success',
            'Regular' => 'primary',
            'New' => 'info',
            'Inactive' => 'secondary',
            default => 'light',
        };
    }

    /**
     * تحديد ما إذا كان العميل نشطاً
     */
    public function getIsActiveAttribute(): bool
    {
        return $this->segment_type !== 'Inactive' && $this->days_since_last_purchase <= 90;
    }

    /**
     * فلترة العملاء حسب نوع التصنيف
     */
    public function scopeOfType($query, $segmentType)
    {
        return $query->where('segment_type', $segmentType);
    }

    /**
     * فلترة العملاء النشطين
     */
    public function scopeActive($query)
    {
        return $query->where('segment_type', '!=', 'Inactive')
                    ->where('days_since_last_purchase', '<=', 90);
    }

    /**
     * فلترة العملاء غير النشطين
     */
    public function scopeInactive($query)
    {
        return $query->where('segment_type', 'Inactive')
                    ->orWhere('days_since_last_purchase', '>', 90);
    }

    /**
     * فلترة العملاء حسب المنشئ
     */
    public function scopeForCreator($query, $creatorId)
    {
        return $query->where('created_by', $creatorId);
    }

    /**
     * ترتيب حسب إجمالي الإنفاق (الأعلى أولاً)
     */
    public function scopeTopSpenders($query)
    {
        return $query->orderBy('total_spent', 'desc');
    }

    /**
     * ترتيب حسب تكرار الشراء (الأعلى أولاً)
     */
    public function scopeFrequentBuyers($query)
    {
        return $query->orderBy('purchase_frequency', 'desc');
    }

    /**
     * حساب نقاط RFM للعميل
     */
    public static function calculateRFMScore($recency, $frequency, $monetary): int
    {
        // تحويل القيم إلى نقاط من 1-5
        $recencyScore = self::getRecencyScore($recency);
        $frequencyScore = self::getFrequencyScore($frequency);
        $monetaryScore = self::getMonetaryScore($monetary);

        // حساب النقاط الإجمالية
        return ($recencyScore * 100) + ($frequencyScore * 10) + $monetaryScore;
    }

    /**
     * حساب نقاط الحداثة (Recency)
     */
    private static function getRecencyScore($daysSinceLastPurchase): int
    {
        if ($daysSinceLastPurchase <= 30) return 5;
        if ($daysSinceLastPurchase <= 60) return 4;
        if ($daysSinceLastPurchase <= 90) return 3;
        if ($daysSinceLastPurchase <= 180) return 2;
        return 1;
    }

    /**
     * حساب نقاط التكرار (Frequency)
     */
    private static function getFrequencyScore($purchaseFrequency): int
    {
        if ($purchaseFrequency >= 20) return 5;
        if ($purchaseFrequency >= 10) return 4;
        if ($purchaseFrequency >= 5) return 3;
        if ($purchaseFrequency >= 2) return 2;
        return 1;
    }

    /**
     * حساب نقاط القيمة النقدية (Monetary)
     */
    private static function getMonetaryScore($totalSpent): int
    {
        if ($totalSpent >= 10000) return 5;
        if ($totalSpent >= 5000) return 4;
        if ($totalSpent >= 2000) return 3;
        if ($totalSpent >= 500) return 2;
        return 1;
    }

    /**
     * تحديد نوع التصنيف بناءً على نقاط RFM
     */
    public static function determineSegmentType($rfmScore): string
    {
        if ($rfmScore >= 444) return 'VIP';
        if ($rfmScore >= 333) return 'Regular';
        if ($rfmScore >= 222) return 'New';
        return 'Inactive';
    }
}
