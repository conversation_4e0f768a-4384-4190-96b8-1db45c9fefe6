<Application x:Class="POS_Desktop_App.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="Views/MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="DeepPurple" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                
                <!-- Custom Styles -->
                <ResourceDictionary>
                    <!-- Primary Colors -->
                    <SolidColorBrush x:Key="PrimaryBrush" Color="#673AB7"/>
                    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="#512DA8"/>
                    <SolidColorBrush x:Key="AccentBrush" Color="#CDDC39"/>
                    
                    <!-- Background Colors -->
                    <SolidColorBrush x:Key="BackgroundBrush" Color="#FAFAFA"/>
                    <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFF"/>
                    
                    <!-- Text Colors -->
                    <SolidColorBrush x:Key="TextPrimaryBrush" Color="#212121"/>
                    <SolidColorBrush x:Key="TextSecondaryBrush" Color="#757575"/>
                    
                    <!-- Success/Error Colors -->
                    <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
                    <SolidColorBrush x:Key="ErrorBrush" Color="#F44336"/>
                    <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
                    
                    <!-- Card Style -->
                    <Style x:Key="CardStyle" TargetType="materialDesign:Card">
                        <Setter Property="Margin" Value="8"/>
                        <Setter Property="Padding" Value="16"/>
                        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
                    </Style>
                    
                    <!-- Button Styles -->
                    <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
                        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                        <Setter Property="Foreground" Value="White"/>
                        <Setter Property="Margin" Value="4"/>
                        <Setter Property="Padding" Value="16,8"/>
                        <Setter Property="FontWeight" Value="Medium"/>
                    </Style>
                    
                    <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
                        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
                        <Setter Property="Margin" Value="4"/>
                        <Setter Property="Padding" Value="16,8"/>
                    </Style>
                    
                    <!-- TextBox Style -->
                    <Style x:Key="MaterialTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
                        <Setter Property="Margin" Value="4"/>
                        <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
                        <Setter Property="FontSize" Value="14"/>
                    </Style>
                    
                    <!-- ComboBox Style -->
                    <Style x:Key="MaterialComboBoxStyle" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
                        <Setter Property="Margin" Value="4"/>
                        <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
                        <Setter Property="FontSize" Value="14"/>
                    </Style>
                    
                    <!-- DataGrid Style -->
                    <Style x:Key="MaterialDataGridStyle" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
                        <Setter Property="AutoGenerateColumns" Value="False"/>
                        <Setter Property="CanUserAddRows" Value="False"/>
                        <Setter Property="CanUserDeleteRows" Value="False"/>
                        <Setter Property="IsReadOnly" Value="True"/>
                        <Setter Property="SelectionMode" Value="Single"/>
                        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
                        <Setter Property="HeadersVisibility" Value="Column"/>
                        <Setter Property="Margin" Value="8"/>
                    </Style>
                    
                    <!-- Icon Styles -->
                    <Style x:Key="IconStyle" TargetType="materialDesign:PackIcon">
                        <Setter Property="Width" Value="24"/>
                        <Setter Property="Height" Value="24"/>
                        <Setter Property="VerticalAlignment" Value="Center"/>
                        <Setter Property="HorizontalAlignment" Value="Center"/>
                    </Style>
                    
                    <!-- Progress Bar Style -->
                    <Style x:Key="MaterialProgressBarStyle" TargetType="ProgressBar" BasedOn="{StaticResource MaterialDesignLinearProgressBar}">
                        <Setter Property="Height" Value="4"/>
                        <Setter Property="Margin" Value="8,4"/>
                    </Style>
                    
                    <!-- Snackbar Style -->
                    <Style x:Key="MaterialSnackbarStyle" TargetType="materialDesign:Snackbar">
                        <Setter Property="MessageQueue" Value="{materialDesign:MessageQueue}"/>
                    </Style>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
