@extends('layouts.admin')

@section('page-title')
    {{ __('التسعير - نسخة مبسطة') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item">{{ __('التسعير') }}</li>
@endsection

@section('content')
    <!-- جدول المنتجات -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('قائمة المنتجات والخدمات - نسخة مبسطة') }}</h5>
                    <small class="text-muted">{{ __('يمكنك النقر على أي خلية لتعديلها مباشرة') }}</small>
                </div>
                <div class="card-body table-border-style">
                    <div class="table-responsive">
                        <table class="table" id="simple-pricing-table">
                            <thead>
                                <tr>
                                    <th>{{ __('الاسم') }}</th>
                                    <th>{{ __('SKU') }}</th>
                                    <th>{{ __('سعر البيع') }}</th>
                                    <th>{{ __('سعر الشراء') }}</th>
                                    <th>{{ __('الكمية') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($products as $product)
                                    <tr data-product-id="{{ $product->id }}">
                                        <!-- الاسم -->
                                        <td class="editable" data-field="name" data-type="text">
                                            {{ $product->name }}
                                        </td>
                                        
                                        <!-- SKU -->
                                        <td class="editable" data-field="sku" data-type="text">
                                            {{ $product->sku }}
                                        </td>
                                        
                                        <!-- سعر البيع -->
                                        <td class="editable" data-field="sale_price" data-type="number">
                                            {{ Auth::user()->priceFormat($product->sale_price) }}
                                        </td>
                                        
                                        <!-- سعر الشراء -->
                                        <td class="editable" data-field="purchase_price" data-type="number">
                                            {{ Auth::user()->priceFormat($product->purchase_price) }}
                                        </td>
                                        
                                        <!-- الكمية -->
                                        <td class="editable" data-field="quantity" data-type="number">
                                            {{ number_format($product->quantity, 2) }}
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
<script>
console.log('Loading simple pricing page...');

$(document).ready(function() {
    console.log('Document ready - simple version');
    
    // التعديل المباشر - نسخة مبسطة
    $('.editable').on('click', function(e) {
        console.log('Clicked on editable cell');
        
        var $cell = $(this);
        var field = $cell.data('field');
        var type = $cell.data('type');
        var currentValue = $cell.text().trim();
        var productId = $cell.closest('tr').data('product-id');
        
        console.log('Cell data:', {field, type, currentValue, productId});
        
        if ($cell.hasClass('editing')) {
            console.log('Already editing');
            return;
        }
        
        $cell.addClass('editing');
        
        // إنشاء input للتعديل
        var inputType = type === 'number' ? 'number' : 'text';
        var editValue = currentValue;
        
        if (type === 'number') {
            editValue = currentValue.replace(/[^\d.-]/g, '');
        }
        
        var $input = $('<input>', {
            type: inputType,
            class: 'form-control form-control-sm',
            value: editValue,
            step: type === 'number' ? '0.01' : '',
            min: type === 'number' ? '0' : ''
        });
        
        $cell.html($input);
        $input.focus().select();
        
        // حفظ عند Enter أو فقدان التركيز
        $input.on('blur keypress', function(e) {
            if (e.type === 'blur' || e.which === 13) {
                saveEdit($cell, field, productId, $input.val(), currentValue);
            }
        });
        
        // إلغاء عند Escape
        $input.on('keypress', function(e) {
            if (e.which === 27) {
                $cell.html(currentValue).removeClass('editing');
            }
        });
    });
    
    // دالة الحفظ
    function saveEdit($cell, field, productId, newValue, oldValue) {
        console.log('Saving edit:', {field, productId, newValue, oldValue});
        
        if (newValue === '' || newValue === oldValue) {
            $cell.html(oldValue).removeClass('editing');
            return;
        }
        
        // إظهار مؤشر التحميل
        $cell.html('<i class="fa fa-spinner fa-spin"></i>');
        
        $.post('{{ route("pricing.update.inline") }}', {
            _token: '{{ csrf_token() }}',
            id: productId,
            field: field,
            value: newValue
        })
        .done(function(response) {
            console.log('Save response:', response);
            if (response.success) {
                $cell.html(response.display_value);
                showAlert('success', response.message);
            } else {
                $cell.html(oldValue);
                showAlert('error', response.message);
            }
        })
        .fail(function(xhr) {
            console.error('Save failed:', xhr);
            $cell.html(oldValue);
            var message = xhr.responseJSON ? xhr.responseJSON.message : 'حدث خطأ غير متوقع';
            showAlert('error', message);
        })
        .always(function() {
            $cell.removeClass('editing');
        });
    }
    
    // عرض التنبيهات
    function showAlert(type, message) {
        var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                       message +
                       '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                       '</div>';
        
        $('.card-body').first().prepend(alertHtml);
        
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 3000);
    }
});
</script>

<style>
.editable {
    cursor: pointer;
    position: relative;
    padding: 8px;
    border: 1px solid transparent;
}

.editable:hover {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
}

.editable.editing {
    background-color: #fff3cd;
}

.editable::after {
    content: '✏️';
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0;
    transition: opacity 0.2s;
    font-size: 12px;
}

.editable:hover::after {
    opacity: 1;
}

.editable.editing::after {
    display: none;
}
</style>
@endpush
