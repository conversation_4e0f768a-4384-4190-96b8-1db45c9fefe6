# تحسينات آلية حذف المنتجات من السلة في نظام POS

## 🔧 المشكلة الأصلية:
- عند حذف منتج من السلة، كان النظام يقوم بإعادة تحميل الصفحة بالكامل
- هذا يسبب فقدان التركيز وتحديث غير مرغوب فيه للسلة
- استخدام نظام `bs-pass-para-pos` الذي يرسل form مباشرة

## ✅ الحلول المطبقة:

### 1. تحديث زر الحذف في HTML:
```html
<!-- الكود القديم -->
<a href="#" class="btn btn-sm bg-danger bs-pass-para-pos"
   data-confirm="Are You Sure?"
   data-text="This action can not be undone. Do you want to continue?"
   data-confirm-yes="delete-form-{{ $id }}"
   title="Delete" data-id="{{ $id }}">
   <i class="ti ti-trash text-white"></i>
</a>
{!! Form::open(['method' => 'delete', 'url' => ['remove-from-cart'], 'id' => 'delete-form-' . $id]) !!}
<!-- Form fields -->
{!! Form::close() !!}

<!-- الكود الجديد -->
<a href="#" class="btn btn-sm bg-danger remove-from-cart-ajax"
   data-id="{{ $id }}"
   data-session-key="{{ $lastsegment }}"
   title="Delete">
   <i class="ti ti-trash text-white"></i>
</a>
```

### 2. تحديث JavaScript لاستخدام AJAX:
```javascript
// الكود الجديد المحسن
$(document).on('click', '.remove-from-cart-ajax', function (e) {
    e.preventDefault();

    var ele = $(this);
    var productId = ele.data('id');
    var sessionKey = ele.data('session-key');

    if (confirm('Are you sure you want to remove this product from cart?')) {
        
        // إضافة مؤشر تحميل
        var originalHtml = ele.html();
        ele.html('<span class="spinner-border spinner-border-sm"></span>').prop('disabled', true);

        $.ajax({
            url: '/remove-from-cart',
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            data: {
                id: productId,
                session_key: sessionKey
            },
            success: function (response) {
                // إزالة المنتج مع تأثير بصري
                $('#product-id-' + productId).fadeOut(300, function() {
                    $(this).remove();
                    
                    // فحص إذا كانت السلة فارغة
                    if ($('#tbody tr:not(.no-found)').length === 0) {
                        $('#tbody').html('<tr class="text-center no-found"><td colspan="7">No Data Found.!</td></tr>');
                        $('#btn-pur button').attr('disabled', 'disabled');
                        $('.btn-empty button').removeClass('btn-clear-cart');
                    } else {
                        // إعادة حساب المجاميع للمنتجات المتبقية
                        recalculateCartTotals();
                    }
                    
                    checkPaymentButtonStatus();
                });

                show_toastr('success', 'Product removed from cart successfully!', 'success');
            },
            error: function (xhr) {
                // إعادة الزر لحالته الأصلية في حالة الخطأ
                ele.html(originalHtml).prop('disabled', false);
                show_toastr('error', 'Error occurred while removing product', 'error');
            }
        });
    }
});
```

### 3. تحديث Controller لدعم AJAX:
```php
public function removeFromCart(Request $request)
{
    $id = $request->id;
    $session_key = $request->session_key;

    if (Auth::user()->can('manage product & service') && isset($id) && !empty($id) && isset($session_key) && !empty($session_key)) {
        $cart = session()->get($session_key);

        if (isset($cart[$id])) {
            $productName = $cart[$id]['name'] ?? 'Product';
            
            unset($cart[$id]);
            session()->put($session_key, $cart);

            // إرجاع استجابة JSON للطلبات AJAX
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'code' => 200,
                    'status' => 'Success',
                    'success' => $productName . ' removed from cart successfully!',
                    'remaining_items' => count($cart),
                    'cart' => $cart
                ]);
            }

            return redirect()->back()->with('success', __('Product removed from cart!'));
        }
    }
    
    // Handle errors...
}
```

### 4. دالة إعادة حساب المجاميع:
```javascript
function recalculateCartTotals() {
    var totalTax = 0;
    var subtotalWithTax = 0;

    // حساب المجاميع من المنتجات الموجودة في الجدول
    $('#tbody tr:not(.no-found)').each(function() {
        var row = $(this);
        var quantity = parseFloat(row.find('input[name="quantity"]').val()) || 0;
        var priceText = row.find('.price').text().replace(/[^\d.-]/g, '');
        var price = parseFloat(priceText) || 0;
        
        var itemSubtotal = price * quantity;
        
        // حساب الضريبة
        var taxBadges = row.find('.badge');
        var taxRate = 0;
        taxBadges.each(function() {
            var badgeText = $(this).text();
            var match = badgeText.match(/\((\d+(?:\.\d+)?)%\)/);
            if (match) {
                taxRate += parseFloat(match[1]);
            }
        });
        
        var itemTax = 0;
        if (taxRate > 0) {
            itemTax = (itemSubtotal * taxRate) / (100 + taxRate);
        }
        
        totalTax += itemTax;
        subtotalWithTax += itemSubtotal;
        
        row.find('.subtotal').text(addCommas(itemSubtotal));
    });

    // تحديث المجاميع في أسفل الصفحة
    $('#displaytotal').text(addCommas(subtotalWithTax));
    $('.subtotalamount').text(addCommas(subtotalWithTax));
    $('.taxamount').text(addCommas(totalTax));
    $('.totalamount').text(addCommas(subtotalWithTax));
}
```

## 🎨 التحسينات البصرية:

### CSS للزر المحسن:
```css
.remove-from-cart-ajax {
    background: linear-gradient(135deg, #FF6B6B 0%, #EE5A52 100%) !important;
    border: none !important;
    border-radius: 8px !important;
    padding: 8px 12px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3) !important;
}

.remove-from-cart-ajax:hover {
    background: linear-gradient(135deg, #EE5A52 0%, #DC3545 100%) !important;
    transform: translateY(-2px) scale(1.05) !important;
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4) !important;
}

.remove-from-cart-ajax:disabled {
    background: linear-gradient(135deg, #cccccc 0%, #**********%) !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
}
```

## 🚀 المزايا الجديدة:

1. **لا إعادة تحميل للصفحة**: حذف سلس بدون تحديث الصفحة
2. **تأثيرات بصرية**: fadeOut للمنتج المحذوف
3. **مؤشر تحميل**: spinner أثناء عملية الحذف
4. **إعادة حساب تلقائية**: للمجاميع والضرائب
5. **معالجة أخطاء محسنة**: رسائل خطأ واضحة
6. **تحقق من السلة الفارغة**: إدارة حالة السلة الفارغة
7. **تحديث حالة الأزرار**: تفعيل/تعطيل أزرار الدفع حسب الحاجة

## 📝 ملاحظات مهمة:

- تم الحفاظ على التوافق مع النظام الحالي
- يدعم كل من AJAX والطلبات العادية
- معالجة شاملة للأخطاء
- تحسينات بصرية متقدمة
- كود منظم وقابل للصيانة
