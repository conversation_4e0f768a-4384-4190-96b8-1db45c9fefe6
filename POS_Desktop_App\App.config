<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  </configSections>

  <!-- Connection Strings -->
  <connectionStrings>
    <!-- قاعدة البيانات الرئيسية على السيرفر - MySQL -->
    <add name="DefaultConnection" 
         connectionString="Server=localhost;Database=pos_database;Uid=root;Pwd=;CharSet=utf8mb4;SslMode=none;" 
         providerName="MySql.Data.MySqlClient" />
    
    <!-- قاعدة البيانات المحلية - SQLite -->
    <add name="LocalCacheConnection" 
         connectionString="Data Source=|DataDirectory|\pos_local_cache.db;Version=3;" 
         providerName="System.Data.SQLite" />
  </connectionStrings>

  <!-- App Settings -->
  <appSettings>
    <!-- إعدادات السيرفر -->
    <add key="ServerUrl" value="http://localhost:8000" />
    <add key="ApiBaseUrl" value="http://localhost:8000/api" />
    <add key="ApiTimeout" value="30" />
    
    <!-- إعدادات المزامنة -->
    <add key="AutoSync" value="true" />
    <add key="SyncInterval" value="30" />
    <add key="MaxRetryCount" value="3" />
    <add key="SyncTimeout" value="60" />
    
    <!-- إعدادات التخزين المؤقت -->
    <add key="CacheExpiry" value="24" />
    <add key="MaxCacheSize" value="100" />
    <add key="AutoCleanCache" value="true" />
    
    <!-- إعدادات النسخ الاحتياطي -->
    <add key="AutoBackup" value="true" />
    <add key="BackupInterval" value="1440" />
    <add key="MaxBackupFiles" value="7" />
    <add key="BackupPath" value="|DataDirectory|\Backups" />
    
    <!-- إعدادات الطباعة -->
    <add key="DefaultPrinter" value="" />
    <add key="ThermalPrintEnabled" value="false" />
    <add key="PrinterTimeout" value="10" />
    <add key="AutoPrint" value="false" />
    
    <!-- إعدادات الواجهة -->
    <add key="Theme" value="Light" />
    <add key="Language" value="ar" />
    <add key="Currency" value="SAR" />
    <add key="CurrencySymbol" value="ر.س" />
    
    <!-- إعدادات الأمان -->
    <add key="EncryptLocalData" value="true" />
    <add key="SessionTimeout" value="480" />
    <add key="AutoLock" value="false" />
    <add key="RequireAuthentication" value="true" />
    
    <!-- إعدادات السجلات -->
    <add key="EnableLogging" value="true" />
    <add key="LogLevel" value="Info" />
    <add key="LogPath" value="|DataDirectory|\Logs" />
    <add key="MaxLogFiles" value="30" />
    
    <!-- إعدادات الأداء -->
    <add key="MaxConcurrentOperations" value="5" />
    <add key="DatabasePoolSize" value="10" />
    <add key="CacheMemoryLimit" value="50" />
    
    <!-- إعدادات التطبيق -->
    <add key="AppName" value="نظام نقاط البيع" />
    <add key="AppVersion" value="1.0.0" />
    <add key="CompanyName" value="شركتك" />
    <add key="SupportEmail" value="<EMAIL>" />
    
    <!-- إعدادات الشبكة -->
    <add key="ConnectionTimeout" value="30" />
    <add key="RetryDelay" value="5" />
    <add key="MaxConcurrentConnections" value="3" />
    <add key="UseProxy" value="false" />
    <add key="ProxyAddress" value="" />
    <add key="ProxyPort" value="8080" />
    
    <!-- إعدادات التحديث -->
    <add key="CheckForUpdates" value="true" />
    <add key="UpdateUrl" value="" />
    <add key="AutoUpdate" value="false" />
    
    <!-- إعدادات التصدير -->
    <add key="ExportPath" value="|DataDirectory|\Exports" />
    <add key="ExportFormat" value="Excel" />
    <add key="AutoExport" value="false" />
    
    <!-- إعدادات الباركود -->
    <add key="BarcodeEnabled" value="true" />
    <add key="BarcodePrefix" value="" />
    <add key="BarcodeSuffix" value="" />
    <add key="BarcodeLength" value="13" />
    
    <!-- إعدادات الضرائب -->
    <add key="DefaultTaxRate" value="15" />
    <add key="TaxIncluded" value="true" />
    <add key="TaxCalculationMethod" value="Inclusive" />
    
    <!-- إعدادات العملة -->
    <add key="DecimalPlaces" value="2" />
    <add key="RoundingMethod" value="Round" />
    <add key="ShowCurrencySymbol" value="true" />
    
    <!-- إعدادات الفواتير -->
    <add key="InvoiceNumberPrefix" value="INV-" />
    <add key="InvoiceNumberLength" value="6" />
    <add key="AutoGenerateInvoiceNumber" value="true" />
    
    <!-- إعدادات العملاء -->
    <add key="RequireCustomerSelection" value="false" />
    <add key="DefaultCustomerName" value="عميل عادي" />
    <add key="AllowCreditSales" value="true" />
    
    <!-- إعدادات المخزون -->
    <add key="CheckStockBeforeSale" value="true" />
    <add key="AllowNegativeStock" value="false" />
    <add key="LowStockWarning" value="true" />
    <add key="LowStockThreshold" value="10" />
    
    <!-- إعدادات الخصومات -->
    <add key="AllowDiscounts" value="true" />
    <add key="MaxDiscountPercentage" value="50" />
    <add key="RequireDiscountApproval" value="false" />
    
    <!-- إعدادات الدفع -->
    <add key="AllowCashPayment" value="true" />
    <add key="AllowCardPayment" value="true" />
    <add key="AllowSplitPayment" value="true" />
    <add key="RequirePaymentBeforePrint" value="true" />
    
    <!-- إعدادات الشفتات -->
    <add key="RequireShiftManagement" value="true" />
    <add key="AutoCloseShift" value="false" />
    <add key="ShiftDuration" value="8" />
    
    <!-- إعدادات التقارير -->
    <add key="DefaultReportPeriod" value="Daily" />
    <add key="AutoGenerateReports" value="false" />
    <add key="ReportPath" value="|DataDirectory|\Reports" />
  </appSettings>

  <!-- Startup Settings -->
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
  </startup>

  <!-- Entity Framework -->
  <entityFramework>
    <providers>
      <provider invariantName="MySql.Data.MySqlClient" type="MySql.Data.MySqlClient.MySqlProviderServices, MySql.Data.EntityFramework, Version=********, Culture=neutral, PublicKeyToken=c5687fc88969c44d" />
      <provider invariantName="System.Data.SQLite.EF6" type="System.Data.SQLite.EF6.SQLiteProviderServices, System.Data.SQLite.EF6" />
    </providers>
  </entityFramework>

  <!-- System.Data -->
  <system.data>
    <DbProviderFactories>
      <remove invariant="MySql.Data.MySqlClient" />
      <add name="MySQL Data Provider" invariant="MySql.Data.MySqlClient" description=".Net Framework Data Provider for MySQL" type="MySql.Data.MySqlClient.MySqlClientFactory, MySql.Data, Version=********, Culture=neutral, PublicKeyToken=c5687fc88969c44d" />
      <remove invariant="System.Data.SQLite.EF6" />
      <add name="SQLite Data Provider (Entity Framework 6)" invariant="System.Data.SQLite.EF6" description=".NET Framework Data Provider for SQLite (Entity Framework 6)" type="System.Data.SQLite.EF6.SQLiteProviderFactory, System.Data.SQLite.EF6" />
    </DbProviderFactories>
  </system.data>

  <!-- Runtime -->
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
