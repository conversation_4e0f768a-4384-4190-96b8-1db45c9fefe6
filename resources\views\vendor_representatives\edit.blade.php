{{ Form::model($representative, ['route' => ['vendor.representatives.update', $representative->id], 'method' => 'PUT', 'enctype' => 'multipart/form-data']) }}
<div class="modal-body">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex align-items-center mb-3">
                <div class="theme-avtar bg-warning me-3">
                    <i class="ti ti-edit"></i>
                </div>
                <div>
                    <h5 class="mb-0">{{ __('تعديل بيانات المندوب') }}</h5>
                    <small class="text-muted">{{ __('تعديل بيانات المندوب') }}: <strong>{{ $representative->name }}</strong></small>
                </div>
            </div>
        </div>
    </div>

    <!-- Form Fields -->
    <div class="row">
        <!-- Personal Information Section -->
        <div class="col-12">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">
                        <i class="ti ti-user me-2"></i>{{ __('المعلومات الشخصية') }}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- اسم المندوب -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="ti ti-user me-1 text-primary"></i>{{ __('اسم المندوب') }}
                                <span class="text-danger">*</span>
                            </label>
                            {{ Form::text('name', null, [
                                'class' => 'form-control form-control-lg',
                                'required' => 'required',
                                'placeholder' => __('أدخل اسم المندوب بالكامل'),
                                'style' => 'border-radius: 10px; border: 2px solid #e9ecef;'
                            ]) }}
                            <small class="text-muted">{{ __('مثال: أحمد محمد علي') }}</small>
                        </div>

                        <!-- رقم الهاتف -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="ti ti-phone me-1 text-success"></i>{{ __('رقم الهاتف') }}
                                <span class="text-danger">*</span>
                            </label>
                            {{ Form::text('phone', null, [
                                'class' => 'form-control form-control-lg',
                                'required' => 'required',
                                'placeholder' => __('أدخل رقم الهاتف'),
                                'style' => 'border-radius: 10px; border: 2px solid #e9ecef;',
                                'pattern' => '[0-9+\-\s()]*',
                                'title' => 'يرجى إدخال رقم هاتف صحيح'
                            ]) }}
                            <small class="text-muted">{{ __('مثال: 0501234567 أو +966501234567') }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Business Information Section -->
        <div class="col-12">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="ti ti-building-store me-2"></i>{{ __('معلومات العمل') }}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- الشركة الموردة -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="ti ti-building me-1 text-info"></i>{{ __('الشركة الموردة') }}
                                <span class="text-danger">*</span>
                            </label>
                            <select name="vendor_id" class="form-control form-control-lg select2-vendor" required style="border-radius: 10px; border: 2px solid #e9ecef;">
                                <option value="">{{ __('اختر الشركة الموردة') }}</option>
                                @foreach($vendors as $vendor)
                                    <option value="{{ $vendor->id }}" {{ $representative->vendor_id == $vendor->id ? 'selected' : '' }}>
                                        {{ $vendor->name }}
                                    </option>
                                @endforeach
                            </select>
                            <small class="text-muted">{{ __('اختر الشركة التي يعمل معها المندوب') }}</small>
                        </div>

                        <!-- فئة المنتجات -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="ti ti-category me-1 text-warning"></i>{{ __('فئة المنتجات') }}
                                <span class="text-danger">*</span>
                            </label>
                            <select name="category_id" class="form-control form-control-lg select2-category" required style="border-radius: 10px; border: 2px solid #e9ecef;">
                                <option value="">{{ __('اختر فئة المنتجات') }}</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}" {{ $representative->category_id == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            </select>
                            <small class="text-muted">{{ __('اختر فئة المنتجات التي يختص بها المندوب') }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Section -->
        <div class="col-12">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="ti ti-settings me-2"></i>{{ __('الإعدادات والملاحظات') }}
                    </h6>
                </div>
                <div class="card-body">
                    <!-- الحالة -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="form-check form-switch form-check-lg">
                                {{ Form::checkbox('is_active', 1, null, [
                                    'class' => 'form-check-input',
                                    'id' => 'is_active',
                                    'style' => 'transform: scale(1.5);'
                                ]) }}
                                <label class="form-check-label fw-bold ms-2" for="is_active">
                                    <i class="ti ti-toggle-right me-1 text-success"></i>{{ __('المندوب نشط') }}
                                </label>
                                <br>
                                <small class="text-muted ms-4">{{ __('عند التفعيل، سيكون المندوب متاحاً للعمل والتواصل') }}</small>
                            </div>
                        </div>
                    </div>

                    <!-- الملاحظات -->
                    <div class="row">
                        <div class="col-12">
                            <label class="form-label fw-bold">
                                <i class="ti ti-notes me-1 text-secondary"></i>{{ __('الملاحظات') }}
                                <span class="badge bg-secondary ms-1">{{ __('اختياري') }}</span>
                            </label>
                            {{ Form::textarea('notes', null, [
                                'class' => 'form-control',
                                'rows' => 4,
                                'placeholder' => __('أدخل أي ملاحظات إضافية حول المندوب (مثل: ساعات العمل، المناطق المختصة، إلخ...)'),
                                'style' => 'border-radius: 10px; border: 2px solid #e9ecef; resize: vertical;'
                            ]) }}
                            <small class="text-muted">{{ __('يمكنك إضافة أي معلومات إضافية مفيدة حول المندوب') }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Change History Section -->
        <div class="col-12">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-secondary text-white">
                    <h6 class="mb-0">
                        <i class="ti ti-history me-2"></i>{{ __('معلومات السجل') }}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">{{ __('تاريخ الإنشاء') }}:</small>
                            <p class="mb-0">{{ $representative->created_at->format('Y-m-d H:i') }}</p>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">{{ __('آخر تحديث') }}:</small>
                            <p class="mb-0">{{ $representative->updated_at->format('Y-m-d H:i') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal-footer bg-light" style="border-radius: 0 0 15px 15px;">
    <button type="button" class="btn btn-light btn-lg me-2" onclick="location.href = '{{ route('vendor.representatives.index') }}';" style="border-radius: 10px;">
        <i class="ti ti-x me-1"></i>{{ __('إلغاء') }}
    </button>
    <button type="submit" class="btn btn-warning btn-lg" style="border-radius: 10px;">
        <i class="ti ti-device-floppy me-1"></i>{{ __('حفظ التغييرات') }}
    </button>
</div>
{{ Form::close() }}

<style>
    .card {
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
    }

    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        transform: scale(1.02);
        transition: all 0.3s ease;
    }

    .form-check-input:checked {
        background-color: #28a745;
        border-color: #28a745;
    }

    .btn {
        transition: all 0.3s ease;
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .select2-container--default .select2-selection--single {
        height: 48px !important;
        border: 2px solid #e9ecef !important;
        border-radius: 10px !important;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 44px !important;
        padding-left: 12px !important;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 44px !important;
    }

    .theme-avtar {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .card-header {
        border-radius: 15px 15px 0 0 !important;
    }

    .modal-body {
        max-height: 70vh;
        overflow-y: auto;
    }

    /* Custom scrollbar */
    .modal-body::-webkit-scrollbar {
        width: 6px;
    }

    .modal-body::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }

    .modal-body::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 10px;
    }

    .modal-body::-webkit-scrollbar-thumb:hover {
        background: #555;
    }
</style>

<script>
    $(document).ready(function() {
        // Initialize Select2 with enhanced styling
        $('.select2-vendor').select2({
            placeholder: 'اختر الشركة الموردة',
            allowClear: true,
            width: '100%',
            dropdownParent: $('.modal-body'),
            templateResult: function(option) {
                if (!option.id) {
                    return option.text;
                }
                return $('<span><i class="ti ti-building me-2 text-info"></i>' + option.text + '</span>');
            },
            templateSelection: function(option) {
                if (!option.id) {
                    return option.text;
                }
                return $('<span><i class="ti ti-building me-2 text-info"></i>' + option.text + '</span>');
            }
        });

        $('.select2-category').select2({
            placeholder: 'اختر فئة المنتجات',
            allowClear: true,
            width: '100%',
            dropdownParent: $('.modal-body'),
            templateResult: function(option) {
                if (!option.id) {
                    return option.text;
                }
                return $('<span><i class="ti ti-category me-2 text-warning"></i>' + option.text + '</span>');
            },
            templateSelection: function(option) {
                if (!option.id) {
                    return option.text;
                }
                return $('<span><i class="ti ti-category me-2 text-warning"></i>' + option.text + '</span>');
            }
        });

        // Form validation with enhanced UX
        $('form').on('submit', function(e) {
            let isValid = true;
            let firstError = null;

            // Check required fields
            $(this).find('[required]').each(function() {
                if (!$(this).val()) {
                    isValid = false;
                    $(this).addClass('is-invalid');
                    if (!firstError) {
                        firstError = $(this);
                    }
                } else {
                    $(this).removeClass('is-invalid').addClass('is-valid');
                }
            });

            if (!isValid) {
                e.preventDefault();

                // Show error message
                show_toastr('خطأ', 'يرجى ملء جميع الحقول المطلوبة', 'error');

                // Focus on first error field
                if (firstError) {
                    firstError.focus();

                    // Scroll to the error field
                    $('.modal-body').animate({
                        scrollTop: firstError.offset().top - $('.modal-body').offset().top + $('.modal-body').scrollTop() - 100
                    }, 500);
                }

                return false;
            }

            // Show loading state
            $(this).find('button[type="submit"]').html('<i class="ti ti-loader-2 me-1"></i>جاري الحفظ...').prop('disabled', true);
        });

        // Real-time validation
        $('[required]').on('blur', function() {
            if ($(this).val()) {
                $(this).removeClass('is-invalid').addClass('is-valid');
            } else {
                $(this).removeClass('is-valid').addClass('is-invalid');
            }
        });

        // Phone number formatting
        $('input[name="phone"]').on('input', function() {
            let value = $(this).val().replace(/\D/g, '');
            if (value.startsWith('966')) {
                value = '+' + value;
            } else if (value.startsWith('05')) {
                // Saudi mobile format
                value = value.replace(/(\d{3})(\d{3})(\d{4})/, '$1 $2 $3');
            }
            $(this).val(value);
        });

        // Enhanced form animations
        $('.form-control').on('focus', function() {
            $(this).parent().find('label').addClass('text-primary');
        }).on('blur', function() {
            $(this).parent().find('label').removeClass('text-primary');
        });

        // Character counter for notes
        $('textarea[name="notes"]').on('input', function() {
            let length = $(this).val().length;
            let maxLength = 500; // Set a reasonable limit

            if (!$(this).next('.char-counter').length) {
                $(this).after('<small class="char-counter text-muted float-end"></small>');
            }

            $(this).next('.char-counter').text(length + ' / ' + maxLength + ' حرف');

            if (length > maxLength) {
                $(this).addClass('is-invalid');
                $(this).next('.char-counter').addClass('text-danger');
            } else {
                $(this).removeClass('is-invalid');
                $(this).next('.char-counter').removeClass('text-danger');
            }
        });

        // Trigger character counter on page load
        $('textarea[name="notes"]').trigger('input');

        // Add tooltips
        $('[data-bs-toggle="tooltip"]').tooltip();

        // Highlight changes
        let originalData = {
            name: $('input[name="name"]').val(),
            phone: $('input[name="phone"]').val(),
            vendor_id: $('select[name="vendor_id"]').val(),
            category_id: $('select[name="category_id"]').val(),
            is_active: $('input[name="is_active"]').is(':checked'),
            notes: $('textarea[name="notes"]').val()
        };

        // Track changes
        $('input, select, textarea').on('input change', function() {
            let currentField = $(this).attr('name');
            let currentValue = $(this).is(':checkbox') ? $(this).is(':checked') : $(this).val();

            if (originalData[currentField] != currentValue) {
                $(this).addClass('border-warning');
                $(this).parent().find('label').addClass('text-warning');
            } else {
                $(this).removeClass('border-warning');
                $(this).parent().find('label').removeClass('text-warning');
            }
        });
    });
</script>
