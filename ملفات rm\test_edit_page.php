<?php
// ملف اختبار بسيط لصفحة التعديل
echo "<!DOCTYPE html>";
echo "<html>";
echo "<head>";
echo "<title>اختبار صفحة التعديل</title>";
echo "<meta charset='UTF-8'>";
echo "</head>";
echo "<body>";
echo "<h1>اختبار صفحة التعديل</h1>";

// محاكاة البيانات
$productService = (object)[
    'id' => 1,
    'name' => 'منتج تجريبي',
    'sku' => 'TEST-001',
    'sale_price' => 100,
    'tax_id' => '1,2',
    'type' => 'product'
];

// محاكاة الضرائب
$currentTaxRate = 15; // 15% VAT

// حساب المبلغ الإجمالي شامل الضريبة
$saleValueIncludingVAT = $productService->sale_price * (1 + ($currentTaxRate / 100));

echo "<p><strong>بيانات المنتج:</strong></p>";
echo "<ul>";
echo "<li>الاسم: " . $productService->name . "</li>";
echo "<li>SKU: " . $productService->sku . "</li>";
echo "<li>سعر البيع (قبل الضريبة): " . $productService->sale_price . " ريال</li>";
echo "<li>معدل الضريبة: " . $currentTaxRate . "%</li>";
echo "<li>المبلغ الإجمالي (شامل الضريبة): " . number_format($saleValueIncludingVAT, 2) . " ريال</li>";
echo "</ul>";

echo "<p><strong>اختبار الحساب:</strong></p>";
echo "<p>إذا كان المبلغ الإجمالي = " . number_format($saleValueIncludingVAT, 2) . " ريال</p>";
echo "<p>فإن السعر قبل الضريبة = " . number_format($saleValueIncludingVAT / (1 + ($currentTaxRate / 100)), 2) . " ريال</p>";

echo "</body>";
echo "</html>";
?>
