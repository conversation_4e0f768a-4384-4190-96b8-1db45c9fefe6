using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace POS_Desktop_App.Models.DatabaseModels
{
    /// <summary>
    /// نموذج المستودع - يطابق جدول warehouses في قاعدة البيانات
    /// </summary>
    [Table("warehouses")]
    public class Warehouse
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("name")]
        [Required]
        [StringLength(255)]
        public string Name { get; set; }

        [Column("address")]
        [Required]
        public string Address { get; set; }

        [Column("city")]
        [Required]
        [StringLength(255)]
        public string City { get; set; }

        [Column("city_zip")]
        [Required]
        [StringLength(20)]
        public string CityZip { get; set; }

        [Column("created_by")]
        public int CreatedBy { get; set; } = 0;

        [Column("created_at")]
        public DateTime CreatedAt { get; set; }

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; }

        // Navigation Properties
        public virtual ICollection<WarehouseProduct> WarehouseProducts { get; set; } = new List<WarehouseProduct>();
        public virtual ICollection<PosTransaction> PosTransactions { get; set; } = new List<PosTransaction>();

        // Calculated Properties
        [NotMapped]
        public string FullAddress => $"{Address}, {City} {CityZip}";

        [NotMapped]
        public int TotalProducts => WarehouseProducts?.Count ?? 0;

        [NotMapped]
        public int TotalStock => WarehouseProducts?.Sum(wp => wp.Quantity) ?? 0;

        // Helper Methods
        public int GetProductStock(long productId)
        {
            var warehouseProduct = WarehouseProducts?.FirstOrDefault(wp => wp.ProductId == productId);
            return warehouseProduct?.Quantity ?? 0;
        }

        public bool HasProduct(long productId)
        {
            return WarehouseProducts?.Any(wp => wp.ProductId == productId) ?? false;
        }

        public void AddProduct(long productId, int quantity)
        {
            var existingProduct = WarehouseProducts?.FirstOrDefault(wp => wp.ProductId == productId);
            if (existingProduct != null)
            {
                existingProduct.Quantity += quantity;
            }
            else
            {
                WarehouseProducts?.Add(new WarehouseProduct
                {
                    WarehouseId = this.Id,
                    ProductId = productId,
                    Quantity = quantity,
                    CreatedBy = this.CreatedBy
                });
            }
        }

        public override string ToString()
        {
            return Name;
        }
    }
}
