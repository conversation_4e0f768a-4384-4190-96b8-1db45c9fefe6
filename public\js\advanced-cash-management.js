class AdvancedCashManagement {
    constructor() {
        this.salesChart = null;
        this.init();
    }

    init() {
        this.initializeDateRangePicker();
        this.setupEventListeners();
        this.loadInitialData();
    }

    initializeDateRangePicker() {
        $('#daterange').daterangepicker({
            startDate: moment().subtract(29, 'days'),
            endDate: moment(),
            locale: {
                format: 'YYYY-MM-DD',
                separator: ' - ',
                applyLabel: 'تطبيق',
                cancelLabel: 'إلغاء',
                fromLabel: 'من',
                toLabel: 'إلى',
                customRangeLabel: 'مخصص',
                weekLabel: 'أ',
                daysOfWeek: ['أح', 'إث', 'ث', 'أر', 'خ', 'ج', 'س'],
                monthNames: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                firstDay: 1
            }
        });
    }

    setupEventListeners() {
        // Apply filters button
        $('#applyFilters').on('click', () => {
            this.loadAllData();
        });

        // Refresh button
        $('#refreshBtn').on('click', () => {
            this.loadAllData();
        });

        // Tab change events
        $('#mainTabs button[data-bs-toggle="tab"]').on('shown.bs.tab', (e) => {
            const target = $(e.target).attr('data-bs-target');
            this.loadTabData(target);

            // Show real-time notification for voucher tabs
            if (target === '#receipts') {
                this.showVoucherUpdateNotification('تحديث سندات القبض - بيانات مباشرة');
            } else if (target === '#payments') {
                this.showVoucherUpdateNotification('تحديث سندات الصرف - بيانات مباشرة');
            }
        });

        // Auto refresh every 5 minutes for open shifts
        setInterval(() => {
            if ($('#shifts-tab').hasClass('active')) {
                this.loadShiftsData();
            }
            this.loadQuickStats();
            this.loadAlerts();
        }, 300000); // 5 minutes

        // Real-time updates for vouchers every 30 seconds
        this.setupVoucherRealTimeUpdates();
    }

    getFilters() {
        const dateRange = $('#daterange').val().split(' - ');
        return {
            warehouse_id: $('#warehouseFilter').val(),
            user_id: $('#userFilter').val(),
            shift_status: $('#shiftStatusFilter').val(),
            start_date: dateRange[0],
            end_date: dateRange[1]
        };
    }

    loadInitialData() {
        this.loadQuickStats();
        this.loadAlerts();
        this.loadShiftsData();
    }

    loadAllData() {
        this.loadQuickStats();
        this.loadAlerts();
        
        // Load data for active tab
        const activeTab = $('#mainTabs .nav-link.active').attr('data-bs-target');
        this.loadTabData(activeTab);
    }

    loadTabData(target) {
        switch(target) {
            case '#shifts':
                this.loadShiftsData();
                break;
            case '#receipts':
                this.loadReceiptVouchers();
                break;
            case '#payments':
                this.loadPaymentVouchers();
                break;
            case '#pos':
                this.loadPOSData();
                this.loadChartData();
                break;
        }
    }

    loadQuickStats() {
        const filters = this.getFilters();

        $.ajax({
            url: '/financial-operations/api/quick-stats',
            method: 'GET',
            data: filters,
            success: (response) => {
                console.log('Quick stats response:', response);
                $('#dailyReceipts').text(response.daily_receipts || '0.00');
                $('#dailyPayments').text(response.daily_payments || '0.00');
                $('#netCash').text(response.net_cash || '0.00');
                $('#openShifts').text(response.open_shifts || '0');
                $('#totalDeficit').text(response.total_deficit || '0.00');
                $('#activeWarehouses').text(response.active_warehouses || '0');

                // Highlight deficit if > 0
                const deficitValue = parseFloat(response.total_deficit || 0);
                if (deficitValue > 0) {
                    $('#totalDeficit').parent().addClass('deficit-highlight');
                } else {
                    $('#totalDeficit').parent().removeClass('deficit-highlight');
                }
            },
            error: (xhr) => {
                console.error('Error loading quick stats:', xhr);
                console.error('Response text:', xhr.responseText);
                this.showError('خطأ في تحميل الإحصائيات السريعة: ' + (xhr.responseJSON?.message || xhr.statusText));
                // Set default values on error
                $('#dailyReceipts').text('0.00');
                $('#dailyPayments').text('0.00');
                $('#netCash').text('0.00');
                $('#openShifts').text('0');
                $('#totalDeficit').text('0.00');
                $('#activeWarehouses').text('0');
            }
        });
    }

    loadAlerts() {
        $.ajax({
            url: '/financial-operations/api/alerts',
            method: 'GET',
            success: (response) => {
                this.displayAlerts(response.alerts);
            },
            error: (xhr) => {
                console.error('Error loading alerts:', xhr);
            }
        });
    }

    displayAlerts(alerts) {
        const container = $('#alertsContainer');
        container.empty();

        alerts.forEach(alert => {
            const alertClass = alert.type === 'warning' ? 'alert-warning' : 
                              alert.type === 'danger' ? 'alert-danger' : 'alert-info';
            
            const alertHtml = `
                <div class="alert ${alertClass} alert-custom">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تنبيه:</strong> ${alert.message}
                    <small class="d-block mt-1">${alert.created_at}</small>
                </div>
            `;
            container.append(alertHtml);
        });
    }

    loadShiftsData() {
        this.showLoading('#shiftsLoading', '#shiftsTable');
        const filters = this.getFilters();

        $.ajax({
            url: '/financial-operations/api/shifts-data',
            method: 'GET',
            data: filters,
            success: (response) => {
                console.log('Shifts data response:', response);
                this.populateShiftsTable(response.data || []);
                this.hideLoading('#shiftsLoading', '#shiftsTable');
            },
            error: (xhr) => {
                console.error('Error loading shifts data:', xhr);
                console.error('Response text:', xhr.responseText);
                this.showError('خطأ في تحميل بيانات الشفتات: ' + (xhr.responseJSON?.message || xhr.statusText));
                this.populateShiftsTable([]); // Show empty table
                this.hideLoading('#shiftsLoading', '#shiftsTable');
            }
        });
    }

    populateShiftsTable(data) {
        const tbody = $('#shiftsTableBody');
        tbody.empty();

        if (!data || data.length === 0) {
            tbody.append(`
                <tr>
                    <td colspan="11" class="text-center text-muted py-4">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <br>لا توجد شفتات في الفترة المحددة
                    </td>
                </tr>
            `);
            return;
        }

        data.forEach(shift => {
            const deficitValue = parseFloat(shift.deficit || 0);
            let deficitClass = 'deficit-zero';
            let deficitDisplay = shift.deficit || '0.00';

            if (deficitValue > 0) {
                deficitClass = 'deficit-positive';
                if (deficitValue > 500) {
                    deficitClass += ' deficit-highlight'; // Animate high deficits
                }
            }

            const row = `
                <tr>
                    <td>${shift.warehouse_name || '-'}</td>
                    <td>${shift.creator_name || '-'}</td>
                    <td>${shift.opened_at || '-'}</td>
                    <td>${shift.closed_at || '-'}</td>
                    <td>${shift.opening_balance || '0.00'}</td>
                    <td>${shift.current_cash || '0.00'}</td>
                    <td>${shift.overnetwork_cash || '0.00'}</td>
                    <td>${shift.total_cash || '0.00'}</td>
                    <td class="${deficitClass}">${deficitDisplay}</td>
                    <td><span class="badge ${shift.status_class || 'badge-secondary'}">${shift.status || 'غير محدد'}</span></td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="viewShift(${shift.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${!shift.is_closed ? `
                            <button class="btn btn-sm btn-warning" onclick="editShift(${shift.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                        ` : `
                            <button class="btn btn-sm btn-info" onclick="printShift(${shift.id})">
                                <i class="fas fa-print"></i>
                            </button>
                        `}
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }

    loadReceiptVouchers() {
        this.showLoading('#receiptsLoading', '#receiptsTable');
        const filters = this.getFilters();

        $.ajax({
            url: '/financial-operations/api/receipt-vouchers',
            method: 'GET',
            data: filters,
            success: (response) => {
                this.populateReceiptVouchersTable(response.data);
                this.hideLoading('#receiptsLoading', '#receiptsTable');
            },
            error: (xhr) => {
                console.error('Error loading receipt vouchers:', xhr);
                this.showError('خطأ في تحميل سندات القبض');
                this.hideLoading('#receiptsLoading', '#receiptsTable');
            }
        });
    }

    populateReceiptVouchersTable(data) {
        const tbody = $('#receiptsTableBody');
        tbody.empty();

        if (!data || data.length === 0) {
            tbody.append(`
                <tr>
                    <td colspan="9" class="text-center text-muted py-4">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <br>لا توجد سندات قبض في الفترة المحددة
                    </td>
                </tr>
            `);
            return;
        }

        data.forEach(voucher => {
            // Add real-time indicator for recent vouchers (created in last 5 minutes)
            const createdTime = new Date(voucher.created_at);
            const now = new Date();
            const diffMinutes = (now - createdTime) / (1000 * 60);
            const isRecent = diffMinutes <= 5;

            const row = `
                <tr class="${isRecent ? 'table-success' : ''}" title="${isRecent ? 'سند جديد - تم إنشاؤه منذ ' + Math.round(diffMinutes) + ' دقائق' : ''}">
                    <td>
                        ${voucher.custome_id}
                        ${isRecent ? '<span class="badge badge-pill bg-success ms-2">جديد</span>' : ''}
                    </td>
                    <td>${voucher.date}</td>
                    <td>${voucher.received_from}</td>
                    <td class="text-end fw-bold">${voucher.amount} ريال</td>
                    <td><span class="badge bg-success">${voucher.payment_method}</span></td>
                    <td>${voucher.purpose}</td>
                    <td>${voucher.warehouse_name}</td>
                    <td><span class="badge ${voucher.status_class}">${voucher.status}</span></td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="viewReceiptVoucher(${voucher.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="editReceiptVoucher(${voucher.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }

    loadPaymentVouchers() {
        this.showLoading('#paymentsLoading', '#paymentsTable');
        const filters = this.getFilters();

        $.ajax({
            url: '/financial-operations/api/payment-vouchers',
            method: 'GET',
            data: filters,
            success: (response) => {
                this.populatePaymentVouchersTable(response.data);
                this.hideLoading('#paymentsLoading', '#paymentsTable');
            },
            error: (xhr) => {
                console.error('Error loading payment vouchers:', xhr);
                this.showError('خطأ في تحميل سندات الصرف');
                this.hideLoading('#paymentsLoading', '#paymentsTable');
            }
        });
    }

    populatePaymentVouchersTable(data) {
        const tbody = $('#paymentsTableBody');
        tbody.empty();

        if (!data || data.length === 0) {
            tbody.append(`
                <tr>
                    <td colspan="9" class="text-center text-muted py-4">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <br>لا توجد سندات صرف في الفترة المحددة
                    </td>
                </tr>
            `);
            return;
        }

        data.forEach(voucher => {
            // Add real-time indicator for recent vouchers (created in last 5 minutes)
            const createdTime = new Date(voucher.created_at);
            const now = new Date();
            const diffMinutes = (now - createdTime) / (1000 * 60);
            const isRecent = diffMinutes <= 5;

            const row = `
                <tr class="${isRecent ? 'table-warning' : ''}" title="${isRecent ? 'سند جديد - تم إنشاؤه منذ ' + Math.round(diffMinutes) + ' دقائق' : ''}">
                    <td>
                        ${voucher.custome_id}
                        ${isRecent ? '<span class="badge badge-pill bg-warning ms-2">جديد</span>' : ''}
                    </td>
                    <td>${voucher.date}</td>
                    <td>${voucher.paid_to}</td>
                    <td class="text-end fw-bold text-danger">${voucher.amount} ريال</td>
                    <td><span class="badge bg-info">${voucher.payment_method}</span></td>
                    <td>${voucher.purpose}</td>
                    <td>${voucher.warehouse_name}</td>
                    <td><span class="badge ${voucher.status_class}">${voucher.status}</span></td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="viewPaymentVoucher(${voucher.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="editPaymentVoucher(${voucher.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }

    loadPOSData() {
        this.showLoading('#posLoading', '#posTable');
        const filters = this.getFilters();

        console.log('Loading POS data with filters:', filters);

        $.ajax({
            url: '/financial-operations/api/pos-sales',
            method: 'GET',
            data: filters,
            success: (response) => {
                console.log('POS data response:', response);

                // إظهار معلومات التشخيص إذا كانت متوفرة
                if (response.debug_info) {
                    console.log('Debug info:', response.debug_info);

                    // إظهار رسالة تشخيصية للمستخدم إذا لم توجد بيانات
                    if (response.debug_info.result_count === 0 && response.debug_info.pos_count > 0) {
                        this.showError(`تم العثور على ${response.debug_info.pos_count} فاتورة و ${response.debug_info.payments_count} دفعة في الفترة المحددة، لكن لا توجد بيانات شفتات مرتبطة. قد تحتاج إلى ربط الفواتير بالشفتات.`);
                    }
                }

                this.populatePOSTable(response.data || []);
                this.hideLoading('#posLoading', '#posTable');
            },
            error: (xhr) => {
                console.error('Error loading POS data:', xhr);
                console.error('Response text:', xhr.responseText);

                let errorMessage = 'خطأ في تحميل بيانات مبيعات POS';

                if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMessage += ': ' + xhr.responseJSON.error;
                }

                this.showError(errorMessage);
                this.populatePOSTable([]); // إظهار جدول فارغ
                this.hideLoading('#posLoading', '#posTable');
            }
        });
    }

    populatePOSTable(data) {
        const tbody = $('#posTableBody');
        tbody.empty();

        if (!data || data.length === 0) {
            tbody.append(`
                <tr>
                    <td colspan="8" class="text-center text-muted py-4">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <br>لا توجد مبيعات في الفترة المحددة
                        <br><small class="text-muted">تأكد من وجود فواتير مبيعات في التواريخ المحددة وأنها مرتبطة بشفتات</small>
                    </td>
                </tr>
            `);
            return;
        }

        data.forEach(sale => {
            const deficitSurplusValue = parseFloat(sale.deficit_surplus_raw || 0);
            let deficitSurplusClass = 'text-success fw-bold';
            let deficitSurplusIcon = '<i class="fas fa-check-circle me-1"></i>';
            let rowClass = '';

            if (deficitSurplusValue > 0) {
                // عجز (المبيعات أكثر من المحصل)
                deficitSurplusClass = 'text-danger fw-bold';
                deficitSurplusIcon = '<i class="fas fa-exclamation-triangle me-1"></i>';
                rowClass = 'deficit-cell';
            } else if (deficitSurplusValue < 0) {
                // فائض (المحصل أكثر من المبيعات)
                deficitSurplusClass = 'text-warning fw-bold';
                deficitSurplusIcon = '<i class="fas fa-plus-circle me-1"></i>';
                rowClass = 'surplus-cell';
            } else {
                // متوازن
                rowClass = 'balanced-cell';
            }

            // إضافة تأثير للشفتات المفتوحة
            if (!sale.is_shift_closed) {
                rowClass += ' open-shift-row';
            }

            // تحديد أيقونة حالة الشفت
            const shiftIcon = sale.is_shift_closed ?
                '<i class="fas fa-lock me-1"></i>' :
                '<i class="fas fa-unlock me-1"></i>';

            // إضافة أيقونة تحذير إذا كان هناك ملاحظة مهمة
            const noteIcon = sale.note ? '<i class="fas fa-info-circle text-info ms-1" title="' + sale.note + '"></i>' : '';

            const row = `
                <tr class="${rowClass}" title="${sale.note || ''}">
                    <td>${sale.sale_date}${noteIcon}</td>
                    <td>${sale.user_name}</td>
                    <td>${sale.warehouse_name}</td>
                    <td class="text-center">${sale.invoice_count}</td>
                    <td class="text-end">${sale.total_sales} ريال</td>
                    <td class="text-end">${sale.total_collected} ريال</td>
                    <td class="text-end ${deficitSurplusClass}">
                        ${deficitSurplusIcon}${sale.deficit_surplus} ريال
                    </td>
                    <td class="text-center">
                        <span class="badge ${sale.shift_status_class}">
                            ${shiftIcon}${sale.shift_status}
                        </span>
                        ${sale.note ? '<br><small class="text-muted">' + sale.note + '</small>' : ''}
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }

    loadChartData() {
        const filters = this.getFilters();

        $.ajax({
            url: '/financial-operations/api/chart-data',
            method: 'GET',
            data: filters,
            success: (response) => {
                this.updateChart(response);
            },
            error: (xhr) => {
                console.error('Error loading chart data:', xhr);
            }
        });
    }

    updateChart(data) {
        const ctx = document.getElementById('salesChart').getContext('2d');
        
        if (this.salesChart) {
            this.salesChart.destroy();
        }

        this.salesChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.labels,
                datasets: [{
                    data: data.data,
                    backgroundColor: data.colors,
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    showLoading(loadingSelector, tableSelector) {
        $(loadingSelector).show();
        $(tableSelector).hide();
    }

    hideLoading(loadingSelector, tableSelector) {
        $(loadingSelector).hide();
        $(tableSelector).show();
    }

    setupVoucherRealTimeUpdates() {
        // Real-time updates for vouchers every 30 seconds
        setInterval(() => {
            const activeTab = $('#mainTabs .nav-link.active').attr('data-bs-target');

            if (activeTab === '#receipts') {
                this.loadReceiptVouchers();
                this.showVoucherUpdateNotification('تم تحديث سندات القبض');
            } else if (activeTab === '#payments') {
                this.loadPaymentVouchers();
                this.showVoucherUpdateNotification('تم تحديث سندات الصرف');
            }

            // Always update quick stats for real-time financial data
            this.loadQuickStats();
        }, 30000); // 30 seconds
    }

    showVoucherUpdateNotification(message) {
        // Show subtle notification for real-time updates
        const notification = $(`
            <div class="voucher-update-notification">
                <i class="fas fa-sync-alt me-2"></i>
                ${message}
            </div>
        `);

        $('body').append(notification);

        // Animate in
        notification.fadeIn(300);

        // Auto remove after 2 seconds
        setTimeout(() => {
            notification.fadeOut(300, function() {
                $(this).remove();
            });
        }, 2000);
    }

    showError(message) {
        console.error(message);

        // Show error in alerts container
        const alertHtml = `
            <div class="alert alert-danger alert-custom">
                <i class="fas fa-exclamation-circle me-2"></i>
                <strong>خطأ:</strong> ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        $('#alertsContainer').prepend(alertHtml);

        // Auto remove after 5 seconds
        setTimeout(() => {
            $('#alertsContainer .alert-danger:first').fadeOut();
        }, 5000);
    }
}

// Global functions for button actions
function viewShift(id) {
    window.open(`/company-operations/branch-cash-management/${id}`, '_blank');
}

function editShift(id) {
    // Implement edit functionality
    console.log('Edit shift:', id);
}

function printShift(id) {
    // Implement print functionality
    console.log('Print shift:', id);
}

function viewReceiptVoucher(id) {
    window.open(`/receipt-voucher/${id}`, '_blank');
}

function editReceiptVoucher(id) {
    window.open(`/receipt-voucher/edit/${id}`, '_blank');
}

function viewPaymentVoucher(id) {
    window.open(`/payment-voucher/${id}`, '_blank');
}

function editPaymentVoucher(id) {
    window.open(`/payment-voucher/edit/${id}`, '_blank');
}

// Initialize when document is ready
$(document).ready(function() {
    new AdvancedCashManagement();
});
