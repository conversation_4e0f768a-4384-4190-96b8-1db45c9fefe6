using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace POS_Desktop_App.Models.LocalCache
{
    /// <summary>
    /// نموذج المعاملة المحلية - للتخزين المؤقت في SQLite
    /// </summary>
    [Table("local_pos_transactions")]
    public class LocalPosTransaction
    {
        [Key]
        public long Id { get; set; }

        [Required]
        public long? ServerPosId { get; set; } // null if not synced yet

        [Required]
        public string TransactionData { get; set; } // JSON data

        [Required]
        public SyncStatus SyncStatus { get; set; } = SyncStatus.Pending;

        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? SyncedAt { get; set; }

        public string ErrorMessage { get; set; }

        public int RetryCount { get; set; } = 0;

        // Helper properties for quick access without deserializing JSON
        public long CustomerId { get; set; }
        public long WarehouseId { get; set; }
        public decimal TotalAmount { get; set; }
        public string CustomerName { get; set; }
        public string WarehouseName { get; set; }
    }

    /// <summary>
    /// نموذج المنتج المحلي - للتخزين المؤقت
    /// </summary>
    [Table("local_products")]
    public class LocalProduct
    {
        [Key]
        public long Id { get; set; }

        [Required]
        public long ServerProductId { get; set; }

        [Required]
        public string ProductData { get; set; } // JSON data

        [Required]
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        // Helper properties for quick search without deserializing JSON
        public string Name { get; set; }
        public string Sku { get; set; }
        public decimal SalePrice { get; set; }
        public string CategoryName { get; set; }
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// نموذج العميل المحلي - للتخزين المؤقت
    /// </summary>
    [Table("local_customers")]
    public class LocalCustomer
    {
        [Key]
        public long Id { get; set; }

        [Required]
        public long ServerCustomerId { get; set; }

        [Required]
        public string CustomerData { get; set; } // JSON data

        [Required]
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        // Helper properties for quick search without deserializing JSON
        public string Name { get; set; }
        public string Contact { get; set; }
        public string Email { get; set; }
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// نموذج المستودع المحلي - للتخزين المؤقت
    /// </summary>
    [Table("local_warehouses")]
    public class LocalWarehouse
    {
        [Key]
        public long Id { get; set; }

        [Required]
        public long ServerWarehouseId { get; set; }

        [Required]
        public string WarehouseData { get; set; } // JSON data

        [Required]
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        // Helper properties for quick search without deserializing JSON
        public string Name { get; set; }
        public string Address { get; set; }
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// نموذج مخزون المستودع المحلي
    /// </summary>
    [Table("local_warehouse_products")]
    public class LocalWarehouseProduct
    {
        [Key]
        public long Id { get; set; }

        [Required]
        public long WarehouseId { get; set; }

        [Required]
        public long ProductId { get; set; }

        [Required]
        public int Quantity { get; set; }

        [Required]
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        // Helper properties
        public string ProductName { get; set; }
        public string ProductSku { get; set; }
        public string WarehouseName { get; set; }
    }

    /// <summary>
    /// طابور المزامنة - للعمليات التي تحتاج مزامنة مع السيرفر
    /// </summary>
    [Table("sync_queue")]
    public class SyncQueue
    {
        [Key]
        public long Id { get; set; }

        [Required]
        [StringLength(50)]
        public string EntityType { get; set; } // PosTransaction, Product, Customer, etc.

        [Required]
        [StringLength(20)]
        public string Operation { get; set; } // Create, Update, Delete

        [Required]
        public string EntityData { get; set; } // JSON data

        [Required]
        public int Priority { get; set; } = 1; // 1 = High, 2 = Medium, 3 = Low

        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? ProcessedAt { get; set; }

        public bool IsProcessed { get; set; } = false;

        public string ErrorMessage { get; set; }

        public int RetryCount { get; set; } = 0;

        public long? EntityId { get; set; } // Local entity ID

        public long? ServerEntityId { get; set; } // Server entity ID after sync
    }

    /// <summary>
    /// إعدادات التطبيق المحلية
    /// </summary>
    [Table("app_settings")]
    public class AppSettings
    {
        [Key]
        [StringLength(100)]
        public string Key { get; set; }

        [Required]
        public string Value { get; set; }

        [Required]
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        public string Description { get; set; }
    }

    /// <summary>
    /// حالات المزامنة
    /// </summary>
    public enum SyncStatus
    {
        Pending = 0,    // في انتظار المزامنة
        Syncing = 1,    // جاري المزامنة
        Synced = 2,     // تم المزامنة
        Failed = 3,     // فشل في المزامنة
        Conflict = 4    // تعارض في البيانات
    }

    /// <summary>
    /// أولويات المزامنة
    /// </summary>
    public static class SyncPriority
    {
        public const int High = 1;      // عالية - المعاملات المالية
        public const int Medium = 2;    // متوسطة - تحديث المخزون
        public const int Low = 3;       // منخفضة - البيانات الأساسية
    }

    /// <summary>
    /// أنواع العمليات
    /// </summary>
    public static class SyncOperation
    {
        public const string Create = "Create";
        public const string Update = "Update";
        public const string Delete = "Delete";
        public const string Sync = "Sync";
    }

    /// <summary>
    /// أنواع الكيانات
    /// </summary>
    public static class EntityType
    {
        public const string PosTransaction = "PosTransaction";
        public const string Product = "Product";
        public const string Customer = "Customer";
        public const string Warehouse = "Warehouse";
        public const string WarehouseProduct = "WarehouseProduct";
        public const string User = "User";
        public const string Shift = "Shift";
    }

    /// <summary>
    /// مفاتيح الإعدادات
    /// </summary>
    public static class SettingsKeys
    {
        public const string LastSyncTime = "LastSyncTime";
        public const string ServerUrl = "ServerUrl";
        public const string SyncInterval = "SyncInterval";
        public const string AutoSync = "AutoSync";
        public const string CurrentUserId = "CurrentUserId";
        public const string CurrentWarehouseId = "CurrentWarehouseId";
        public const string CurrentShiftId = "CurrentShiftId";
        public const string PrinterName = "PrinterName";
        public const string ThermalPrintEnabled = "ThermalPrintEnabled";
        public const string ApiToken = "ApiToken";
        public const string DatabaseVersion = "DatabaseVersion";
        public const string AppVersion = "AppVersion";
        public const string LastBackupTime = "LastBackupTime";
        public const string AutoBackup = "AutoBackup";
        public const string BackupInterval = "BackupInterval";
        public const string MaxRetryCount = "MaxRetryCount";
        public const string SyncTimeout = "SyncTimeout";
        public const string OfflineMode = "OfflineMode";
        public const string CacheExpiry = "CacheExpiry";
        public const string LogLevel = "LogLevel";
        public const string EnableLogging = "EnableLogging";
    }

    /// <summary>
    /// نموذج إحصائيات التخزين المؤقت
    /// </summary>
    public class LocalCacheStatistics
    {
        public int TotalTransactions { get; set; }
        public int PendingSyncTransactions { get; set; }
        public int FailedSyncTransactions { get; set; }
        public int TotalProducts { get; set; }
        public int TotalCustomers { get; set; }
        public int TotalWarehouses { get; set; }
        public int PendingSyncItems { get; set; }
        public int FailedSyncItems { get; set; }
        public long DatabaseSize { get; set; }
        public DateTime? LastSyncTime { get; set; }
        public DateTime? LastBackupTime { get; set; }
        public bool IsOnlineMode { get; set; }
        public string DatabaseVersion { get; set; }
        public string AppVersion { get; set; }
    }

    /// <summary>
    /// نموذج نتيجة المزامنة
    /// </summary>
    public class SyncResult
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; }
        public int ProcessedItems { get; set; }
        public int FailedItems { get; set; }
        public int SkippedItems { get; set; }
        public DateTime SyncTime { get; set; } = DateTime.Now;
        public TimeSpan Duration { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
    }

    /// <summary>
    /// نموذج حالة الاتصال
    /// </summary>
    public class ConnectionStatus
    {
        public bool IsOnline { get; set; }
        public bool DatabaseConnected { get; set; }
        public bool ServerReachable { get; set; }
        public string ServerUrl { get; set; }
        public DateTime LastChecked { get; set; } = DateTime.Now;
        public int ResponseTime { get; set; } // in milliseconds
        public string ErrorMessage { get; set; }
    }
}
