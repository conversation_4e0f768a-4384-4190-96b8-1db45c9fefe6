@extends('layouts.admin')

@section('page-title')
    {{__('تعديل منتجات الفاتورة')}} - {{ $purchase->purchase_id ?? 'N/A' }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('الرئيسية')}}</a></li>
    <li class="breadcrumb-item">{{__('إدارة العمليات المالية')}}</li>
    <li class="breadcrumb-item"><a href="{{route('warehouse.purchase.processing.index')}}">{{__('معالجة فواتير المستودع')}}</a></li>
    <li class="breadcrumb-item">{{__('تعديل منتجات الفاتورة')}}</li>
@endsection

@push('css-page')
    <link rel="stylesheet" href="{{ asset('css/datatable/buttons.dataTables.min.css') }}">
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <style>
        .editable-field {
            cursor: pointer;
            border-bottom: 1px dashed #007bff;
            transition: all 0.3s ease;
            min-height: 25px;
            padding: 5px;
        }
        .editable-field:hover {
            background-color: #f8f9fa;
            border-bottom: 1px solid #007bff;
        }
        .editing {
            background-color: #fff3cd !important;
        }
        .edit-icon {
            opacity: 0;
            transition: opacity 0.3s ease;
            margin-left: 5px;
            color: #007bff;
            font-size: 12px;
        }
        .editable-field:hover .edit-icon {
            opacity: 1;
        }
        .purchase-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .total-row {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .product-row:hover {
            background-color: #f5f5f5;
        }
        .header-field {
            transition: all 0.3s ease;
            border-radius: 3px;
        }
        .header-field:hover {
            background-color: rgba(255,255,255,0.2) !important;
            border-bottom: 1px solid rgba(255,255,255,0.9) !important;
        }
        .header-field.editing {
            background-color: rgba(255,255,255,0.9) !important;
            color: #333 !important;
            border-radius: 5px;
            padding: 5px !important;
        }

        /* تخصيص Select2 ليتناسق مع Bootstrap */
        .select2-container {
            width: 100% !important;
        }

        .select2-container--bootstrap-5 .select2-selection {
            min-height: calc(1.5em + 0.75rem + 2px);
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            color: #212529;
            background-color: #fff;
            background-image: none;
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .select2-container--bootstrap-5 .select2-selection--single {
            height: calc(1.5em + 0.75rem + 2px);
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
            padding: 0;
            line-height: calc(1.5em + 0.75rem);
            color: #212529;
        }

        .select2-dropdown {
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .select2-search--dropdown .select2-search__field {
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            color: #212529;
            background-color: #fff;
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
            width: 100%;
        }

        .select2-results__option {
            padding: 0.5rem 0.75rem;
            font-size: 1rem;
            line-height: 1.5;
        }

        .select2-results__option--highlighted {
            background-color: var(--bs-primary);
            color: #fff;
        }

        /* تحسين عرض النتائج */
        .product-option {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .product-option-title {
            font-weight: 600;
            color: #212529;
        }

        .product-option-details {
            font-size: 0.875rem;
            color: #6c757d;
            display: flex;
            gap: 1rem;
        }

        .product-option-price,
        .product-option-sku {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        /* تصميم صفوف المنتجات */
        .product-row {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 15px;
            padding: 15px;
            background-color: #f9f9f9;
        }

        .remove-product {
            background-color: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .add-product {
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 10px 20px;
            cursor: pointer;
        }

        .invoice-summary {
            background-color: #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            position: sticky;
            top: 20px;
        }

        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
    </style>
@endpush

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="mb-0">{{ __('تعديل منتجات الفاتورة') }} #{{ $purchase->purchase_id ?? 'N/A' }}</h5>
                        </div>
                        <div class="col-6 text-end">
                            <a href="{{ route('warehouse.purchase.processing.index') }}" class="btn btn-secondary">
                                <i class="ti ti-arrow-left"></i> {{ __('العودة') }}
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- رسائل النجاح والخطأ -->
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="ti ti-check-circle me-2"></i>{{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="ti ti-alert-circle me-2"></i>{{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="ti ti-alert-circle me-2"></i>
                            <ul class="mb-0">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <form action="{{ route('warehouse.purchase.processing.update.products', $purchase->id) }}" method="POST" id="editProductsForm">
                        @csrf

                        <!-- معلومات الفاتورة -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <label class="form-label">{{ __('تاريخ الشراء') }}</label>
                                <input type="date" name="purchase_date" class="form-control"
                                       value="{{ $purchase->purchase_date ?? date('Y-m-d') }}" required>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">{{ __('المورد') }}</label>
                                <select name="vender_id" class="form-control vendor-select" required>
                                    <option value="">{{ __('اختر المورد') }}</option>
                                    @if(isset($venders) && count($venders) > 0)
                                        @foreach($venders as $vender)
                                            <option value="{{ $vender->id ?? '' }}"
                                                {{ (isset($purchase->vender_id) && $purchase->vender_id == $vender->id) ? 'selected' : '' }}>
                                                {{ $vender->name ?? 'N/A' }}
                                            </option>
                                        @endforeach
                                    @endif
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">{{ __('المستودع') }}</label>
                                <input type="text" class="form-control"
                                       value="{{ (isset($purchase->warehouse) && isset($purchase->warehouse->name)) ? $purchase->warehouse->name : '-' }}"
                                       readonly>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">{{ __('الحالة') }}</label>
                                <select name="status" class="form-control">
                                    <option value="0" {{ (isset($purchase->status) && $purchase->status == 0) ? 'selected' : '' }}>{{ __('Draft') }}</option>
                                    <option value="1" {{ (isset($purchase->status) && $purchase->status == 1) ? 'selected' : '' }}>{{ __('Sent') }}</option>
                                    <option value="2" {{ (isset($purchase->status) && $purchase->status == 2) ? 'selected' : '' }}>{{ __('Unpaid') }}</option>
                                    <option value="3" {{ (isset($purchase->status) && $purchase->status == 3) ? 'selected' : '' }}>{{ __('Partially Paid') }}</option>
                                    <option value="4" {{ (isset($purchase->status) && $purchase->status == 4) ? 'selected' : '' }}>{{ __('Paid') }}</option>
                                </select>
                            </div>
                        </div>


                        <div class="row">
                            <div class="col-md-8">
                                <h6 class="mb-3">{{ __('منتجات الفاتورة') }}</h6>

                                <div id="products-container">
                                    @if(isset($purchase) && isset($purchase->items))
                                        @foreach($purchase->items as $index => $item)
                                            <div class="product-row" data-index="{{ $index }}">
                                                <!-- Hidden input for existing product ID -->
                                                <input type="hidden" name="products[{{ $index }}][id]" value="{{ $item->id ?? '' }}">

                                            <div class="row align-items-center">
                                                <div class="col-md-3">
                                                    <label class="form-label">{{ __('المنتج') }}</label>
                                                    <select name="products[{{ $index }}][product_id]" class="form-control product-select" required>
                                                        <option value="">{{ __('اختر المنتج') }}</option>
                                                        @if(isset($products) && count($products) > 0)
                                                            @foreach($products as $product)
                                                                <option value="{{ $product->id ?? '' }}"
                                                                    data-price="{{ $product->purchase_price ?? 0 }}"
                                                                    data-sku="{{ $product->sku ?? '' }}"
                                                                    {{ (isset($item->product_id) && $item->product_id == $product->id) ? 'selected' : '' }}>
                                                                    {{ $product->name ?? 'N/A' }} ({{ $product->sku ?? 'N/A' }})
                                                                </option>
                                                            @endforeach
                                                        @endif
                                                    </select>
                                                </div>
                                                <div class="col-md-2">
                                                    <label class="form-label">{{ __('الكمية') }}</label>
                                                    <input type="number" name="products[{{ $index }}][quantity]"
                                                           class="form-control quantity-input"
                                                           value="{{ $item->quantity ?? 1 }}"
                                                           min="1" step="1" required>
                                                </div>
                                                <div class="col-md-2">
                                                    <label class="form-label">{{ __('السعر') }}</label>
                                                    <input type="number" name="products[{{ $index }}][price]"
                                                           class="form-control price-input"
                                                           value="{{ $item->price ?? 0 }}"
                                                           min="0" step="0.01" required>
                                                </div>
                                                <div class="col-md-2">
                                                    <label class="form-label">{{ __('الضريبة') }} (%)</label>
                                                    <input type="number" name="products[{{ $index }}][tax]"
                                                           class="form-control tax-input"
                                                           value="{{ $item->tax ?? 0 }}"
                                                           min="0" max="100" step="0.01">
                                                </div>
                                                <div class="col-md-2">
                                                    <label class="form-label">{{ __('الخصم') }}</label>
                                                    <input type="number" name="products[{{ $index }}][discount]"
                                                           class="form-control discount-input"
                                                           value="{{ $item->discount ?? 0 }}"
                                                           min="0" step="0.01">
                                                </div>
                                                <div class="col-md-1">
                                                    <label class="form-label">&nbsp;</label>
                                                    <button type="button" class="remove-product d-block" onclick="removeProduct(this)">
                                                        <i class="ti ti-x"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="row mt-2">
                                                <div class="col-md-11">
                                                    <label class="form-label">{{ __('الوصف') }}</label>
                                                    <input type="text" name="products[{{ $index }}][description]"
                                                           class="form-control"
                                                           value="{{ $item->description ?? '' }}"
                                                           placeholder="{{ __('وصف اختياري للمنتج') }}">
                                                </div>
                                            </div>
                                        </div>
                                        @endforeach
                                    @else
                                        <div class="alert alert-info">
                                            {{ __('لا توجد منتجات في هذه الفاتورة') }}
                                        </div>
                                    @endif
                                </div>

                                <button type="button" class="add-product" onclick="addProduct()">
                                    <i class="ti ti-plus"></i> {{ __('إضافة منتج') }}
                                </button>
                            </div>

                            <div class="col-md-4">
                                <div class="invoice-summary">
                                    <h6 class="mb-3">{{ __('ملخص الفاتورة') }}</h6>
                                    <div class="row mb-2">
                                        <div class="col-6">{{ __('المجموع الفرعي') }}:</div>
                                        <div class="col-6 text-end" id="subtotal">
                                            @php
                                                try {
                                                    echo Auth::user()->priceFormat($purchase->getSubTotal() ?? 0);
                                                } catch (Exception $e) {
                                                    echo '0.00';
                                                }
                                            @endphp
                                        </div>
                                    </div>
                                    <div class="row mb-2">
                                        <div class="col-6">{{ __('الخصم') }}:</div>
                                        <div class="col-6 text-end" id="total-discount">
                                            @php
                                                try {
                                                    echo Auth::user()->priceFormat($purchase->getTotalDiscount() ?? 0);
                                                } catch (Exception $e) {
                                                    echo '0.00';
                                                }
                                            @endphp
                                        </div>
                                    </div>
                                    <div class="row mb-2">
                                        <div class="col-6">{{ __('الضريبة') }}:</div>
                                        <div class="col-6 text-end" id="total-tax">
                                            @php
                                                try {
                                                    echo Auth::user()->priceFormat($purchase->getTotalTax() ?? 0);
                                                } catch (Exception $e) {
                                                    echo '0.00';
                                                }
                                            @endphp
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-6"><strong>{{ __('الإجمالي') }}:</strong></div>
                                        <div class="col-6 text-end"><strong id="total">
                                            @php
                                                try {
                                                    echo Auth::user()->priceFormat($purchase->getTotal() ?? 0);
                                                } catch (Exception $e) {
                                                    echo '0.00';
                                                }
                                            @endphp
                                        </strong></div>
                                    </div>
                                </div>

                                <div class="mt-3">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="ti ti-device-floppy"></i> {{ __('حفظ التعديلات') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
    <!-- jQuery (إذا لم يكن موجوداً) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        $(document).ready(function() {
            // Setup CSRF token for all AJAX requests
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

            // Debug information
            console.log('Edit Products Page - jQuery loaded:', typeof $ !== 'undefined');
            console.log('CSRF Token:', $('meta[name="csrf-token"]').attr('content'));
            console.log('Editable fields found:', $('.editable-field').length);
            console.log('Header fields found:', $('.header-field').length);

            let currentEditingCell = null;
            const purchaseId = {{ $purchase->id ?? 0 }};

            // تهيئة Select2 للمنتجات
            function initializeSelect2(selectElement) {
                $(selectElement).select2({
                    theme: 'bootstrap-5',
                    placeholder: '{{ __("ابحث عن المنتج...") }}',
                    allowClear: true,
                    width: '100%',
                    dropdownAutoWidth: true,
                    language: {
                        noResults: function() {
                            return '<div class="text-center p-2">{{ __("لا توجد نتائج") }}</div>';
                        },
                        searching: function() {
                            return '<div class="text-center p-2"><i class="fas fa-spinner fa-spin me-2"></i>{{ __("جاري البحث...") }}</div>';
                        },
                        inputTooShort: function() {
                            return '<div class="text-center p-2 text-muted">{{ __("اكتب للبحث") }}</div>';
                        }
                    },
                    matcher: function(params, data) {
                        // إذا لم يكن هناك نص بحث، أظهر جميع الخيارات
                        if ($.trim(params.term) === '') {
                            return data;
                        }

                        // البحث في النص والـ SKU
                        const term = params.term.toLowerCase().trim();
                        const text = data.text.toLowerCase();

                        // البحث في اسم المنتج أو الـ SKU
                        if (text.indexOf(term) > -1) {
                            return data;
                        }

                        // البحث في الـ SKU إذا كان موجود في النص
                        const skuMatch = text.match(/\(([^)]+)\)/);
                        if (skuMatch && skuMatch[1].toLowerCase().indexOf(term) > -1) {
                            return data;
                        }

                        return null;
                    },
                    templateResult: function(option) {
                        if (!option.id) {
                            return option.text;
                        }

                        const $option = $(option.element);
                        const price = $option.data('price') || '0';
                        const sku = $option.data('sku') || '';

                        // تنسيق السعر
                        const formattedPrice = parseFloat(price).toLocaleString('ar-SA', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                        });

                        return $(`
                            <div class="product-option">
                                <div class="product-option-title">${option.text}</div>
                                <div class="product-option-details">
                                    <div class="product-option-price">
                                        <i class="fas fa-coins text-success me-1"></i>
                                        <span>${formattedPrice} ر.س</span>
                                    </div>
                                    <div class="product-option-sku">
                                        <i class="fas fa-barcode text-info me-1"></i>
                                        <span>${sku}</span>
                                    </div>
                                </div>
                            </div>
                        `);
                    },
                    templateSelection: function(option) {
                        if (!option.id) {
                            return option.text;
                        }

                        // عرض اسم المنتج فقط في الحقل المحدد
                        const text = option.text;
                        const nameOnly = text.split(' (')[0]; // إزالة SKU من العرض
                        return nameOnly;
                    },
                    escapeMarkup: function(markup) {
                        return markup; // السماح بـ HTML في النتائج
                    }
                });
            }

            // تهيئة Select2 للموردين
            function initializeVendorSelect2(selectElement) {
                $(selectElement).select2({
                    theme: 'bootstrap-5',
                    placeholder: '{{ __("ابحث عن المورد...") }}',
                    allowClear: true,
                    width: '100%',
                    language: {
                        noResults: function() {
                            return '<div class="text-center p-2">{{ __("لا توجد نتائج") }}</div>';
                        },
                        searching: function() {
                            return '<div class="text-center p-2"><i class="fas fa-spinner fa-spin me-2"></i>{{ __("جاري البحث...") }}</div>';
                        }
                    }
                });
            }

            let productIndex = {{ count($purchase->items) }};

            // تخزين بيانات المنتجات في JavaScript
            const productsData = {!! json_encode($products->map(function($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'sku' => $product->sku,
                    'price' => $product->purchase_price
                ];
            })) !!};

            function addProduct() {
                console.log('addProduct function called');

                const container = document.getElementById('products-container');
                if (!container) {
                    console.error('Products container not found');
                    return;
                }

                const productRow = document.createElement('div');
                productRow.className = 'product-row';
                productRow.setAttribute('data-index', productIndex);

                // إنشاء HTML للمنتج الجديد
                const productHtml = createProductRowHTML(productIndex);
                productRow.innerHTML = productHtml;

                container.appendChild(productRow);
                console.log('Product row added, new index:', productIndex);
                productIndex++;

                // إضافة event listeners للمنتج الجديد
                attachProductEvents(productRow);
                calculateTotals();
            }

            function createProductRowHTML(index) {
                let optionsHtml = '<option value="">{{ __("اختر المنتج") }}</option>';

                productsData.forEach(product => {
                    optionsHtml += `<option value="${product.id}" data-price="${product.price}" data-sku="${product.sku}">
                        ${product.name} (${product.sku})
                    </option>`;
                });

                return `
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <label class="form-label">{{ __('المنتج') }}</label>
                            <select name="products[${index}][product_id]" class="form-control product-select" required>
                                ${optionsHtml}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">{{ __('الكمية') }}</label>
                            <input type="number" name="products[${index}][quantity]"
                                   class="form-control quantity-input"
                                   value="1" min="1" step="1" required>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">{{ __('السعر') }}</label>
                            <input type="number" name="products[${index}][price]"
                                   class="form-control price-input"
                                   value="0" min="0" step="0.01" required>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">{{ __('الضريبة') }} (%)</label>
                            <input type="number" name="products[${index}][tax]"
                                   class="form-control tax-input"
                                   value="0" min="0" max="100" step="0.01">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">{{ __('الخصم') }}</label>
                            <input type="number" name="products[${index}][discount]"
                                   class="form-control discount-input"
                                   value="0" min="0" step="0.01">
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <button type="button" class="remove-product d-block" onclick="removeProduct(this)">
                                <i class="ti ti-x"></i>
                            </button>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-11">
                            <label class="form-label">{{ __('الوصف') }}</label>
                            <input type="text" name="products[${index}][description]"
                                   class="form-control"
                                   placeholder="{{ __('وصف اختياري للمنتج') }}">
                        </div>
                    </div>
                `;
            }

            function removeProduct(button) {
                console.log('removeProduct function called');

                if (!button) {
                    console.error('Button parameter is null');
                    return;
                }

                const productRow = button.closest('.product-row');
                if (!productRow) {
                    console.error('Product row not found');
                    return;
                }

                console.log('Removing product row');

                // إزالة Select2 قبل حذف العنصر
                const productSelect = productRow.querySelector('.product-select');
                if (productSelect && $(productSelect).hasClass('select2-hidden-accessible')) {
                    console.log('Destroying Select2 instance');
                    $(productSelect).select2('destroy');
                }

                productRow.remove();
                console.log('Product row removed');
                calculateTotals();
            }

            function attachProductEvents(row) {
                console.log('Attaching events to product row');

                const productSelect = row.querySelector('.product-select');
                const priceInput = row.querySelector('.price-input');

                if (!productSelect) {
                    console.error('Product select not found in row');
                    return;
                }

                // تهيئة Select2 للمنتج
                try {
                    initializeSelect2(productSelect);
                    console.log('Select2 initialized for new product');
                } catch (error) {
                    console.error('Error initializing Select2:', error);
                }

                // إضافة event listener لتحديث السعر
                $(productSelect).on('select2:select', function(e) {
                    console.log('Product selected:', e.params.data);
                    const selectedOption = e.params.data.element;
                    if (selectedOption && priceInput) {
                        const price = selectedOption.getAttribute('data-price') || 0;
                        priceInput.value = price;
                        console.log('Price updated to:', price);
                        calculateTotals();
                    }
                });

                // إضافة event listeners للحقول الرقمية
                row.querySelectorAll('input').forEach(input => {
                    input.addEventListener('input', function() {
                        console.log('Input changed:', input.name, input.value);
                        calculateTotals();
                    });
                });

                // إضافة event listener لزر الحذف
                const removeButton = row.querySelector('.remove-product');
                if (removeButton) {
                    console.log('Adding remove button event listener');
                    removeButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        console.log('Remove button clicked (event listener)');
                        removeProduct(this);
                    });
                } else {
                    console.error('Remove button not found in row');
                }
            }

            function calculateTotals() {
                console.log('Calculating totals...');

                let subtotal = 0;
                let totalDiscount = 0;
                let totalTax = 0;

                const productRows = document.querySelectorAll('.product-row');
                console.log('Found product rows:', productRows.length);

                productRows.forEach((row, index) => {
                    const quantity = parseFloat(row.querySelector('.quantity-input')?.value) || 0;
                    const price = parseFloat(row.querySelector('.price-input')?.value) || 0;
                    const discount = parseFloat(row.querySelector('.discount-input')?.value) || 0;
                    const tax = parseFloat(row.querySelector('.tax-input')?.value) || 0;

                    console.log(`Row ${index}:`, { quantity, price, discount, tax });

                    const lineTotal = quantity * price;
                    subtotal += lineTotal;
                    totalDiscount += discount;
                    totalTax += (lineTotal * tax / 100);
                });

                const total = subtotal - totalDiscount + totalTax;

                console.log('Totals calculated:', { subtotal, totalDiscount, totalTax, total });

                // تحديث العرض
                const subtotalElement = document.getElementById('subtotal');
                const totalDiscountElement = document.getElementById('total-discount');
                const totalTaxElement = document.getElementById('total-tax');
                const totalElement = document.getElementById('total');

                if (subtotalElement) subtotalElement.textContent = formatPrice(subtotal);
                if (totalDiscountElement) totalDiscountElement.textContent = formatPrice(totalDiscount);
                if (totalTaxElement) totalTaxElement.textContent = formatPrice(totalTax);
                if (totalElement) totalElement.textContent = formatPrice(total);
            }

            function formatPrice(amount) {
                // تنسيق السعر بالريال السعودي
                return amount.toLocaleString('ar-SA', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                }) + ' ر.س';
            }

            // تطبيق event listeners على المنتجات الموجودة
            $(document).ready(function() {
                console.log('Document ready - jQuery version:', $.fn.jquery);
                console.log('Products data loaded:', productsData.length, 'products');
                console.log('Initial product index:', productIndex);

                // تهيئة Select2 للمنتجات الموجودة
                const existingRows = document.querySelectorAll('.product-row');
                console.log('Found existing product rows:', existingRows.length);

                existingRows.forEach((row, index) => {
                    console.log('Initializing existing row:', index);
                    attachProductEvents(row);
                });

                // تهيئة حساب المجاميع الأولي
                calculateTotals();

                // تهيئة Select2 للمورد
                const vendorSelect = document.querySelector('.vendor-select');
                if (vendorSelect) {
                    console.log('Initializing vendor Select2');
                    initializeVendorSelect2(vendorSelect);
                } else {
                    console.error('Vendor select not found');
                }

                // التحقق من صحة النموذج قبل الإرسال
                const form = document.getElementById('editProductsForm');
                if (form) {
                    form.addEventListener('submit', function(e) {
                        console.log('Form submission attempted');

                        const productRows = document.querySelectorAll('.product-row');
                        console.log('Product rows on submit:', productRows.length);

                        if (productRows.length === 0) {
                            e.preventDefault();
                            alert('{{ __("يجب إضافة منتج واحد على الأقل") }}');
                            return false;
                        }

                        let hasValidProduct = false;
                        productRows.forEach(row => {
                            const productSelect = row.querySelector('.product-select');
                            if (productSelect && productSelect.value) {
                                hasValidProduct = true;
                            }
                        });

                        if (!hasValidProduct) {
                            e.preventDefault();
                            alert('{{ __("يجب اختيار منتج واحد على الأقل") }}');
                            return false;
                        }

                        console.log('Form validation passed');
                    });
                } else {
                    console.error('Form not found');
                }

                // إضافة event listener لزر إضافة المنتج كبديل
                const addButton = document.querySelector('.add-product');
                if (addButton) {
                    console.log('Add product button found');
                    addButton.addEventListener('click', function() {
                        console.log('Add product button clicked (event listener)');
                        addProduct();
                    });
                } else {
                    console.error('Add product button not found');
                }

                // إضافة event listener عام لجميع أزرار الحذف
                $(document).on('click', '.remove-product', function(e) {
                    e.preventDefault();
                    console.log('Remove button clicked (delegated event)');
                    removeProduct(this);
                });

                console.log('All event listeners initialized');
            });

        });
    </script>
@endpush
