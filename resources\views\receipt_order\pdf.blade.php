<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $receiptOrder->order_number }} - فاتورة {{ $receiptOrder->order_type }}</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            background: #fff;
            direction: rtl;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #fff;
        }
        
        .invoice-header {
            border-bottom: 4px solid #1e3a8a;
            padding-bottom: 20px;
            margin-bottom: 30px;
            position: relative;
        }
        
        .company-section {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        
        .company-info {
            flex: 1;
        }
        
        .company-logo {
            max-height: 100px;
            max-width: 150px;
            margin-left: 20px;
        }
        
        .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #1e3a8a;
            margin-bottom: 8px;
        }
        
        .company-details {
            font-size: 13px;
            color: #666;
            line-height: 1.4;
        }
        
        .invoice-title {
            text-align: center;
            background: #1e3a8a;
            color: white;
            padding: 20px;
            margin: 25px 0;
            border-radius: 8px;
        }
        
        .invoice-title h1 {
            font-size: 24px;
            margin-bottom: 8px;
            font-weight: bold;
        }
        
        .invoice-title .order-number {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .invoice-meta {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        
        .meta-group {
            flex: 1;
            margin: 0 10px;
        }
        
        .meta-group h4 {
            color: #1e3a8a;
            margin-bottom: 12px;
            font-size: 16px;
            font-weight: bold;
            border-bottom: 2px solid #3b82f6;
            padding-bottom: 5px;
        }
        
        .meta-item {
            margin-bottom: 8px;
            font-size: 13px;
            display: flex;
            justify-content: space-between;
        }
        
        .meta-label {
            font-weight: bold;
            color: #374151;
            min-width: 100px;
        }
        
        .meta-value {
            color: #1f2937;
        }
        
        .products-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            border: 2px solid #1e3a8a;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .products-table th {
            background: #1e3a8a;
            color: white;
            padding: 15px 10px;
            text-align: center;
            font-weight: bold;
            font-size: 13px;
            border: 1px solid #1e40af;
        }
        
        .products-table td {
            padding: 12px 10px;
            border: 1px solid #d1d5db;
            text-align: center;
            font-size: 12px;
        }
        
        .products-table tbody tr:nth-child(even) {
            background: #f9fafb;
        }
        
        .products-table tbody tr:hover {
            background: #eff6ff;
        }
        
        .product-name {
            text-align: right;
            font-weight: 500;
            color: #1f2937;
        }
        
        .total-row {
            background: linear-gradient(135deg, #1e3a8a, #3730a3) !important;
            color: white !important;
            font-weight: bold;
        }
        
        .total-row td {
            border-color: #1e40af !important;
            font-size: 14px;
        }
        
        .summary-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            gap: 20px;
        }
        
        .summary-box {
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            padding: 20px;
            border-radius: 8px;
            border-right: 5px solid #0ea5e9;
            flex: 1;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .summary-title {
            font-weight: bold;
            color: #0c4a6e;
            margin-bottom: 12px;
            font-size: 16px;
        }
        
        .summary-item {
            margin-bottom: 8px;
            font-size: 13px;
            display: flex;
            justify-content: space-between;
        }
        
        .summary-label {
            font-weight: 600;
            color: #374151;
        }
        
        .summary-value {
            color: #1f2937;
            font-weight: bold;
        }
        
        .notes-section {
            background: #fefce8;
            border: 2px solid #facc15;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .notes-title {
            font-weight: bold;
            color: #a16207;
            margin-bottom: 12px;
            font-size: 16px;
        }
        
        .notes-content {
            color: #92400e;
            line-height: 1.6;
        }
        
        .signatures-section {
            display: flex;
            justify-content: space-between;
            margin-top: 50px;
            padding-top: 30px;
            border-top: 2px solid #e5e7eb;
            gap: 30px;
        }
        
        .signature-box {
            text-align: center;
            flex: 1;
        }
        
        .signature-line {
            border-top: 2px solid #374151;
            margin-top: 50px;
            padding-top: 8px;
            font-weight: bold;
            color: #374151;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #d1d5db;
            font-size: 11px;
            color: #6b7280;
        }
        
        .badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: bold;
            color: white;
        }
        
        .badge-success { 
            background: linear-gradient(135deg, #059669, #10b981); 
        }
        
        .badge-info { 
            background: linear-gradient(135deg, #0ea5e9, #3b82f6); 
        }
        
        .badge-warning { 
            background: linear-gradient(135deg, #d97706, #f59e0b); 
        }
        
        .badge-danger { 
            background: linear-gradient(135deg, #dc2626, #ef4444); 
        }
        
        .watermark {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 80px;
            color: rgba(30, 58, 138, 0.05);
            z-index: -1;
            font-weight: bold;
        }
        
        @page {
            margin: 20mm;
            size: A4;
        }
        
        .page-break {
            page-break-after: always;
        }
    </style>
</head>
<body>
    <!-- العلامة المائية -->
    <div class="watermark">{{ $companyData['name'] }}</div>
    
    <div class="invoice-container">
        <!-- رأس الفاتورة -->
        <div class="invoice-header">
            <div class="company-section">
                <div class="company-info">
                    <div class="company-name">{{ $companyData['name'] }}</div>
                    <div class="company-details">
                        <div><strong>العنوان:</strong> {{ $companyData['address'] }}</div>
                        <div><strong>الهاتف:</strong> {{ $companyData['phone'] }}</div>
                        <div><strong>البريد الإلكتروني:</strong> {{ $companyData['email'] }}</div>
                    </div>
                </div>
                @if($companyData['logo'])
                    <img src="{{ $companyData['logo'] }}" alt="شعار الشركة" class="company-logo">
                @endif
            </div>
        </div>

        <!-- عنوان الفاتورة -->
        <div class="invoice-title">
            <h1>
                @if($receiptOrder->order_type === 'استلام بضاعة')
                    فاتورة استلام بضاعة
                @elseif($receiptOrder->order_type === 'نقل بضاعة')
                    أمر نقل بضاعة
                @else
                    أمر إخراج بضاعة
                @endif
            </h1>
            <div class="order-number">رقم الأمر: {{ $receiptOrder->order_number }}</div>
        </div>

        <!-- معلومات الفاتورة -->
        <div class="invoice-meta">
            <div class="meta-group">
                <h4>معلومات الأمر</h4>
                <div class="meta-item">
                    <span class="meta-label">نوع الأمر:</span>
                    <span class="meta-value">
                        @if($receiptOrder->order_type === 'استلام بضاعة')
                            <span class="badge badge-success">{{ $receiptOrder->order_type }}</span>
                        @elseif($receiptOrder->order_type === 'نقل بضاعة')
                            <span class="badge badge-info">{{ $receiptOrder->order_type }}</span>
                        @else
                            <span class="badge badge-warning">{{ $receiptOrder->order_type }}</span>
                        @endif
                    </span>
                </div>
                @if($receiptOrder->vendor)
                    <div class="meta-item">
                        <span class="meta-label">المورد:</span>
                        <span class="meta-value">{{ $receiptOrder->vendor->name }}</span>
                    </div>
                @endif
                <div class="meta-item">
                    <span class="meta-label">المستودع:</span>
                    <span class="meta-value">{{ $receiptOrder->warehouse->name ?? 'غير محدد' }}</span>
                </div>
                @if($receiptOrder->fromWarehouse)
                    <div class="meta-item">
                        <span class="meta-label">من مستودع:</span>
                        <span class="meta-value">{{ $receiptOrder->fromWarehouse->name }}</span>
                    </div>
                @endif
            </div>
            
            <div class="meta-group">
                <h4>التواريخ والأرقام</h4>
                <div class="meta-item">
                    <span class="meta-label">التاريخ:</span>
                    <span class="meta-value">{{ \App\Models\Utility::getDateFormated($receiptOrder->invoice_date ?: $receiptOrder->created_at) }}</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">الوقت:</span>
                    <span class="meta-value">{{ $receiptOrder->created_at->format('H:i') }}</span>
                </div>
                @if($receiptOrder->exit_date)
                    <div class="meta-item">
                        <span class="meta-label">تاريخ الإخراج:</span>
                        <span class="meta-value">{{ \App\Models\Utility::getDateFormated($receiptOrder->exit_date) }}</span>
                    </div>
                @endif
                @if($receiptOrder->invoice_number)
                    <div class="meta-item">
                        <span class="meta-label">رقم الفاتورة:</span>
                        <span class="meta-value">{{ $receiptOrder->invoice_number }}</span>
                    </div>
                @endif
            </div>
            
            <div class="meta-group">
                <h4>معلومات إضافية</h4>
                @if($receiptOrder->exit_reason)
                    <div class="meta-item">
                        <span class="meta-label">سبب الإخراج:</span>
                        <span class="meta-value">{{ $receiptOrder->exit_reason }}</span>
                    </div>
                @endif
                @if($receiptOrder->responsible_person)
                    <div class="meta-item">
                        <span class="meta-label">الشخص المسؤول:</span>
                        <span class="meta-value">{{ $receiptOrder->responsible_person }}</span>
                    </div>
                @endif
                <div class="meta-item">
                    <span class="meta-label">المنشئ:</span>
                    <span class="meta-value">{{ isset($creator) && $creator ? $creator->name : 'غير محدد' }}</span>
                </div>
            </div>
        </div>

        <!-- جدول المنتجات -->
        <table class="products-table">
            <thead>
                <tr>
                    <th width="5%">#</th>
                    <th width="15%">رمز المنتج</th>
                    <th width="25%">اسم المنتج</th>
                    <th width="10%">الكمية</th>
                    @if($receiptOrder->order_type === 'استلام بضاعة')
                        <th width="12%">سعر الوحدة</th>
                        <th width="12%">الإجمالي</th>
                    @endif
                    @if($receiptOrder->products->where('expiry_date', '!=', null)->count() > 0)
                        <th width="12%">تاريخ الصلاحية</th>
                    @endif
                    <th width="21%">ملاحظات</th>
                </tr>
            </thead>
            <tbody>
                @php $totalAmount = 0; @endphp
                @foreach($receiptOrder->products as $index => $item)
                    <tr>
                        <td>{{ $index + 1 }}</td>
                        <td>{{ $item->product->sku ?? 'غير محدد' }}</td>
                        <td class="product-name">{{ $item->product->name ?? 'غير محدد' }}</td>
                        <td>{{ number_format($item->quantity, 2) }}</td>
                        @if($receiptOrder->order_type === 'استلام بضاعة')
                            <td>{{ number_format($item->unit_cost, 2) }}</td>
                            <td>{{ number_format($item->total_cost, 2) }}</td>
                            @php $totalAmount += $item->total_cost; @endphp
                        @endif
                        @if($receiptOrder->products->where('expiry_date', '!=', null)->count() > 0)
                            <td>
                                @if($item->expiry_date)
                                    {{ \App\Models\Utility::getDateFormated($item->expiry_date) }}
                                @else
                                    -
                                @endif
                            </td>
                        @endif
                        <td>
                            @if($item->is_return)
                                <span class="badge badge-danger">مرتجع</span><br>
                            @endif
                            {{ $item->notes }}
                        </td>
                    </tr>
                @endforeach
                
                @if($receiptOrder->order_type === 'استلام بضاعة' && $totalAmount > 0)
                    <tr class="total-row">
                        <td colspan="{{ $receiptOrder->products->where('expiry_date', '!=', null)->count() > 0 ? '5' : '4' }}">
                            <strong>الإجمالي النهائي</strong>
                        </td>
                        <td><strong>{{ number_format($totalAmount, 2) }} ريال</strong></td>
                        <td></td>
                    </tr>
                @endif
            </tbody>
        </table>

        <!-- ملخص الأمر -->
        <div class="summary-section">
            <div class="summary-box">
                <div class="summary-title">ملخص الأمر</div>
                <div class="summary-item">
                    <span class="summary-label">إجمالي المنتجات:</span>
                    <span class="summary-value">{{ $receiptOrder->total_products }} منتج</span>
                </div>
                @if($receiptOrder->order_type === 'استلام بضاعة')
                    <div class="summary-item">
                        <span class="summary-label">إجمالي المبلغ:</span>
                        <span class="summary-value">{{ number_format($receiptOrder->total_amount, 2) }} ريال</span>
                    </div>
                @endif
                <div class="summary-item">
                    <span class="summary-label">الحالة:</span>
                    <span class="summary-value">
                        <span class="badge badge-{{ $receiptOrder->status_color }}">{{ $receiptOrder->status }}</span>
                    </span>
                </div>
            </div>
        </div>

        <!-- الملاحظات -->
        @if($receiptOrder->notes)
            <div class="notes-section">
                <div class="notes-title">ملاحظات هامة:</div>
                <div class="notes-content">{{ $receiptOrder->notes }}</div>
            </div>
        @endif

        <!-- التوقيعات -->
        <div class="signatures-section">
            <div class="signature-box">
                <div class="signature-line">توقيع المستلم</div>
            </div>
            <div class="signature-box">
                <div class="signature-line">توقيع المسؤول</div>
            </div>
            <div class="signature-box">
                <div class="signature-line">ختم الشركة</div>
            </div>
        </div>

        <!-- الفوتر -->
        <div class="footer">
            <div><strong>تاريخ الطباعة:</strong> {{ now()->format('Y-m-d H:i') }} | <strong>طُبع بواسطة:</strong> {{ Auth::check() ? Auth::user()->name : 'النظام' }}</div>
            <div style="margin-top: 8px;">تم إنشاء هذه الفاتورة بواسطة نظام إدارة المستودعات - {{ $companyData['name'] }}</div>
        </div>
    </div>
</body>
</html>
