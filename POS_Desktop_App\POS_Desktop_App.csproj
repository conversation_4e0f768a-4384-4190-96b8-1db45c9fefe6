<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <ApplicationIcon>Assets\pos-icon.ico</ApplicationIcon>
    <AssemblyTitle>نظام نقاط البيع</AssemblyTitle>
    <AssemblyDescription>تطبيق سطح مكتب لإدارة نقاط البيع مع مزامنة السيرفر</AssemblyDescription>
    <AssemblyCompany>شركتك</AssemblyCompany>
    <AssemblyProduct>POS Desktop Application</AssemblyProduct>
    <AssemblyCopyright>Copyright © 2024</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateAssemblyInfo>true</GenerateAssemblyInfo>
    <StartupObject>POS_Desktop_App.App</StartupObject>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DefineConstants>TRACE</DefineConstants>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="7.0.14" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="7.0.14">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="7.0.14" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="7.0.14">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="7.0.0" />
    <PackageReference Include="MySql.Data" Version="8.2.0" />
    
    <!-- WPF and UI Libraries -->
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="MaterialDesignColors" Version="2.1.4" />
    <PackageReference Include="Microsoft.Xaml.Behaviors.Wpf" Version="1.1.77" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
    
    <!-- HTTP Client and JSON -->
    <PackageReference Include="Microsoft.Extensions.Http" Version="7.0.0" />
    <PackageReference Include="System.Text.Json" Version="7.0.3" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    
    <!-- Dependency Injection -->
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="7.0.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="7.0.0" />
    
    <!-- Printing Libraries -->
    <PackageReference Include="System.Drawing.Common" Version="7.0.0" />
    <PackageReference Include="System.Printing" Version="7.0.0" />
    
    <!-- Barcode and QR Code -->
    <PackageReference Include="ZXing.Net" Version="0.16.9" />
    <PackageReference Include="ZXing.Net.Bindings.Windows.Compatibility" Version="0.16.12" />
    
    <!-- Excel Export -->
    <PackageReference Include="EPPlus" Version="7.0.4" />
    <PackageReference Include="ClosedXML" Version="0.102.1" />
    
    <!-- Encryption and Security -->
    <PackageReference Include="System.Security.Cryptography.Algorithms" Version="4.3.1" />
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    
    <!-- Configuration -->
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="7.0.0" />
    
    <!-- Validation -->
    <PackageReference Include="FluentValidation" Version="11.8.0" />
    
    <!-- AutoMapper -->
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    
    <!-- Polly for Resilience -->
    <PackageReference Include="Polly" Version="7.2.4" />
    <PackageReference Include="Polly.Extensions.Http" Version="3.0.0" />
    
    <!-- Serilog for Logging -->
    <PackageReference Include="Serilog" Version="3.0.1" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="7.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="4.1.0" />
    <PackageReference Include="Serilog.Extensions.Logging.File" Version="3.0.0" />
    
    <!-- Notifications -->
    <PackageReference Include="Hardcodet.NotifyIcon.Wpf" Version="1.1.0" />
    
    <!-- Image Processing -->
    <PackageReference Include="ImageSharp" Version="3.0.2" />
    
    <!-- Utilities -->
    <PackageReference Include="Humanizer" Version="2.14.1" />
    <PackageReference Include="CsvHelper" Version="30.0.1" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Assets\**" />
    <Resource Include="Assets\**" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Assets\Images\" />
    <Folder Include="Assets\Icons\" />
    <Folder Include="Assets\Fonts\" />
    <Folder Include="Data\" />
    <Folder Include="Logs\" />
    <Folder Include="Backups\" />
    <Folder Include="Exports\" />
    <Folder Include="Reports\" />
    <Folder Include="Temp\" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Assets\pos-icon.ico" />
    <Resource Include="Assets\Images\logo.png" />
    <Resource Include="Assets\Images\no-image.png" />
    <Resource Include="Assets\Images\default-avatar.png" />
  </ItemGroup>

  <ItemGroup>
    <None Update="App.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.Development.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.Production.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Properties\Settings.Designer.cs">
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <None Update="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>

  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="if not exist &quot;$(TargetDir)Data&quot; mkdir &quot;$(TargetDir)Data&quot;&#xD;&#xA;if not exist &quot;$(TargetDir)Logs&quot; mkdir &quot;$(TargetDir)Logs&quot;&#xD;&#xA;if not exist &quot;$(TargetDir)Backups&quot; mkdir &quot;$(TargetDir)Backups&quot;&#xD;&#xA;if not exist &quot;$(TargetDir)Exports&quot; mkdir &quot;$(TargetDir)Exports&quot;&#xD;&#xA;if not exist &quot;$(TargetDir)Reports&quot; mkdir &quot;$(TargetDir)Reports&quot;&#xD;&#xA;if not exist &quot;$(TargetDir)Temp&quot; mkdir &quot;$(TargetDir)Temp&quot;" />
  </Target>

</Project>
