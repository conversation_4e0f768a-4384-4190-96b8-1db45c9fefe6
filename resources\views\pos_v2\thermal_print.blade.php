<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ __('POS V2 Thermal Print') }} - {{ $pos->pos_id }}</title>
    <style>
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
        
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 10px;
            background: white;
            color: #000000;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .thermal-print {
            width: 80mm;
            max-width: 300px;
            margin: 0 auto;
            background: white;
            color: #000000;
            padding: 10px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 15px;
            border-bottom: 2px solid #000000;
            padding-bottom: 10px;
        }
        
        .logo-container {
            margin-bottom: 8px;
        }
        
        .logo-img {
            max-width: 60px;
            max-height: 60px;
            object-fit: contain;
        }
        
        .company-name {
            font-size: 16px;
            font-weight: bold;
            color: #000000;
            margin: 5px 0;
        }
        
        .receipt-info {
            font-size: 10px;
            color: #000000;
            margin: 5px 0;
        }
        
        .bilingual {
            margin: 2px 0;
        }
        
        .ar {
            font-weight: bold;
            color: #000000;
        }
        
        .en {
            font-style: italic;
            color: #000000;
        }
        
        .invoice-details {
            margin: 15px 0;
            border-bottom: 1px solid #000000;
            padding-bottom: 10px;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin: 3px 0;
            font-size: 11px;
            color: #000000;
        }
        
        .detail-label {
            font-weight: bold;
            color: #000000;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            color: #000000;
        }
        
        .items-table th,
        .items-table td {
            padding: 4px 2px;
            text-align: right;
            border-bottom: 1px solid #000000;
            font-size: 10px;
            color: #000000;
        }
        
        .items-table th {
            font-weight: bold;
            background-color: #f0f0f0;
            color: #000000;
        }
        
        .item-name {
            text-align: right;
            max-width: 80px;
            word-wrap: break-word;
            color: #000000;
        }
        
        .totals-section {
            margin-top: 15px;
            border-top: 2px solid #000000;
            padding-top: 10px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            font-size: 11px;
            color: #000000;
        }
        
        .total-row.grand-total {
            font-weight: bold;
            font-size: 14px;
            border-top: 1px solid #000000;
            padding-top: 5px;
            color: #000000;
        }
        
        .footer {
            text-align: center;
            margin-top: 20px;
            border-top: 1px solid #000000;
            padding-top: 10px;
            font-size: 10px;
            color: #000000;
        }
        
        .thank-you {
            font-weight: bold;
            margin: 10px 0;
            color: #000000;
        }
        
        .print-controls {
            text-align: center;
            margin: 20px 0;
        }
        
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .btn-print {
            background-color: #007bff;
            color: white;
        }
        
        .btn-close {
            background-color: #6c757d;
            color: white;
        }
    </style>
</head>
<body>
    <div class="thermal-print" id="printarea">
        <!-- شعار واسم الشركة -->
        <div class="header">
            <div class="logo-container">
                <img src="{{ $company_logo }}" alt="{{ $settings['company_name'] ?? 'شركتي' }}" class="logo-img">
            </div>
            <div class="company-name">{{ $settings['company_name'] ?? 'شركتي' }}</div>

            <div class="receipt-info">
                <div class="bilingual">
                    <div class="ar">العنوان: {{ $settings['company_address'] ?? 'المملكة العربية السعودية' }}</div>
                    <div class="en">Address: {{ $settings['company_address'] ?? 'Saudi Arabia' }}</div>
                </div>
                <div class="bilingual">
                    <div class="ar">الرقم الضريبي: {{ $settings['vat_number'] ?? '*********' }}</div>
                    <div class="en">VAT Number: {{ $settings['vat_number'] ?? '*********' }}</div>
                </div>
                <div class="bilingual">
                    <div class="ar">الهاتف: {{ $settings['company_telephone'] ?? '+966 XX XXX XXXX' }}</div>
                    <div class="en">Phone: {{ $settings['company_telephone'] ?? '+966 XX XXX XXXX' }}</div>
                </div>
            </div>
        </div>

        <!-- تفاصيل الفاتورة -->
        <div class="invoice-details">
            <div class="detail-row">
                <span class="detail-label">رقم الفاتورة / Invoice No:</span>
                <span>{{ \Auth::user()->posNumberFormat($pos->pos_id) }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">التاريخ / Date:</span>
                <span>{{ \Utility::dateFormat($settings, $pos->pos_date) }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">الوقت / Time:</span>
                <span>{{ $pos->created_at->format('H:i:s') }}</span>
            </div>
            @if($pos->customer && $pos->customer->name != 'Walk-in-customer')
            <div class="detail-row">
                <span class="detail-label">العميل / Customer:</span>
                <span>{{ $pos->customer->name }}</span>
            </div>
            @endif
            @if($pos->warehouse)
            <div class="detail-row">
                <span class="detail-label">المخزن / Warehouse:</span>
                <span>{{ $pos->warehouse->name }}</span>
            </div>
            @endif
            <div class="detail-row">
                <span class="detail-label">الكاشير / Cashier:</span>
                <span>{{ $pos->createdBy->name ?? 'غير محدد' }}</span>
            </div>
        </div>

        <!-- جدول المنتجات -->
        <table class="items-table">
            <thead>
                <tr>
                    <th style="text-align: right;">المنتج</th>
                    <th style="text-align: center;">الكمية</th>
                    <th style="text-align: right;">السعر</th>
                    <th style="text-align: right;">المجموع</th>
                </tr>
            </thead>
            <tbody>
                @php
                    $subtotal = 0;
                    $totalTax = 0;
                    $totalDiscount = 0;
                @endphp
                @foreach($pos->items as $item)
                @php
                    $itemTotal = $item->price * $item->quantity;
                    $subtotal += $itemTotal;
                    $totalDiscount += $item->discount;
                    
                    // حساب الضريبة
                    $itemTax = 0;
                    if (!empty($item->tax)) {
                        $taxes = explode(',', $item->tax);
                        foreach ($taxes as $taxRate) {
                            $itemTax += ($itemTotal * $taxRate) / 100;
                        }
                    }
                    $totalTax += $itemTax;
                @endphp
                <tr>
                    <td class="item-name">{{ $item->product->name ?? 'منتج محذوف' }}</td>
                    <td style="text-align: center;">{{ $item->quantity }}</td>
                    <td style="text-align: right;">{{ \Auth::user()->priceFormat($item->price) }}</td>
                    <td style="text-align: right;">{{ \Auth::user()->priceFormat($itemTotal) }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>

        <!-- المجاميع -->
        <div class="totals-section">
            <div class="total-row">
                <span>المجموع الفرعي / Subtotal:</span>
                <span>{{ \Auth::user()->priceFormat($subtotal) }}</span>
            </div>
            
            @if($totalDiscount > 0)
            <div class="total-row">
                <span>الخصم / Discount:</span>
                <span>-{{ \Auth::user()->priceFormat($totalDiscount) }}</span>
            </div>
            @endif
            
            @if($totalTax > 0)
            <div class="total-row">
                <span>ضريبة القيمة المضافة / VAT:</span>
                <span>{{ \Auth::user()->priceFormat($totalTax) }}</span>
            </div>
            @endif
            
            <div class="total-row grand-total">
                <span>الإجمالي / Total:</span>
                <span>{{ \Auth::user()->priceFormat($subtotal + $totalTax - $totalDiscount) }}</span>
            </div>
        </div>

        <!-- معلومات الدفع -->
        @if($pos->posPayment)
        <div class="invoice-details">
            <div class="detail-row">
                <span class="detail-label">طريقة الدفع / Payment Method:</span>
                <span>
                    @if($pos->posPayment->payment_type == 'cash')
                        نقدي / Cash
                    @elseif($pos->posPayment->payment_type == 'network')
                        شبكة / Network
                    @elseif($pos->posPayment->payment_type == 'split')
                        مختلط / Split
                    @endif
                </span>
            </div>
            
            @if($pos->posPayment->payment_type == 'network' && $pos->posPayment->transaction_number)
            <div class="detail-row">
                <span class="detail-label">رقم المعاملة / Transaction No:</span>
                <span>{{ $pos->posPayment->transaction_number }}</span>
            </div>
            @endif
            
            @if($pos->posPayment->payment_type == 'split')
                @if($pos->posPayment->cash_amount > 0)
                <div class="detail-row">
                    <span class="detail-label">المبلغ النقدي / Cash Amount:</span>
                    <span>{{ \Auth::user()->priceFormat($pos->posPayment->cash_amount) }}</span>
                </div>
                @endif
                @if($pos->posPayment->network_amount > 0)
                <div class="detail-row">
                    <span class="detail-label">مبلغ الشبكة / Network Amount:</span>
                    <span>{{ \Auth::user()->priceFormat($pos->posPayment->network_amount) }}</span>
                </div>
                @endif
            @endif
        </div>
        @endif

        <!-- تذييل الفاتورة -->
        <div class="footer">
            <div class="thank-you">
                <div class="ar">شكراً لزيارتكم</div>
                <div class="en">Thank you for your visit</div>
            </div>
            <div class="receipt-info">
                <div>تم إنشاء هذه الفاتورة بواسطة نظام نقاط البيع المتقدم V2</div>
                <div>Generated by Advanced POS System V2</div>
                <div style="margin-top: 10px;">
                    <div>{{ now()->format('Y-m-d H:i:s') }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار التحكم -->
    <div class="print-controls no-print">
        <button class="btn btn-print" onclick="window.print()">
            <i class="ti ti-printer"></i> طباعة / Print
        </button>
        <button class="btn btn-close" onclick="window.close()">
            <i class="ti ti-x"></i> إغلاق / Close
        </button>
    </div>

    <script>
        // طباعة تلقائية عند تحميل الصفحة
        window.onload = function() {
            setTimeout(function() {
                window.print();
            }, 500);
        };

        // إغلاق النافذة بعد الطباعة
        window.onafterprint = function() {
            setTimeout(function() {
                window.close();
            }, 1000);
        };
    </script>
</body>
</html>
