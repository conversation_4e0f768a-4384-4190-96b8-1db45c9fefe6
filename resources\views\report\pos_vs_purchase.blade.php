@extends('layouts.admin')
@section('page-title')
    {{__('POS Vs Purchase')}}
@endsection

@push('css-page')
<style>
    .apexcharts-yaxis
    {
        transform: translate(15px, 0px) !important;
    }

    /* تحسينات واجهة المستخدم لعرض البيانات في بطاقات */
    .card.border {
        transition: all 0.3s ease;
    }
    .card.border:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    .theme-avtar {
        width: 45px;
        height: 45px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: #fff;
    }
</style>
@endpush

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item">{{__('POS Vs Purchase')}}</li>
@endsection

@push('theme-script')
    <script src="{{ asset('assets/libs/apexcharts/dist/apexcharts.min.js') }}"></script>
@endpush
@push('script-page')
    <script>
        (function () {
            var chartBarOptions = {
                series: [
                    {
                        name: '{{ __("Monthly POS Sales") }}',
                        data: {!! json_encode(is_array($posMonthly) ? $posMonthly : []) !!},
                        type: 'column'
                    },
                    {
                        name: '{{ __("Monthly Purchases") }}',
                        data: {!! json_encode(is_array($purchaseMonthly) ? $purchaseMonthly : []) !!},
                        type: 'column'
                    },
                    {
                        name: '{{ __("Monthly Profit") }}',
                        data: {!! json_encode(array_map(function($val) { return (float)str_replace(",", "", $val); }, is_array($monthlyProfits) ? $monthlyProfits : [])) !!},
                        type: 'line'
                    }
                ],
                chart: {
                    height: 350,
                    type: 'line',
                    stacked: false,
                    toolbar: {
                        show: true,
                        tools: {
                            download: true,
                            selection: true,
                            zoom: true,
                            zoomin: true,
                            zoomout: true,
                            pan: true,
                            reset: true
                        }
                    },
                    animations: {
                        enabled: true,
                        easing: 'easeinout',
                        speed: 800
                    }
                },
                stroke: {
                    width: [0, 0, 3],
                    curve: 'smooth'
                },
                plotOptions: {
                    bar: {
                        columnWidth: '50%',
                        borderRadius: 5,
                        dataLabels: {
                            position: 'top',
                        },
                    }
                },
                dataLabels: {
                    enabled: false,
                    formatter: function (val) {
                        return val;
                    },
                    offsetY: -20,
                    style: {
                        fontSize: '12px',
                        colors: ["#304758"]
                    }
                },
                title: {
                    text: '{{ __("Monthly POS Sales vs Purchases") }}',
                    align: 'left',
                    style: {
                        fontSize: '16px',
                        fontWeight: 'bold'
                    }
                },
                xaxis: {
                    categories: {!! json_encode(is_array($monthList) ? $monthList : []) !!},
                    title: {
                        text: '{{ __("Months") }}'
                    }
                },
                colors: ['#3ec9d6', '#FF3A6E', '#6fd943'],
                yaxis: [
                    {
                        axisTicks: {
                            show: true,
                        },
                        axisBorder: {
                            show: true,
                            color: '#3ec9d6'
                        },
                        labels: {
                            style: {
                                colors: '#3ec9d6',
                            }
                        },
                        title: {
                            text: "{{ __('Monthly POS Sales') }}",
                            style: {
                                color: '#3ec9d6',
                            }
                        }
                    },
                    {
                        opposite: true,
                        axisTicks: {
                            show: true,
                        },
                        axisBorder: {
                            show: true,
                            color: '#FF3A6E'
                        },
                        labels: {
                            style: {
                                colors: '#FF3A6E',
                            }
                        },
                        title: {
                            text: "{{ __('Monthly Purchases') }}",
                            style: {
                                color: '#FF3A6E',
                            }
                        }
                    },
                    {
                        opposite: true,
                        axisTicks: {
                            show: true,
                        },
                        axisBorder: {
                            show: true,
                            color: '#6fd943'
                        },
                        labels: {
                            style: {
                                colors: '#6fd943',
                            }
                        },
                        title: {
                            text: "{{ __('Monthly Profit') }}",
                            style: {
                                color: '#6fd943',
                            }
                        }
                    },
                ],
                tooltip: {
                    shared: true,
                    intersect: false,
                    y: {
                        formatter: function (y) {
                            if (typeof y !== "undefined") {
                                return "{{\Auth::user()->priceFormat('')}}" + y.toFixed(2);
                            }
                            return y;
                        }
                    }
                },
                legend: {
                    show: true,
                    position: 'bottom',
                    horizontalAlign: 'center',
                    offsetX: 0,
                    offsetY: 0
                }
            };
            var arChart = new ApexCharts(document.querySelector("#pos-vs-purchase"), chartBarOptions);
            arChart.render();
        })();
    </script>
    <script type="text/javascript" src="{{ asset('js/html2pdf.bundle.min.js') }}"></script>
    <script>
        var year = '{{$currentYear}}';
        var filename = $('#filename').val();

        function saveAsPDF() {
            var element = document.getElementById('printableArea');
            var opt = {
                margin: 0.3,
                filename: filename,
                image: {type: 'jpeg', quality: 1},
                html2canvas: {scale: 4, dpi: 72, letterRendering: true},
                jsPDF: {unit: 'in', format: 'A2'}
            };
            html2pdf().set(opt).from(element).save();

        }
    </script>
@endpush


@section('action-btn')
    <div class="float-end">
        <a href="#" class="btn btn-sm btn-primary" onclick="saveAsPDF()"data-bs-toggle="tooltip" title="{{__('Download')}}"
           data-original-title="{{__('Download')}}">
            <span class="btn-inner--icon"><i class="ti ti-download"></i></span>
        </a>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-sm-12">
            <div class="mt-2 " id="multiCollapseExample1">
                <div class="card">
                    <div class="card-body">
                        {{ Form::open(array('route' => array('report.pos.vs.purchase'),'method' => 'GET','id'=>'pos_vs_purchase')) }}
                        <div class="row align-items-center justify-content-end">
                            <div class="col-xl-10">
                                <div class="row ">
                                    <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 col-12">
                                        <div class="btn-box">
                                        </div>
                                    </div>

                                    <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 col-12">
                                        <div class="btn-box">
                                        </div>
                                    </div>
                                    <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 col-12">
                                        <div class="btn-box">
                                        </div>
                                    </div>
                                    <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 col-12">
                                        <div class="btn-box">
                                            {{ Form::label('year', __('Year'),['class'=>'form-label'])}}
                                            {{ Form::select('year',$yearList,isset($_GET['year'])?$_GET['year']:'', array('class' => 'form-control select')) }}
                                        </div>
                                    </div>

                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="row">
                                    <div class="col-auto mt-4">
                                        <a href="#" class="btn btn-sm btn-primary me-1" onclick="document.getElementById('pos_vs_purchase').submit(); return false;" data-bs-toggle="tooltip" title="{{__('Apply')}}" data-original-title="{{__('apply')}}">
                                            <span class="btn-inner--icon"><i class="ti ti-search"></i></span>
                                        </a>
                                        <a href="{{route('report.pos.vs.purchase')}}" class="btn btn-sm btn-danger " data-bs-toggle="tooltip"  title="{{ __('Reset') }}" data-original-title="{{__('Reset')}}">
                                            <span class="btn-inner--icon"><i class="ti ti-refresh text-white-off "></i></span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {{ Form::close() }}
                </div>
            </div>
        </div>
    </div>


    <div id="printableArea">
        <div class="row mt-0">
            <div class="col">
                <input type="hidden" value="{{__('POS Vs Purchase').' '.'Report of'.' '.$filter['startDateRange'].' to '.$filter['endDateRange']}}" id="filename">
                <div class="card p-4 mb-4">
                    <h7 class="report-text gray-text mb-0">{{__('Report')}} :</h7>
                    <h6 class="report-text mb-0">{{__('POS Vs Purchase')}}</h6>
                </div>
            </div>
            <div class="col">
                <div class="card p-4 mb-4">
                    <h7 class="report-text gray-text mb-0">{{__('Duration')}} :</h7>
                    <h6 class="report-text mb-0">{{$filter['startDateRange'].' to '.$filter['endDateRange']}}</h6>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12" id="chart-container">
                <div class="card">
                    <div class="card-header">
                        <h5>{{ __('Monthly Sales vs Purchases Analysis') }}</h5>
                        <small class="text-muted">{{ __('Visual comparison of monthly POS sales, purchases and profit for') }} {{ $currentYear }}</small>
                    </div>
                    <div class="card-body">
                        <div class="scrollbar-inner">
                            <div id="pos-vs-purchase" data-color="primary" data-height="350"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إجماليات في بطاقات -->
        <div class="row">
            <!-- إجمالي المبيعات -->
            <div class="col-xl-4 col-md-6 col-sm-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center justify-content-between">
                            <div class="col-auto mb-3 mb-sm-0">
                                <div class="d-flex align-items-center">
                                    <div class="theme-avtar bg-primary">
                                        <i class="ti ti-shopping-cart"></i>
                                    </div>
                                    <div class="ms-3">
                                        <h6 class="m-0">{{ __('Current Month POS Sales') }}</h6>
                                        <small class="text-muted">{{ __('Sales for') }} {{ $monthList[date('n')-1] }} {{ $currentYear }}</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-auto text-end">
                                <h4 class="m-0 text-primary">
                                    {{\Auth::user()->priceFormat($currentMonthPos)}}
                                </h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إجمالي المشتريات -->
            <div class="col-xl-4 col-md-6 col-sm-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center justify-content-between">
                            <div class="col-auto mb-3 mb-sm-0">
                                <div class="d-flex align-items-center">
                                    <div class="theme-avtar bg-info">
                                        <i class="ti ti-truck-delivery"></i>
                                    </div>
                                    <div class="ms-3">
                                        <h6 class="m-0">{{ __('Current Month Purchases') }}</h6>
                                        <small class="text-muted">{{ __('Purchases for') }} {{ $monthList[date('n')-1] }} {{ $currentYear }}</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-auto text-end">
                                <h4 class="m-0 text-info">
                                    {{\Auth::user()->priceFormat($currentMonthPurchase)}}
                                </h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إجمالي الأرباح -->
            <div class="col-xl-4 col-md-6 col-sm-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center justify-content-between">
                            <div class="col-auto mb-3 mb-sm-0">
                                <div class="d-flex align-items-center">
                                    <div class="theme-avtar bg-success">
                                        <i class="ti ti-chart-bar"></i>
                                    </div>
                                    <div class="ms-3">
                                        <h6 class="m-0">{{ __('Current Month Profit') }}</h6>
                                        <small class="text-muted">{{ __('Sales - Purchases for') }} {{ $monthList[date('n')-1] }} {{ $currentYear }}</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-auto text-end">
                                @php
                                    $profitClass = $currentMonthProfit >= 0 ? 'text-success' : 'text-danger';
                                @endphp
                                <h4 class="m-0 {{ $profitClass }}">
                                    {{\Auth::user()->priceFormat($currentMonthProfit)}}
                                </h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- بيانات شهرية في بطاقات -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>{{ __('Monthly Breakdown (Non-Cumulative)') }}</h5>
                        <small class="text-muted">{{ __('Monthly values for each individual month') }}</small>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach($monthList as $index => $month)
                                @php
                                    $monthProfit = isset($monthlyProfits[$index]) ? str_replace(",", "", $monthlyProfits[$index]) : 0;
                                    $cardClass = $monthProfit >= 0 ? 'border-success' : 'border-danger';
                                    $iconClass = $monthProfit >= 0 ? 'ti ti-trending-up text-success' : 'ti ti-trending-down text-danger';
                                @endphp
                                <div class="col-xl-3 col-md-6 col-sm-12 mb-3">
                                    <div class="card {{ $cardClass }} border">
                                        <div class="card-header bg-transparent">
                                            <h6 class="mb-0">{{ $month }}</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-6">
                                                    <span class="text-muted">{{ __('POS') }}</span>
                                                    <h6 class="mb-2">{{\Auth::user()->priceFormat(isset($posMonthly[$index]) ? $posMonthly[$index] : 0)}}</h6>
                                                </div>
                                                <div class="col-6">
                                                    <span class="text-muted">{{ __('Purchase') }}</span>
                                                    <h6 class="mb-2">{{\Auth::user()->priceFormat(isset($purchaseMonthly[$index]) ? $purchaseMonthly[$index] : 0)}}</h6>
                                                </div>
                                            </div>
                                            <div class="d-flex align-items-center justify-content-between mt-3">
                                                <div>
                                                    <span class="text-muted">{{ __('Profit') }}</span>
                                                    <h6 class="mb-0">{{\Auth::user()->priceFormat(isset($monthlyProfits[$index]) ? str_replace(",", "", $monthlyProfits[$index]) : 0)}}</h6>
                                                </div>
                                                <div>
                                                    <i class="{{ $iconClass }} h3"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول البيانات التفصيلية - القيم الشهرية -->
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('Monthly Data') }}</h5>
                    <small class="text-muted">{{ __('Monthly values (non-cumulative)') }}</small>
                </div>
                <div class="card-body table-border-style">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                            <tr>
                                <th>{{__('Type')}}</th>
                                @foreach($monthList as $month)
                                    <th>{{$month}}</th>
                                @endforeach
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>{{(__('POS'))}}</td>
                                @foreach($posMonthly as $pos)
                                    <td>{{\Auth::user()->priceFormat($pos)}}</td>
                                @endforeach
                            </tr>
                            <tr>
                                <td>{{(__('Purchase'))}}</td>
                                @foreach($purchaseMonthly as $purchase)
                                    <td>{{\Auth::user()->priceFormat($purchase)}}</td>
                                @endforeach
                            </tr>
                            <tr>
                                <td colspan="13" class="text-dark"><span>{{__('Monthly Profit = POS - Purchase')}}</span></td>
                            </tr>
                            <tr>
                                <td><h6>{{(__('Monthly Profit'))}}</h6></td>
                                @foreach($monthlyProfits as $profit)
                                    <td>{{\Auth::user()->priceFormat(str_replace(",", "", $profit))}}</td>
                                @endforeach
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول البيانات التفصيلية - القيم التراكمية -->
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('Cumulative Data') }}</h5>
                    <small class="text-muted">{{ __('Cumulative values (running total)') }}</small>
                </div>
                <div class="card-body table-border-style">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                            <tr>
                                <th>{{__('Type')}}</th>
                                @foreach($monthList as $month)
                                    <th>{{$month}}</th>
                                @endforeach
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>{{(__('POS (Cumulative)'))}}</td>
                                @foreach($posTotal as $pos)
                                    <td>{{\Auth::user()->priceFormat($pos)}}</td>
                                @endforeach
                            </tr>
                            <tr>
                                <td>{{(__('Purchase (Cumulative)'))}}</td>
                                @foreach($purchaseTotal as $purchase)
                                    <td>{{\Auth::user()->priceFormat($purchase)}}</td>
                                @endforeach
                            </tr>
                            <tr>
                                <td colspan="13" class="text-dark"><span>{{__('Cumulative Profit = Cumulative POS - Cumulative Purchase')}}</span></td>
                            </tr>
                            <tr>
                                <td><h6>{{(__('Cumulative Profit'))}}</h6></td>
                                @foreach($profits as $profit)
                                    <td>{{\Auth::user()->priceFormat(str_replace(",", "", $profit))}}</td>
                                @endforeach
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Debug Information (Only visible to admins) -->
        @if(Auth::user()->type == 'super admin' || Auth::user()->type == 'company')
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h5>{{ __('Debug Information') }}</h5>
                        <small class="text-muted">{{ __('Raw data for troubleshooting') }}</small>
                    </div>
                    <button class="btn btn-sm btn-primary" type="button" data-bs-toggle="collapse" data-bs-target="#debugInfoCollapse" aria-expanded="false" aria-controls="debugInfoCollapse">
                        {{ __('Show/Hide') }}
                    </button>
                </div>
                <div class="collapse" id="debugInfoCollapse">
                    <div class="card-body">
                        @if(isset($sql_pos))
                            <h6>{{__('POS SQL')}}</h6>
                            <pre class="bg-light p-3 rounded">{{ $sql_pos }}</pre>
                            @if(isset($sql_pos_bindings))
                                <p>{{__('Year Parameter')}}: {{ $sql_pos_bindings }}</p>
                            @endif
                        @endif

                        @if(isset($sql_purchase))
                            <h6>{{__('Purchase SQL')}}</h6>
                            <pre class="bg-light p-3 rounded">{{ $sql_purchase }}</pre>
                            @if(isset($sql_purchase_bindings))
                                <p>{{__('Year Parameter')}}: {{ $sql_purchase_bindings }}</p>
                            @endif
                        @endif
                    </div>
                </div>
            </div>
        </div>
        @endif

    </div>

@endsection


