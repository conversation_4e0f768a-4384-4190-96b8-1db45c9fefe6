<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير الجرد - {{ $warehouse->name }}</title>
    <style>
        @page {
            margin: 15mm 10mm 20mm 10mm;
            size: A4;
            @bottom-center {
                content: "صفحة " counter(page) " من " counter(pages);
                font-family: 'DejaVu Sans', sans-serif;
                font-size: 9px;
                color: #555;
            }
        }

        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                margin: 0;
                padding: 0;
            }
            .page-break {
                page-break-before: always;
            }
            .no-print {
                display: none;
            }
            .print-button {
                display: none;
            }
            .products-table {
                page-break-inside: avoid;
            }
            .products-table tr {
                page-break-inside: avoid;
            }
        }

        .print-button {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-family: Arial, sans-serif;
        }

        .print-button:hover {
            background: #0056b3;
        }

        body {
            font-family: 'DejaVu Sans', sans-serif;
            font-size: 11px;
            line-height: 1.3;
            color: #000;
            margin: 0;
            padding: 0;
            direction: rtl;
            background: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 25px;
            border: 3px solid #000;
            padding: 15px;
            background-color: #f8f9fa;
        }

        .header h1 {
            font-size: 22px;
            font-weight: bold;
            margin: 0 0 8px 0;
            color: #000;
            text-transform: uppercase;
        }

        .header h2 {
            font-size: 16px;
            margin: 0;
            color: #333;
            font-weight: bold;
        }

        .info-section {
            width: 100%;
            margin-bottom: 20px;
            border-collapse: collapse;
        }

        .info-section td {
            width: 50%;
            vertical-align: top;
            padding: 0 5px;
        }

        .info-box {
            border: 2px solid #000;
            padding: 12px;
            margin: 3px;
            background-color: #fff;
        }

        .info-box h3 {
            margin: 0 0 8px 0;
            font-size: 13px;
            font-weight: bold;
            color: #000;
            border-bottom: 1px solid #000;
            padding-bottom: 3px;
            text-align: center;
        }

        .info-item {
            margin: 5px 0;
            font-size: 11px;
            line-height: 1.4;
        }

        .info-label {
            font-weight: bold;
            color: #000;
            display: inline-block;
            width: 85px;
        }
        
        .products-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 25px;
            font-size: 10px;
            border: 2px solid #000;
        }

        .products-table th {
            background-color: #000;
            color: white;
            padding: 8px 4px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #000;
            font-size: 10px;
        }

        .products-table td {
            padding: 6px 4px;
            text-align: center;
            border: 1px solid #000;
            vertical-align: middle;
            min-height: 25px;
        }

        .products-table tbody tr:nth-child(even) {
            background-color: #f5f5f5;
        }

        .product-name-cell {
            text-align: right;
            padding-right: 8px;
            font-weight: bold;
        }

        .barcode-cell {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            font-size: 9px;
            background-color: #fff;
            border: 2px solid #000;
            padding: 4px;
        }

        .current-quantity-cell {
            font-weight: bold;
            font-size: 11px;
            background-color: #e8f5e8;
        }

        .actual-quantity-cell {
            background-color: #fff;
            border: 2px solid #000;
            min-height: 35px;
            position: relative;
        }

        .actual-quantity-cell::after {
            content: "";
            position: absolute;
            bottom: 5px;
            right: 10px;
            left: 10px;
            border-bottom: 1px solid #ccc;
        }

        .signature-cell {
            background-color: #fff;
            border: 2px solid #000;
            min-height: 35px;
        }
        
        .summary-box {
            border: 2px solid #000;
            padding: 12px;
            margin: 15px 0;
            text-align: center;
            background-color: #f0f0f0;
        }

        .summary-title {
            font-size: 14px;
            font-weight: bold;
            color: #000;
            margin-bottom: 8px;
        }

        .summary-content {
            font-size: 12px;
            color: #000;
            font-weight: bold;
        }

        .footer-section {
            margin-top: 30px;
            border-top: 3px solid #000;
            padding-top: 15px;
        }

        .signature-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            border: 2px solid #000;
        }

        .signature-table td {
            padding: 12px 8px;
            text-align: center;
            border: 1px solid #000;
            vertical-align: top;
            height: 70px;
            background-color: #fff;
        }

        .signature-label {
            font-weight: bold;
            font-size: 12px;
            margin-bottom: 8px;
            color: #000;
        }

        .signature-line {
            margin: 15px 5px 8px 5px;
            border-bottom: 1px solid #000;
            height: 25px;
        }

        .signature-info {
            font-size: 10px;
            color: #000;
            font-weight: bold;
        }

        .page-break {
            page-break-before: always;
        }

        .no-products {
            text-align: center;
            padding: 30px;
            color: #000;
            border: 2px solid #000;
            background-color: #f8f9fa;
            margin: 20px 0;
        }

        .no-products h3 {
            font-size: 14px;
            margin-bottom: 10px;
            color: #000;
        }

        .no-products p {
            font-size: 12px;
            color: #333;
        }
    </style>
</head>
<body>
    <!-- Print Button -->
    <button class="print-button no-print" onclick="window.print()">🖨️ طباعة التقرير</button>

    <!-- Header -->
    <div class="header">
        <h1>تقرير جرد المخزون</h1>
        <h2>{{ $warehouse->name }}</h2>
    </div>

    <!-- Information Section -->
    <table class="info-section">
        <tr>
            <td>
                <div class="info-box">
                    <h3>معلومات المستودع</h3>
                    <div class="info-item">
                        <span class="info-label">اسم المستودع:</span>
                        {{ $warehouse->name }}
                    </div>
                    <div class="info-item">
                        <span class="info-label">الموقع:</span>
                        {{ $warehouse->location ?? 'غير محدد' }}
                    </div>
                    <div class="info-item">
                        <span class="info-label">تاريخ الجرد:</span>
                        {{ $inventory_date }}
                    </div>
                    <div class="info-item">
                        <span class="info-label">وقت الجرد:</span>
                        {{ $inventory_time }}
                    </div>
                </div>
            </td>
            <td>
                <div class="info-box">
                    <h3>معلومات الموظف</h3>
                    <div class="info-item">
                        <span class="info-label">اسم الموظف:</span>
                        {{ $user->name }}
                    </div>
                    <div class="info-item">
                        <span class="info-label">رقم الموظف:</span>
                        {{ isset($employee->employee_id) ? $employee->employee_id : 'غير محدد' }}
                    </div>
                    <div class="info-item">
                        <span class="info-label">البريد الإلكتروني:</span>
                        {{ $user->email }}
                    </div>
                    <div class="info-item">
                        <span class="info-label">تاريخ التقرير:</span>
                        {{ now()->format('Y-m-d H:i:s') }}
                    </div>
                </div>
            </td>
        </tr>
    </table>

    <!-- Summary Box -->
    <div class="summary-box">
        <div class="summary-title">ملخص الجرد</div>
        <div class="summary-content">
            إجمالي المنتجات المتوفرة: <strong>{{ count($products) }}</strong> منتج
        </div>
    </div>

    <!-- Products Table -->
    @if(count($products) > 0)
        <table class="products-table">
            <thead>
                <tr>
                    <th style="width: 4%;">#</th>
                    <th style="width: 30%;">اسم المنتج</th>
                    <th style="width: 12%;">الرمز التعريفي (SKU)</th>
                    <th style="width: 14%;">الباركود</th>
                    <th style="width: 10%;">الكمية الحالية</th>
                    <th style="width: 15%;">الكمية الفعلية</th>
                    <th style="width: 15%;">التأشير</th>
                </tr>
            </thead>
            <tbody>
                @foreach($products as $index => $product)
                <tr>
                    <td><strong>{{ $index + 1 }}</strong></td>
                    <td class="product-name-cell">
                        {{ $product->name }}
                    </td>
                    <td><strong>{{ $product->sku }}</strong></td>
                    <td class="barcode-cell">
                        {{ $product->sku }}
                    </td>
                    <td class="current-quantity-cell">
                        {{ $product->current_quantity }}
                    </td>
                    <td class="actual-quantity-cell">
                        <div class="signature-line"></div>
                    </td>
                    <td class="signature-cell">
                        <div class="signature-line"></div>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    @else
        <div class="no-products">
            <h3>لا توجد منتجات متوفرة في هذا المستودع</h3>
            <p>جميع المنتجات في هذا المستودع لها كمية صفر أو غير موجودة.</p>
        </div>
    @endif

    <!-- Footer Section -->
    <div class="footer-section">
        <table class="signature-table">
            <tr>
                <td style="width: 33.33%;">
                    <div class="signature-label">اسم الموظف</div>
                    <div class="signature-line"></div>
                    <div class="signature-info">{{ $user->name }}</div>
                </td>
                <td style="width: 33.33%;">
                    <div class="signature-label">رقم الموظف</div>
                    <div class="signature-line"></div>
                    <div class="signature-info">{{ isset($employee->employee_id) ? $employee->employee_id : 'غير محدد' }}</div>
                </td>
                <td style="width: 33.33%;">
                    <div class="signature-label">التوقيع</div>
                    <div class="signature-line"></div>
                    <div class="signature-info">التاريخ: {{ $inventory_date }}</div>
                </td>
            </tr>
        </table>

        <!-- Print Instructions -->
        <div style="margin-top: 20px; text-align: center; font-size: 10px; color: #666; border-top: 1px solid #ccc; padding-top: 10px;">
            <p><strong>تعليمات:</strong> يرجى ملء الكمية الفعلية بعد العد الفعلي للمخزون ووضع التأشير في العمود المخصص</p>
            <p>تم إنشاء هذا التقرير تلقائياً في {{ now()->format('Y-m-d H:i:s') }}</p>
        </div>
    </div>
</body>
</html>
