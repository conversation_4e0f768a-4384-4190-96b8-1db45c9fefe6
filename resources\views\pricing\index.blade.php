@extends('layouts.admin')

@section('page-title')
    {{ __('التسعير') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item">{{ __('التسعير') }}</li>
@endsection

@section('action-btn')
    <div class="float-end">
        <a href="{{ route('productservice.create') }}" class="btn btn-sm btn-primary">
            <i class="ti ti-plus"></i> {{ __('إضافة منتج جديد') }}
        </a>
    </div>
@endsection

@section('content')
    <!-- فلاتر البحث -->
    <div class="row">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="{{ route('pricing.index') }}" id="filter-form">
                        <div class="row align-items-center">
                            <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 col-12">
                                <div class="btn-box">
                                    <label for="search" class="form-label">{{ __('البحث') }}</label>
                                    <input type="text" name="search" id="search" class="form-control" placeholder="{{ __('البحث بالاسم أو SKU') }}" value="{{ request('search') }}">
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 col-12">
                                <div class="btn-box">
                                    <label for="category_id" class="form-label">{{ __('الفئة') }}</label>
                                    <select name="category_id" id="category_id" class="form-control select">
                                        <option value="">{{ __('جميع الفئات') }}</option>
                                        @foreach($categories as $id => $name)
                                            <option value="{{ $id }}" {{ request('category_id') == $id ? 'selected' : '' }}>{{ $name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-xl-2 col-lg-2 col-md-6 col-sm-12 col-12">
                                <div class="btn-box">
                                    <label for="type" class="form-label">{{ __('النوع') }}</label>
                                    <select name="type" id="type" class="form-control select">
                                        <option value="">{{ __('جميع الأنواع') }}</option>
                                        @foreach($types as $value => $label)
                                            <option value="{{ $value }}" {{ request('type') == $value ? 'selected' : '' }}>{{ $label }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-xl-2 col-lg-2 col-md-6 col-sm-12 col-12">
                                <div class="btn-box">
                                    <label for="show_duplicates" class="form-label">{{ __('المكررات') }}</label>
                                    <select name="show_duplicates" id="show_duplicates" class="form-control select">
                                        <option value="">{{ __('جميع المنتجات') }}</option>
                                        <option value="1" {{ request('show_duplicates') == '1' ? 'selected' : '' }}>{{ __('SKU المكررة فقط') }}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-xl-2 col-lg-2 col-md-6 col-sm-12 col-12">
                                <div class="btn-box">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-flex">
                                        <button type="submit" class="btn btn-primary me-2">
                                            <i class="ti ti-search"></i> {{ __('بحث') }}
                                        </button>
                                        <a href="{{ route('pricing.index') }}" class="btn btn-secondary">
                                            <i class="ti ti-refresh"></i> {{ __('إعادة تعيين') }}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات المكررات -->
    @if(count($duplicateSkus) > 0)
        <div class="row">
            <div class="col-xl-12">
                <div class="duplicate-stats">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <i class="ti ti-alert-triangle text-danger me-2"></i>
                            <strong>{{ __('تحذير: تم العثور على SKU مكررة') }}</strong>
                        </div>
                        <div>
                            <span class="badge">{{ count($duplicateSkus) }} {{ __('SKU مكرر') }}</span>
                            <span class="badge">{{ $products->whereIn('sku', $duplicateSkus)->count() }} {{ __('منتج متأثر') }}</span>
                        </div>
                    </div>
                    <small class="text-muted mt-2 d-block">
                        {{ __('الصفوف المميزة باللون الأحمر تحتوي على SKU مكررة. يُنصح بتصحيح هذه المشكلة لتجنب التضارب.') }}
                    </small>
                </div>
            </div>
        </div>
    @endif

    <!-- جدول المنتجات -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('قائمة المنتجات والخدمات') }}</h5>
                    <small class="text-muted">{{ __('يمكنك النقر على أي خلية لتعديلها مباشرة') }}</small>
                </div>
                <div class="card-body table-border-style">
                    <div class="table-responsive">
                        <table class="table datatable" id="pricing-table">
                            <thead>
                                <tr>
                                    <th>{{ __('الاسم') }}</th>
                                    <th>{{ __('SKU') }}</th>
                                    <th>{{ __('سعر البيع') }}</th>
                                    <th>{{ __('سعر الشراء') }}</th>
                                    <th>{{ __('حساب الإيرادات') }}</th>
                                    <th>{{ __('حساب المصروفات') }}</th>
                                    <th>{{ __('الفئة') }}</th>
                                    <th>{{ __('الوحدة') }}</th>
                                    <th>{{ __('النوع') }}</th>
                                    <th>{{ __('الكمية') }}</th>
                                    <th>{{ __('حذف') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($products as $product)
                                    <tr data-product-id="{{ $product->id }}"
                                        class="{{ in_array($product->sku, $duplicateSkus) ? 'duplicate-sku-row' : '' }}"
                                        @if(in_array($product->sku, $duplicateSkus))
                                            data-bs-toggle="tooltip"
                                            data-bs-placement="top"
                                            title="{{ __('تحذير: SKU مكرر') }}"
                                        @endif>

                                        <!-- الاسم -->
                                        <td class="editable" data-field="name" data-type="text">
                                            {{ $product->name }}
                                        </td>

                                        <!-- SKU -->
                                        <td class="editable" data-field="sku" data-type="text">
                                            {{ $product->sku }}
                                            @if(in_array($product->sku, $duplicateSkus))
                                                <i class="ti ti-alert-triangle text-danger ms-1"
                                                   data-bs-toggle="tooltip"
                                                   data-bs-placement="top"
                                                   title="{{ __('SKU مكرر') }}"></i>
                                            @endif
                                        </td>

                                        <!-- سعر البيع -->
                                        <td class="editable" data-field="sale_price" data-type="number">
                                            {{ Auth::user()->priceFormat($product->sale_price) }}
                                        </td>

                                        <!-- سعر الشراء -->
                                        <td class="editable" data-field="purchase_price" data-type="number">
                                            {{ Auth::user()->priceFormat($product->purchase_price) }}
                                        </td>

                                        <!-- حساب الإيرادات -->
                                        <td class="editable" data-field="sale_chartaccount_id" data-type="select">
                                            @php
                                                $incomeAccount = \App\Models\ChartOfAccount::find($product->sale_chartaccount_id);
                                            @endphp
                                            {{ $incomeAccount ? $incomeAccount->name : '-' }}
                                        </td>

                                        <!-- حساب المصروفات -->
                                        <td class="editable" data-field="expense_chartaccount_id" data-type="select">
                                            @php
                                                $expenseAccount = \App\Models\ChartOfAccount::find($product->expense_chartaccount_id);
                                            @endphp
                                            {{ $expenseAccount ? $expenseAccount->name : '-' }}
                                        </td>

                                        <!-- الفئة -->
                                        <td class="editable" data-field="category_id" data-type="select">
                                            {{ $product->category ? $product->category->name : '-' }}
                                        </td>

                                        <!-- الوحدة -->
                                        <td class="editable" data-field="unit_id" data-type="select">
                                            {{ $product->unit ? $product->unit->name : '-' }}
                                        </td>

                                        <!-- النوع -->
                                        <td class="editable" data-field="type" data-type="select">
                                            {{ __($product->type) }}
                                        </td>

                                        <!-- الكمية -->
                                        <td class="editable" data-field="quantity" data-type="number">
                                            {{ number_format($product->quantity, 2) }}
                                        </td>
                                        
                                        <!-- حذف -->
                                        <td class="text-center">
                                            @can('delete product & service')
                                                <button type="button"
                                                        class="btn btn-danger btn-sm delete-product"
                                                        data-product-id="{{ $product->id }}"
                                                        data-product-name="{{ $product->name }}"
                                                        data-bs-toggle="tooltip"
                                                        title="{{ __('حذف المنتج') }}">
                                                    <i class="ti ti-trash"></i>
                                                </button>
                                            @else
                                                <span class="text-muted">{{ __('غير مصرح') }}</span>
                                            @endcan
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
<script>
// التأكد من تحميل jQuery
if (typeof jQuery === 'undefined') {
    console.error('jQuery is not loaded!');
}

$(document).ready(function() {
    console.log('Document ready, initializing pricing page...');

    // تفعيل tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // تهيئة DataTable
    try {
        var table = $('#pricing-table').DataTable({
            "pageLength": 25,
            "order": [[ 1, "asc" ]],
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json"
            },
            "columnDefs": [
                { "orderable": false, "targets": [10] } // عمود الحذف غير قابل للترتيب
            ]
        });
        console.log('DataTable initialized successfully');
    } catch (error) {
        console.error('DataTable initialization failed:', error);
    }

    // التعديل المباشر - استخدام event delegation للعمل مع DataTable
    $(document).on('click', '.editable', function(e) {
        e.preventDefault();
        e.stopPropagation();

        var $cell = $(this);
        var field = $cell.data('field');
        var type = $cell.data('type');
        var currentValue = $cell.text().trim();
        var productId = $cell.closest('tr').data('product-id');

        console.log('Clicked on editable cell:', field, type, currentValue, productId);

        if ($cell.hasClass('editing')) {
            console.log('Already editing, returning');
            return; // منع التعديل المتعدد
        }

        if (!field || !type || !productId) {
            console.log('Missing required data:', {field, type, productId});
            return;
        }

        $cell.addClass('editing');

        if (type === 'select') {
            createSelectEditor($cell, field, productId, currentValue);
        } else {
            createTextEditor($cell, field, type, productId, currentValue);
        }
    });
    
    // إنشاء محرر النص
    function createTextEditor($cell, field, type, productId, currentValue) {
        var inputType = type === 'number' ? 'number' : 'text';
        var step = type === 'number' ? '0.01' : '';
        var min = type === 'number' ? '0' : '';
        
        // إزالة تنسيق السعر للأرقام
        var editValue = currentValue;
        if (type === 'number') {
            editValue = currentValue.replace(/[^\d.-]/g, '');
        }
        
        var $input = $('<input>', {
            type: inputType,
            class: 'form-control form-control-sm',
            value: editValue,
            step: step,
            min: min
        });
        
        $cell.html($input);
        $input.focus().select();
        
        // حفظ عند الضغط على Enter أو فقدان التركيز
        $input.on('blur keypress', function(e) {
            if (e.type === 'blur' || e.which === 13) {
                saveEdit($cell, field, productId, $input.val(), currentValue);
            }
        });
        
        // إلغاء عند الضغط على Escape
        $input.on('keypress', function(e) {
            if (e.which === 27) {
                cancelEdit($cell, currentValue);
            }
        });
    }
    
    // إنشاء محرر القائمة المنسدلة
    function createSelectEditor($cell, field, productId, currentValue) {
        var $select = $('<select>', {
            class: 'form-control form-control-sm'
        });
        
        $cell.html($select);
        
        // جلب الخيارات
        $.get('{{ route("pricing.field.options") }}', { field: field })
            .done(function(response) {
                if (response.success) {
                    $.each(response.options, function(index, option) {
                        var $option = $('<option>', {
                            value: option.value,
                            text: option.text
                        });
                        
                        if (option.text === currentValue) {
                            $option.prop('selected', true);
                        }
                        
                        $select.append($option);
                    });
                    
                    $select.focus();
                    
                    // حفظ عند التغيير أو فقدان التركيز
                    $select.on('change blur', function() {
                        var selectedText = $select.find('option:selected').text();
                        saveEdit($cell, field, productId, $select.val(), currentValue, selectedText);
                    });
                } else {
                    cancelEdit($cell, currentValue);
                    showAlert('error', response.message);
                }
            })
            .fail(function() {
                cancelEdit($cell, currentValue);
                showAlert('error', 'حدث خطأ في جلب البيانات');
            });
    }
    
    // حفظ التعديل
    function saveEdit($cell, field, productId, newValue, oldValue, displayValue = null) {
        if (newValue === '' || (field !== 'tax_id' && newValue === oldValue)) {
            cancelEdit($cell, oldValue);
            return;
        }
        
        // إظهار مؤشر التحميل
        $cell.html('<i class="ti ti-loader fa-spin"></i>');
        
        $.post('{{ route("pricing.update.inline") }}', {
            _token: '{{ csrf_token() }}',
            id: productId,
            field: field,
            value: newValue
        })
        .done(function(response) {
            if (response.success) {
                $cell.html(displayValue || response.display_value);
                showAlert('success', response.message);
            } else {
                $cell.html(oldValue);
                showAlert('error', response.message);
            }
        })
        .fail(function(xhr) {
            $cell.html(oldValue);
            var message = xhr.responseJSON ? xhr.responseJSON.message : 'حدث خطأ غير متوقع';
            showAlert('error', message);
        })
        .always(function() {
            $cell.removeClass('editing');
        });
    }
    
    // إلغاء التعديل
    function cancelEdit($cell, oldValue) {
        $cell.html(oldValue).removeClass('editing');
    }
    
    // عرض التنبيهات
    function showAlert(type, message) {
        var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                       message +
                       '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                       '</div>';

        $('.card-body').first().prepend(alertHtml);

        // إزالة التنبيه تلقائياً بعد 3 ثوان
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 3000);
    }

    // وظيفة الحذف
    $(document).on('click', '.delete-product', function(e) {
        e.preventDefault();

        var $button = $(this);
        var productId = $button.data('product-id');
        var productName = $button.data('product-name');

        // تأكيد الحذف
        if (confirm('هل أنت متأكد من حذف المنتج "' + productName + '"؟\n\nتحذير: هذا الإجراء لا يمكن التراجع عنه!')) {

            // تعطيل الزر وإظهار مؤشر التحميل
            $button.prop('disabled', true).html('<i class="ti ti-loader fa-spin"></i>');

            // إرسال طلب الحذف
            $.ajax({
                url: '{{ route("productservice.destroy", ":id") }}'.replace(':id', productId),
                type: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        // إزالة الصف من الجدول
                        var table = $('#pricing-table').DataTable();
                        table.row($button.closest('tr')).remove().draw();

                        showAlert('success', response.message || 'تم حذف المنتج بنجاح');
                    } else {
                        showAlert('error', response.message || 'فشل في حذف المنتج');
                        $button.prop('disabled', false).html('<i class="ti ti-trash"></i>');
                    }
                },
                error: function(xhr) {
                    var message = 'حدث خطأ أثناء حذف المنتج';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        message = xhr.responseJSON.message;
                    }
                    showAlert('error', message);
                    $button.prop('disabled', false).html('<i class="ti ti-trash"></i>');
                }
            });
        }
    });
});
</script>

<style>
.editable {
    cursor: pointer;
    position: relative;
}

.editable:hover {
    background-color: #f8f9fa;
}

.editable.editing {
    background-color: #fff3cd;
}

.editable::after {
    content: '✏️';
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0;
    transition: opacity 0.2s;
    font-size: 12px;
}

.editable:hover::after {
    opacity: 1;
}

.editable.editing::after {
    display: none;
}

/* تصميم للخلايا غير القابلة للتعديل */
td:not(.editable) {
    background-color: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
}

/* رسالة للمستخدمين غير المصرح لهم */
.readonly-notice {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 8px 12px;
    margin-bottom: 15px;
    color: #856404;
}

/* تمييز الصفوف المكررة */
.duplicate-sku-row {
    background-color: #ffebee !important;
    border-left: 4px solid #f44336 !important;
}

.duplicate-sku-row:hover {
    background-color: #ffcdd2 !important;
}

.duplicate-sku-row td {
    border-color: #ffcdd2 !important;
}

/* تمييز خلية SKU المكررة */
.duplicate-sku-row td[data-field="sku"] {
    background-color: #ffcdd2 !important;
    font-weight: bold;
    color: #d32f2f !important;
}

/* أيقونة التحذير */
.ti-alert-triangle {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* إحصائيات المكررات */
.duplicate-stats {
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
    border: 1px solid #f44336;
    border-radius: 8px;
    padding: 10px 15px;
    margin-bottom: 15px;
}

.duplicate-stats .badge {
    background-color: #f44336 !important;
    color: white !important;
    font-size: 12px;
    padding: 4px 8px;
}

/* تصميم زر الحذف */
.delete-product {
    transition: all 0.3s ease;
    border-radius: 6px;
    padding: 6px 10px;
}

.delete-product:hover {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.delete-product:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* تحسين عمود الحذف */
td:has(.delete-product) {
    background-color: #fff !important;
    vertical-align: middle;
}

/* تأثير hover للصف عند التمرير على زر الحذف */
.delete-product:hover {
    animation: deleteWarning 1s ease-in-out infinite alternate;
}

@keyframes deleteWarning {
    0% { background-color: #dc3545; }
    100% { background-color: #c82333; }
}
</style>
@endpush
