<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>{{ $receiptOrder->order_number }} - فاتورة {{ $receiptOrder->order_type }}</title>
    
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 14px;
            line-height: 1.5;
            color: #333;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #1e3a8a;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .company-logo {
            max-height: 80px;
            margin-bottom: 10px;
        }
        
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #1e3a8a;
            margin-bottom: 5px;
        }
        
        .company-details {
            font-size: 12px;
            color: #666;
        }
        
        .invoice-title {
            background: #1e3a8a;
            color: white;
            padding: 15px;
            text-align: center;
            margin: 20px 0;
            font-size: 20px;
            font-weight: bold;
        }
        
        .info-section {
            display: table;
            width: 100%;
            margin-bottom: 30px;
        }
        
        .info-group {
            display: table-cell;
            width: 33.33%;
            vertical-align: top;
            padding: 0 10px;
        }
        
        .info-title {
            font-weight: bold;
            color: #1e3a8a;
            margin-bottom: 10px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        
        .info-item {
            margin-bottom: 5px;
            font-size: 13px;
        }
        
        .info-label {
            font-weight: bold;
            display: inline-block;
            width: 80px;
        }
        
        .products-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            border: 2px solid #1e3a8a;
        }
        
        .products-table th {
            background: #1e3a8a;
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #1e3a8a;
        }
        
        .products-table td {
            padding: 10px 8px;
            border: 1px solid #ddd;
            text-align: center;
            font-size: 12px;
        }
        
        .products-table tbody tr:nth-child(even) {
            background: #f9f9f9;
        }
        
        .product-name {
            text-align: right;
            font-weight: 500;
        }
        
        .total-row {
            background: #1e3a8a !important;
            color: white !important;
            font-weight: bold;
        }
        
        .summary {
            background: #f0f9ff;
            padding: 15px;
            border: 1px solid #3b82f6;
            margin-bottom: 20px;
        }
        
        .summary-title {
            font-weight: bold;
            color: #1e3a8a;
            margin-bottom: 10px;
        }
        
        .summary-item {
            margin-bottom: 5px;
            display: flex;
            justify-content: space-between;
        }
        
        .notes {
            background: #fefce8;
            border: 1px solid #facc15;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .notes-title {
            font-weight: bold;
            color: #a16207;
            margin-bottom: 10px;
        }
        
        .signatures {
            display: table;
            width: 100%;
            margin-top: 50px;
            border-top: 1px solid #ddd;
            padding-top: 30px;
        }
        
        .signature {
            display: table-cell;
            width: 33.33%;
            text-align: center;
            padding: 0 20px;
        }
        
        .signature-line {
            border-top: 2px solid #333;
            margin-top: 50px;
            padding-top: 5px;
            font-weight: bold;
        }
        
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 15px;
            border-top: 1px solid #ddd;
            font-size: 11px;
            color: #666;
        }
        
        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            color: white;
        }
        
        .badge-success { background: #059669; }
        .badge-info { background: #0ea5e9; }
        .badge-warning { background: #d97706; }
        .badge-danger { background: #dc2626; }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- رأس الفاتورة -->
        <div class="header">
            @if($companyData['logo'])
                <img src="{{ $companyData['logo'] }}" alt="شعار الشركة" class="company-logo">
            @endif
            <div class="company-name">{{ $companyData['name'] }}</div>
            <div class="company-details">
                {{ $companyData['address'] }}<br>
                هاتف: {{ $companyData['phone'] }} | بريد: {{ $companyData['email'] }}
            </div>
        </div>

        <!-- عنوان الفاتورة -->
        <div class="invoice-title">
            @if($receiptOrder->order_type === 'استلام بضاعة')
                فاتورة استلام بضاعة
            @elseif($receiptOrder->order_type === 'نقل بضاعة')
                أمر نقل بضاعة
            @else
                أمر إخراج بضاعة
            @endif
            - رقم الأمر: {{ $receiptOrder->order_number }}
        </div>

        <!-- معلومات الفاتورة -->
        <div class="info-section">
            <div class="info-group">
                <div class="info-title">معلومات الأمر</div>
                <div class="info-item">
                    <span class="info-label">النوع:</span>
                    @if($receiptOrder->order_type === 'استلام بضاعة')
                        <span class="badge badge-success">{{ $receiptOrder->order_type }}</span>
                    @elseif($receiptOrder->order_type === 'نقل بضاعة')
                        <span class="badge badge-info">{{ $receiptOrder->order_type }}</span>
                    @else
                        <span class="badge badge-warning">{{ $receiptOrder->order_type }}</span>
                    @endif
                </div>
                @if($receiptOrder->vendor)
                    <div class="info-item">
                        <span class="info-label">المورد:</span> {{ $receiptOrder->vendor->name }}
                    </div>
                @endif
                <div class="info-item">
                    <span class="info-label">المستودع:</span> {{ $receiptOrder->warehouse->name ?? 'غير محدد' }}
                </div>
                @if($receiptOrder->fromWarehouse)
                    <div class="info-item">
                        <span class="info-label">من مستودع:</span> {{ $receiptOrder->fromWarehouse->name }}
                    </div>
                @endif
            </div>
            
            <div class="info-group">
                <div class="info-title">التواريخ</div>
                <div class="info-item">
                    <span class="info-label">التاريخ:</span> {{ \App\Models\Utility::getDateFormated($receiptOrder->invoice_date ?: $receiptOrder->created_at) }}
                </div>
                <div class="info-item">
                    <span class="info-label">الوقت:</span> {{ $receiptOrder->created_at->format('H:i') }}
                </div>
                @if($receiptOrder->exit_date)
                    <div class="info-item">
                        <span class="info-label">تاريخ الإخراج:</span> {{ \App\Models\Utility::getDateFormated($receiptOrder->exit_date) }}
                    </div>
                @endif
                @if($receiptOrder->invoice_number)
                    <div class="info-item">
                        <span class="info-label">رقم الفاتورة:</span> {{ $receiptOrder->invoice_number }}
                    </div>
                @endif
            </div>
            
            <div class="info-group">
                <div class="info-title">معلومات إضافية</div>
                @if($receiptOrder->exit_reason)
                    <div class="info-item">
                        <span class="info-label">سبب الإخراج:</span> {{ $receiptOrder->exit_reason }}
                    </div>
                @endif
                @if($receiptOrder->responsible_person)
                    <div class="info-item">
                        <span class="info-label">المسؤول:</span> {{ $receiptOrder->responsible_person }}
                    </div>
                @endif
                <div class="info-item">
                    <span class="info-label">المنشئ:</span> {{ isset($creator) && $creator ? $creator->name : 'غير محدد' }}
                </div>
            </div>
        </div>

        <!-- جدول المنتجات -->
        <table class="products-table">
            <thead>
                <tr>
                    <th width="5%">#</th>
                    <th width="15%">رمز المنتج</th>
                    <th width="25%">اسم المنتج</th>
                    <th width="10%">الكمية</th>
                    @if($receiptOrder->order_type === 'استلام بضاعة')
                        <th width="12%">سعر الوحدة</th>
                        <th width="12%">الإجمالي</th>
                    @endif
                    @if($receiptOrder->products->where('expiry_date', '!=', null)->count() > 0)
                        <th width="12%">تاريخ الصلاحية</th>
                    @endif
                    <th width="21%">ملاحظات</th>
                </tr>
            </thead>
            <tbody>
                @php $totalAmount = 0; @endphp
                @foreach($receiptOrder->products as $index => $item)
                    <tr>
                        <td>{{ $index + 1 }}</td>
                        <td>{{ $item->product->sku ?? 'غير محدد' }}</td>
                        <td class="product-name">{{ $item->product->name ?? 'غير محدد' }}</td>
                        <td>{{ number_format($item->quantity, 2) }}</td>
                        @if($receiptOrder->order_type === 'استلام بضاعة')
                            <td>{{ number_format($item->unit_cost, 2) }}</td>
                            <td>{{ number_format($item->total_cost, 2) }}</td>
                            @php $totalAmount += $item->total_cost; @endphp
                        @endif
                        @if($receiptOrder->products->where('expiry_date', '!=', null)->count() > 0)
                            <td>
                                @if($item->expiry_date)
                                    {{ \App\Models\Utility::getDateFormated($item->expiry_date) }}
                                @else
                                    -
                                @endif
                            </td>
                        @endif
                        <td>
                            @if($item->is_return)
                                <span class="badge badge-danger">مرتجع</span><br>
                            @endif
                            {{ $item->notes }}
                        </td>
                    </tr>
                @endforeach
                
                @if($receiptOrder->order_type === 'استلام بضاعة' && $totalAmount > 0)
                    <tr class="total-row">
                        <td colspan="{{ $receiptOrder->products->where('expiry_date', '!=', null)->count() > 0 ? '5' : '4' }}">
                            <strong>الإجمالي النهائي</strong>
                        </td>
                        <td><strong>{{ number_format($totalAmount, 2) }} ريال</strong></td>
                        <td></td>
                    </tr>
                @endif
            </tbody>
        </table>

        <!-- ملخص الأمر -->
        <div class="summary">
            <div class="summary-title">ملخص الأمر</div>
            <div class="summary-item">
                <span>إجمالي المنتجات:</span>
                <span>{{ $receiptOrder->total_products }} منتج</span>
            </div>
            @if($receiptOrder->order_type === 'استلام بضاعة')
                <div class="summary-item">
                    <span>إجمالي المبلغ:</span>
                    <span>{{ number_format($receiptOrder->total_amount, 2) }} ريال</span>
                </div>
            @endif
            <div class="summary-item">
                <span>الحالة:</span>
                <span class="badge badge-{{ $receiptOrder->status_color }}">{{ $receiptOrder->status }}</span>
            </div>
        </div>

        <!-- الملاحظات -->
        @if($receiptOrder->notes)
            <div class="notes">
                <div class="notes-title">ملاحظات:</div>
                <div>{{ $receiptOrder->notes }}</div>
            </div>
        @endif

        <!-- التوقيعات -->
        <div class="signatures">
            <div class="signature">
                <div class="signature-line">توقيع المستلم</div>
            </div>
            <div class="signature">
                <div class="signature-line">توقيع المسؤول</div>
            </div>
            <div class="signature">
                <div class="signature-line">ختم الشركة</div>
            </div>
        </div>

        <!-- الفوتر -->
        <div class="footer">
            <div><strong>تاريخ الطباعة:</strong> {{ now()->format('Y-m-d H:i') }} | <strong>طُبع بواسطة:</strong> {{ Auth::check() ? Auth::user()->name : 'النظام' }}</div>
            <div>تم إنشاء هذه الفاتورة بواسطة نظام إدارة المستودعات - {{ $companyData['name'] }}</div>
        </div>
    </div>
</body>
</html>
