<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVendorRepresentativesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('vendor_representatives', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name')->comment('اسم المندوب');
            $table->string('phone', 20)->comment('رقم الهاتف');
            $table->unsignedBigInteger('vendor_id')->comment('معرف الشركة الموردة');
            $table->unsignedBigInteger('category_id')->comment('معرف فئة المنتجات');
            $table->boolean('is_active')->default(1)->comment('الحالة (نشط/غير نشط)');
            $table->text('notes')->nullable()->comment('ملاحظات إضافية');
            $table->integer('created_by')->default(0)->comment('منشئ السجل');
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('vendor_id')->references('id')->on('venders')->onDelete('cascade');
            $table->foreign('category_id')->references('id')->on('product_service_categories')->onDelete('cascade');

            // Indexes for better performance
            $table->index('vendor_id');
            $table->index('category_id');
            $table->index('is_active');
            $table->index('created_by');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('vendor_representatives');
    }
}
