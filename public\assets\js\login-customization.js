/**
 * Login Page Customization JavaScript
 * Enhanced functionality for image preview and validation
 */

$(document).ready(function() {
    
    // Initialize login customization functionality
    initLoginCustomization();
    
    function initLoginCustomization() {
        // Form validation
        setupFormValidation();
        
        // Image preview functionality
        setupImagePreview();
        
        // File validation
        setupFileValidation();
        
        // Color picker enhancements
        setupColorPickers();
        
        // Drag and drop functionality
        setupDragAndDrop();
    }
    
    function setupFormValidation() {
        $('#login-customization form').on('submit', function(e) {
            var hasImages = false;
            var hasColors = $('#login_primary_color').val() && $('#login_background_color').val();

            // Check if at least one background image is uploaded or exists
            $('#login-customization input[type="file"]').each(function() {
                if (this.files && this.files.length > 0) {
                    hasImages = true;
                    return false;
                }
            });
            
            // Check if images already exist
            if (!hasImages) {
                $('#login-customization .logo-content img').each(function() {
                    if ($(this).attr('src')) {
                        hasImages = true;
                        return false;
                    }
                });
            }

            if ($('#enable_login_customization').val() === 'on' && !hasImages && !hasColors) {
                e.preventDefault();
                showAlert('warning', 'Please upload at least one background image or set custom colors when enabling login customization.');
                return false;
            }
        });
    }
    
    function setupImagePreview() {
        // Background images
        $('#login_bg_image_1').change(function() {
            previewImage(this, '#login-customization .logo-content:eq(0)');
        });

        $('#login_bg_image_2').change(function() {
            previewImage(this, '#login-customization .logo-content:eq(1)');
        });

        $('#login_bg_image_3').change(function() {
            previewImage(this, '#login-customization .logo-content:eq(2)');
        });

        $('#login_custom_logo').change(function() {
            previewImage(this, '#login-customization .logo-content:eq(3)');
        });

        $('#login_favicon').change(function() {
            previewImage(this, '#login-customization .logo-content:eq(4)', true);
        });
    }
    
    function previewImage(input, previewContainer, isIcon = false) {
        if (input.files && input.files[0]) {
            // Show loading state
            $(previewContainer).addClass('uploading');
            
            var reader = new FileReader();
            reader.onload = function(e) {
                // Remove loading state
                $(previewContainer).removeClass('uploading');
                
                var imageStyle = isIcon ? 
                    'width="64px" class="img-fluid" style="filter: drop-shadow(2px 3px 7px #011c4b);"' :
                    'class="img-fluid rounded" style="max-height: 150px; filter: drop-shadow(2px 3px 7px #011c4b);"';
                    
                var imageHtml = '<a href="' + e.target.result + '" target="_blank">' +
                               '<img src="' + e.target.result + '" ' + imageStyle + '>' +
                               '</a>';
                $(previewContainer).html(imageHtml);
                
                // Add success animation
                $(previewContainer).find('img').hide().fadeIn(500);
                
                // Show success message
                showAlert('success', 'Image preview loaded successfully!');
            }
            
            reader.onerror = function() {
                $(previewContainer).removeClass('uploading');
                showAlert('error', 'Error reading file. Please try again.');
            }
            
            reader.readAsDataURL(input.files[0]);
        }
    }
    
    function setupFileValidation() {
        $('input[type="file"]').change(function() {
            var file = this.files[0];
            if (file) {
                // File size validation
                var fileSize = file.size / 1024 / 1024; // Convert to MB
                var maxSize = 20; // 20MB
                
                if (fileSize > maxSize) {
                    showAlert('error', 'File size must be less than 20MB');
                    $(this).val('');
                    return false;
                }
                
                // File type validation
                var allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'];
                if (this.id === 'login_favicon') {
                    allowedTypes.push('image/x-icon', 'image/vnd.microsoft.icon');
                }
                
                if (allowedTypes.indexOf(file.type) === -1) {
                    showAlert('error', 'Please select a valid image file');
                    $(this).val('');
                    return false;
                }
                
                // Additional validation for image dimensions (optional)
                validateImageDimensions(file, this.id);
            }
        });
    }
    
    function validateImageDimensions(file, fieldId) {
        var img = new Image();
        img.onload = function() {
            var width = this.width;
            var height = this.height;
            
            // Recommended dimensions
            var recommendations = {
                'login_bg_image_1': { min: 1920, max: 4096, type: 'background' },
                'login_bg_image_2': { min: 1920, max: 4096, type: 'background' },
                'login_bg_image_3': { min: 1920, max: 4096, type: 'background' },
                'login_custom_logo': { min: 200, max: 800, type: 'logo' },
                'login_favicon': { min: 16, max: 512, type: 'icon' }
            };
            
            if (recommendations[fieldId]) {
                var rec = recommendations[fieldId];
                if (width < rec.min || height < rec.min) {
                    showAlert('warning', `Image resolution is quite low. Recommended minimum: ${rec.min}px for ${rec.type} images.`);
                } else if (width > rec.max || height > rec.max) {
                    showAlert('info', `Image resolution is very high. Consider optimizing for better performance.`);
                }
            }
        };
        img.src = URL.createObjectURL(file);
    }
    
    function setupColorPickers() {
        // Real-time color preview
        $('#login_primary_color, #login_background_color').on('input change', function() {
            var colorName = $(this).attr('name');
            var colorValue = $(this).val();
            
            // Update CSS variables for live preview
            if (colorName === 'login_primary_color') {
                $(':root').css('--login-primary-color', colorValue);
                $(this).css('border-color', colorValue);
            } else if (colorName === 'login_background_color') {
                $(':root').css('--login-background-color', colorValue);
                $(this).css('background-color', colorValue);
            }
        });
    }
    
    function setupDragAndDrop() {
        // Add drag and drop functionality to file inputs
        $('.choose-files').each(function() {
            var $this = $(this);
            var $input = $this.find('input[type="file"]');
            var $label = $this.find('label');
            
            $label.on('dragover dragenter', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $this.addClass('drag-over');
            });
            
            $label.on('dragleave dragend', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $this.removeClass('drag-over');
            });
            
            $label.on('drop', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $this.removeClass('drag-over');
                
                var files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    $input[0].files = files;
                    $input.trigger('change');
                }
            });
        });
    }
    
    function showAlert(type, message) {
        // Create alert element
        var alertClass = 'alert-' + (type === 'error' ? 'danger' : type);
        var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                       '<strong>' + capitalizeFirst(type) + '!</strong> ' + message +
                       '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                       '</div>';
        
        // Remove existing alerts
        $('#login-customization .alert').remove();
        
        // Add new alert
        $('#login-customization .card-body').prepend(alertHtml);
        
        // Auto-hide after 5 seconds
        setTimeout(function() {
            $('#login-customization .alert').fadeOut();
        }, 5000);
    }
    
    function capitalizeFirst(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    }
});

// CSS for drag and drop styling
var dragDropCSS = `
.choose-files.drag-over .company_logo_update {
    background: linear-gradient(45deg, #28a745, #20c997) !important;
    transform: scale(1.02);
    border: 2px dashed #fff !important;
}

.choose-files .company_logo_update {
    transition: all 0.3s ease;
}
`;

// Inject CSS
$('<style>').text(dragDropCSS).appendTo('head');
