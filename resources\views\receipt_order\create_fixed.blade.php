@extends('layouts.admin')

@section('page-title')
    {{ __('إنشاء أمر استلام جديد') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('الرئيسية') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ route('receipt-order.index') }}">{{ __('أوامر الاستلام') }}</a></li>
    <li class="breadcrumb-item">{{ __('إنشاء أمر جديد') }}</li>
@endsection

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="mb-0">{{ __('إنشاء أمر استلام جديد') }}</h5>
                        </div>
                        <div class="col-6 text-end">
                            <a href="{{ route('receipt-order.index') }}" class="btn btn-secondary">
                                <i class="ti ti-arrow-left"></i> {{ __('العودة') }}
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif

                    <form action="{{ route('receipt-order.store') }}" method="POST" id="receiptOrderForm">
                        @csrf
                        
                        <div class="row">
                            <!-- نوع الأمر -->
                            <div class="col-md-6 mb-3">
                                <label for="order_type" class="form-label">{{ __('نوع الأمر') }} <span class="text-danger">*</span></label>
                                <select name="order_type" id="order_type" class="form-select" required>
                                    <option value="">{{ __('اختر نوع الأمر') }}</option>
                                    <option value="استلام بضاعة" {{ old('order_type') == 'استلام بضاعة' ? 'selected' : '' }}>
                                        {{ __('استلام بضاعة') }}
                                    </option>
                                    <option value="نقل بضاعة" {{ old('order_type') == 'نقل بضاعة' ? 'selected' : '' }}>
                                        {{ __('نقل بضاعة') }}
                                    </option>
                                    <option value="أمر إخراج" {{ old('order_type') == 'أمر إخراج' ? 'selected' : '' }}>
                                        {{ __('أمر إخراج') }}
                                    </option>
                                </select>
                            </div>

                            <!-- المستودع الهدف -->
                            <div class="col-md-6 mb-3">
                                <label for="warehouse_id" class="form-label">{{ __('المستودع الهدف') }} <span class="text-danger">*</span></label>
                                <select name="warehouse_id" id="warehouse_id" class="form-select" required>
                                    <option value="">{{ __('اختر المستودع') }}</option>
                                    @foreach($warehouses as $warehouse)
                                        <option value="{{ $warehouse->id }}" {{ old('warehouse_id') == $warehouse->id ? 'selected' : '' }}>
                                            {{ $warehouse->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- المورد -->
                            <div class="col-md-6 mb-3">
                                <label for="vendor_id" class="form-label">{{ __('المورد') }}</label>
                                <select name="vendor_id" id="vendor_id" class="form-select">
                                    <option value="">{{ __('اختر المورد (اختياري)') }}</option>
                                    @foreach($vendors as $vendor)
                                        <option value="{{ $vendor->id }}" {{ old('vendor_id') == $vendor->id ? 'selected' : '' }}>
                                            {{ $vendor->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- المستودع المصدر (للنقل) -->
                            <div class="col-md-6 mb-3" id="from_warehouse_div" style="display: none;">
                                <label for="from_warehouse_id" class="form-label">{{ __('من مستودع') }}</label>
                                <select name="from_warehouse_id" id="from_warehouse_id" class="form-select">
                                    <option value="">{{ __('اختر المستودع المصدر') }}</option>
                                    @foreach($warehouses as $warehouse)
                                        <option value="{{ $warehouse->id }}" {{ old('from_warehouse_id') == $warehouse->id ? 'selected' : '' }}>
                                            {{ $warehouse->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- رقم الفاتورة -->
                            <div class="col-md-6 mb-3">
                                <label for="invoice_number" class="form-label">{{ __('رقم الفاتورة') }}</label>
                                <input type="text" name="invoice_number" id="invoice_number" class="form-control" 
                                       value="{{ old('invoice_number') }}" placeholder="{{ __('رقم الفاتورة') }}">
                            </div>

                            <!-- مبلغ الفاتورة -->
                            <div class="col-md-6 mb-3">
                                <label for="invoice_total" class="form-label">{{ __('مبلغ الفاتورة') }}</label>
                                <input type="number" name="invoice_total" id="invoice_total" class="form-control" 
                                       value="{{ old('invoice_total') }}" step="0.01" min="0" placeholder="0.00">
                            </div>

                            <!-- تاريخ الفاتورة -->
                            <div class="col-md-6 mb-3">
                                <label for="invoice_date" class="form-label">{{ __('تاريخ الفاتورة') }}</label>
                                <input type="date" name="invoice_date" id="invoice_date" class="form-control" 
                                       value="{{ old('invoice_date', date('Y-m-d')) }}">
                            </div>

                            <!-- سبب الإخراج (لأوامر الإخراج فقط) -->
                            <div class="col-md-6 mb-3" id="exit_reason_div" style="display: none;">
                                <label for="exit_reason" class="form-label">{{ __('سبب الإخراج') }}</label>
                                <select name="exit_reason" id="exit_reason" class="form-select">
                                    <option value="">{{ __('اختر سبب الإخراج') }}</option>
                                    <option value="فقدان" {{ old('exit_reason') == 'فقدان' ? 'selected' : '' }}>{{ __('فقدان') }}</option>
                                    <option value="منتهي الصلاحية" {{ old('exit_reason') == 'منتهي الصلاحية' ? 'selected' : '' }}>{{ __('منتهي الصلاحية') }}</option>
                                    <option value="تلف/خراب" {{ old('exit_reason') == 'تلف/خراب' ? 'selected' : '' }}>{{ __('تلف/خراب') }}</option>
                                    <option value="بيع بالتجزئة" {{ old('exit_reason') == 'بيع بالتجزئة' ? 'selected' : '' }}>{{ __('بيع بالتجزئة') }}</option>
                                </select>
                            </div>

                            <!-- الشخص المسؤول -->
                            <div class="col-md-6 mb-3">
                                <label for="responsible_person" class="form-label">{{ __('الشخص المسؤول') }}</label>
                                <input type="text" name="responsible_person" id="responsible_person" class="form-control" 
                                       value="{{ old('responsible_person') }}" placeholder="{{ __('اسم الشخص المسؤول') }}">
                            </div>

                            <!-- الملاحظات -->
                            <div class="col-12 mb-3">
                                <label for="notes" class="form-label">{{ __('الملاحظات') }}</label>
                                <textarea name="notes" id="notes" class="form-control" rows="3" 
                                          placeholder="{{ __('أدخل أي ملاحظات إضافية') }}">{{ old('notes') }}</textarea>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('receipt-order.index') }}" class="btn btn-secondary">
                                        <i class="ti ti-arrow-left"></i> {{ __('إلغاء') }}
                                    </a>
                                    <button type="submit" class="btn btn-primary" id="submitBtn">
                                        <i class="ti ti-device-floppy"></i> {{ __('حفظ أمر الاستلام') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
<script>
$(document).ready(function() {
    // إظهار/إخفاء حقول حسب نوع الأمر
    $('#order_type').change(function() {
        var orderType = $(this).val();
        
        if (orderType === 'نقل بضاعة') {
            $('#from_warehouse_div').show();
            $('#from_warehouse_id').attr('required', true);
        } else {
            $('#from_warehouse_div').hide();
            $('#from_warehouse_id').attr('required', false);
        }
        
        if (orderType === 'أمر إخراج') {
            $('#exit_reason_div').show();
        } else {
            $('#exit_reason_div').hide();
        }
    });

    // تشغيل التغيير عند تحميل الصفحة
    $('#order_type').trigger('change');

    // منع الإرسال المتكرر
    $('#receiptOrderForm').submit(function() {
        $('#submitBtn').prop('disabled', true).html('<i class="ti ti-loader"></i> جاري الحفظ...');
    });
});
</script>
@endpush
