using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace POS_Desktop_App.Models.DatabaseModels
{
    /// <summary>
    /// نموذج العميل - يطابق جدول customers في قاعدة البيانات
    /// </summary>
    [Table("customers")]
    public class Customer
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("customer_id")]
        public int CustomerId { get; set; }

        [Column("name")]
        [StringLength(255)]
        public string Name { get; set; }

        [Column("email")]
        [StringLength(255)]
        public string Email { get; set; }

        [Column("tax_number")]
        [StringLength(255)]
        public string TaxNumber { get; set; }

        [Column("contact")]
        [StringLength(255)]
        public string Contact { get; set; }

        [Column("avatar")]
        [StringLength(100)]
        public string Avatar { get; set; } = "";

        [Column("created_by")]
        public int CreatedBy { get; set; } = 0;

        [Column("is_active")]
        public int IsActive { get; set; } = 1;

        [Column("email_verified_at")]
        public DateTime? EmailVerifiedAt { get; set; }

        [Column("billing_name")]
        [StringLength(255)]
        public string BillingName { get; set; }

        [Column("billing_country")]
        [StringLength(255)]
        public string BillingCountry { get; set; }

        [Column("billing_state")]
        [StringLength(255)]
        public string BillingState { get; set; }

        [Column("billing_city")]
        [StringLength(255)]
        public string BillingCity { get; set; }

        [Column("billing_phone")]
        [StringLength(255)]
        public string BillingPhone { get; set; }

        [Column("billing_zip")]
        [StringLength(255)]
        public string BillingZip { get; set; }

        [Column("billing_address")]
        public string BillingAddress { get; set; }

        [Column("shipping_name")]
        [StringLength(255)]
        public string ShippingName { get; set; }

        [Column("shipping_country")]
        [StringLength(255)]
        public string ShippingCountry { get; set; }

        [Column("shipping_state")]
        [StringLength(255)]
        public string ShippingState { get; set; }

        [Column("shipping_city")]
        [StringLength(255)]
        public string ShippingCity { get; set; }

        [Column("shipping_phone")]
        [StringLength(255)]
        public string ShippingPhone { get; set; }

        [Column("shipping_zip")]
        [StringLength(255)]
        public string ShippingZip { get; set; }

        [Column("shipping_address")]
        public string ShippingAddress { get; set; }

        [Column("lang")]
        [StringLength(10)]
        public string Lang { get; set; } = "ar";

        [Column("balance")]
        [Precision(15, 2)]
        public decimal Balance { get; set; } = 0.00m;

        [Column("warehouse_id")]
        public long? WarehouseId { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; }

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; }

        // Navigation Properties
        public virtual Warehouse Warehouse { get; set; }
        public virtual ICollection<PosTransaction> PosTransactions { get; set; } = new List<PosTransaction>();

        // Calculated Properties
        [NotMapped]
        public bool IsActiveCustomer => IsActive == 1;

        [NotMapped]
        public string DisplayName => string.IsNullOrWhiteSpace(Name) ? $"عميل #{CustomerId}" : Name;

        [NotMapped]
        public string FullContact
        {
            get
            {
                var contacts = new List<string>();
                if (!string.IsNullOrWhiteSpace(Contact)) contacts.Add(Contact);
                if (!string.IsNullOrWhiteSpace(Email)) contacts.Add(Email);
                return string.Join(" | ", contacts);
            }
        }

        [NotMapped]
        public string AvatarUrl
        {
            get
            {
                if (string.IsNullOrEmpty(Avatar))
                    return "/Images/default-avatar.png";
                
                return $"/uploads/avatars/{Avatar}";
            }
        }

        [NotMapped]
        public bool HasAvatar => !string.IsNullOrEmpty(Avatar);

        [NotMapped]
        public string FullBillingAddress
        {
            get
            {
                var addressParts = new List<string>();
                
                if (!string.IsNullOrWhiteSpace(BillingAddress)) addressParts.Add(BillingAddress);
                if (!string.IsNullOrWhiteSpace(BillingCity)) addressParts.Add(BillingCity);
                if (!string.IsNullOrWhiteSpace(BillingState)) addressParts.Add(BillingState);
                if (!string.IsNullOrWhiteSpace(BillingCountry)) addressParts.Add(BillingCountry);
                if (!string.IsNullOrWhiteSpace(BillingZip)) addressParts.Add(BillingZip);

                return string.Join(", ", addressParts);
            }
        }

        [NotMapped]
        public string FullShippingAddress
        {
            get
            {
                var addressParts = new List<string>();
                
                if (!string.IsNullOrWhiteSpace(ShippingAddress)) addressParts.Add(ShippingAddress);
                if (!string.IsNullOrWhiteSpace(ShippingCity)) addressParts.Add(ShippingCity);
                if (!string.IsNullOrWhiteSpace(ShippingState)) addressParts.Add(ShippingState);
                if (!string.IsNullOrWhiteSpace(ShippingCountry)) addressParts.Add(ShippingCountry);
                if (!string.IsNullOrWhiteSpace(ShippingZip)) addressParts.Add(ShippingZip);

                return string.Join(", ", addressParts);
            }
        }

        [NotMapped]
        public bool HasBillingAddress => !string.IsNullOrWhiteSpace(BillingAddress);

        [NotMapped]
        public bool HasShippingAddress => !string.IsNullOrWhiteSpace(ShippingAddress);

        [NotMapped]
        public bool HasDebt => Balance < 0;

        [NotMapped]
        public bool HasCredit => Balance > 0;

        [NotMapped]
        public decimal DebtAmount => Balance < 0 ? Math.Abs(Balance) : 0;

        [NotMapped]
        public decimal CreditAmount => Balance > 0 ? Balance : 0;

        // Helper Methods
        public void Activate()
        {
            IsActive = 1;
        }

        public void Deactivate()
        {
            IsActive = 0;
        }

        public void AddToBalance(decimal amount)
        {
            Balance += amount;
        }

        public void SubtractFromBalance(decimal amount)
        {
            Balance -= amount;
        }

        public void SetBalance(decimal amount)
        {
            Balance = amount;
        }

        public void ClearBalance()
        {
            Balance = 0;
        }

        public bool CanPurchase(decimal amount)
        {
            // Check if customer can make a purchase based on their balance
            // This could include credit limit checks
            return true; // For now, allow all purchases
        }

        public decimal GetTotalPurchases()
        {
            return PosTransactions?.Where(t => t.IsCompleted()).Sum(t => t.GrandTotal) ?? 0;
        }

        public int GetTotalTransactions()
        {
            return PosTransactions?.Count(t => t.IsCompleted()) ?? 0;
        }

        public DateTime? GetLastPurchaseDate()
        {
            return PosTransactions?.Where(t => t.IsCompleted())
                                  .OrderByDescending(t => t.PosDate)
                                  .FirstOrDefault()?.PosDate;
        }

        public void CopyBillingToShipping()
        {
            ShippingName = BillingName;
            ShippingCountry = BillingCountry;
            ShippingState = BillingState;
            ShippingCity = BillingCity;
            ShippingPhone = BillingPhone;
            ShippingZip = BillingZip;
            ShippingAddress = BillingAddress;
        }

        public void CopyShippingToBilling()
        {
            BillingName = ShippingName;
            BillingCountry = ShippingCountry;
            BillingState = ShippingState;
            BillingCity = ShippingCity;
            BillingPhone = ShippingPhone;
            BillingZip = ShippingZip;
            BillingAddress = ShippingAddress;
        }

        // Validation
        public List<string> Validate()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(Name))
                errors.Add("اسم العميل مطلوب");

            if (!string.IsNullOrWhiteSpace(Email) && !IsValidEmail(Email))
                errors.Add("البريد الإلكتروني غير صحيح");

            if (!string.IsNullOrWhiteSpace(Contact) && !IsValidPhone(Contact))
                errors.Add("رقم الهاتف غير صحيح");

            return errors;
        }

        public bool IsValid()
        {
            return !Validate().Any();
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private bool IsValidPhone(string phone)
        {
            // Simple phone validation - can be enhanced
            return phone.All(c => char.IsDigit(c) || c == '+' || c == '-' || c == ' ' || c == '(' || c == ')');
        }

        // Search Methods
        public bool MatchesSearch(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return true;

            searchTerm = searchTerm.ToLower();

            return (Name?.ToLower().Contains(searchTerm) == true) ||
                   (Email?.ToLower().Contains(searchTerm) == true) ||
                   (Contact?.ToLower().Contains(searchTerm) == true) ||
                   (TaxNumber?.ToLower().Contains(searchTerm) == true) ||
                   CustomerId.ToString().Contains(searchTerm);
        }

        // Display Methods
        public string GetFormattedBalance()
        {
            return Balance.ToString("C2");
        }

        public string GetBalanceStatusText()
        {
            if (Balance == 0)
                return "متوازن";
            else if (Balance > 0)
                return $"رصيد دائن: {CreditAmount:C2}";
            else
                return $"رصيد مدين: {DebtAmount:C2}";
        }

        public string GetStatusText()
        {
            return IsActiveCustomer ? "نشط" : "غير نشط";
        }

        // Override Methods
        public override string ToString()
        {
            return DisplayName;
        }

        public override bool Equals(object obj)
        {
            if (obj is Customer other)
            {
                return this.Id == other.Id;
            }
            return false;
        }

        public override int GetHashCode()
        {
            return Id.GetHashCode();
        }

        // Static Methods
        public static Customer CreateWalkInCustomer()
        {
            return new Customer
            {
                CustomerId = 0,
                Name = "عميل عادي",
                IsActive = 1,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };
        }
    }
}
