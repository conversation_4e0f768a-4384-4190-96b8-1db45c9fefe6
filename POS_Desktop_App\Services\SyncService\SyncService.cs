using Microsoft.EntityFrameworkCore;
using POS_Desktop_App.Models.LocalCache;
using POS_Desktop_App.Services.CacheService;
using POS_Desktop_App.Services.DatabaseService;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace POS_Desktop_App.Services.SyncService
{
    /// <summary>
    /// خدمة المزامنة بين التخزين المحلي وقاعدة البيانات على السيرفر
    /// </summary>
    public class SyncService : IDisposable
    {
        private readonly LocalCacheDbContext _localDb;
        private readonly PosDbContext _serverDb;
        private readonly HttpClient _httpClient;
        private readonly Timer _syncTimer;
        private readonly SemaphoreSlim _syncSemaphore;
        private bool _isDisposed = false;
        private bool _isSyncing = false;

        public event EventHandler<SyncProgressEventArgs> SyncProgress;
        public event EventHandler<SyncCompletedEventArgs> SyncCompleted;
        public event EventHandler<SyncErrorEventArgs> SyncError;

        public SyncService(LocalCacheDbContext localDb, PosDbContext serverDb, HttpClient httpClient)
        {
            _localDb = localDb ?? throw new ArgumentNullException(nameof(localDb));
            _serverDb = serverDb ?? throw new ArgumentNullException(nameof(serverDb));
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _syncSemaphore = new SemaphoreSlim(1, 1);

            // Initialize sync timer (default: every 30 seconds)
            _syncTimer = new Timer(async _ => await AutoSyncAsync(), null, TimeSpan.Zero, TimeSpan.FromSeconds(30));
        }

        /// <summary>
        /// مزامنة تلقائية دورية
        /// </summary>
        private async Task AutoSyncAsync()
        {
            if (_isSyncing) return;

            try
            {
                var autoSyncSetting = await _localDb.AppSettings.FindAsync(SettingsKeys.AutoSync);
                if (autoSyncSetting?.Value?.ToLower() == "true")
                {
                    await SyncAllAsync();
                }
            }
            catch (Exception ex)
            {
                OnSyncError(new SyncErrorEventArgs { Error = ex, Message = "خطأ في المزامنة التلقائية" });
            }
        }

        /// <summary>
        /// مزامنة شاملة لجميع البيانات
        /// </summary>
        public async Task<SyncResult> SyncAllAsync()
        {
            if (!await _syncSemaphore.WaitAsync(1000))
            {
                return new SyncResult { IsSuccess = false, Message = "المزامنة قيد التشغيل بالفعل" };
            }

            try
            {
                _isSyncing = true;
                var startTime = DateTime.Now;
                var result = new SyncResult();

                OnSyncProgress(new SyncProgressEventArgs { Message = "بدء المزامنة...", Progress = 0 });

                // Check connection
                if (!await IsServerReachableAsync())
                {
                    result.IsSuccess = false;
                    result.Message = "لا يمكن الوصول إلى السيرفر";
                    return result;
                }

                // Sync pending transactions (highest priority)
                OnSyncProgress(new SyncProgressEventArgs { Message = "مزامنة المعاملات...", Progress = 10 });
                var transactionResult = await SyncPendingTransactionsAsync();
                result.ProcessedItems += transactionResult.ProcessedItems;
                result.FailedItems += transactionResult.FailedItems;

                // Sync products from server
                OnSyncProgress(new SyncProgressEventArgs { Message = "تحديث المنتجات...", Progress = 30 });
                var productResult = await SyncProductsFromServerAsync();
                result.ProcessedItems += productResult.ProcessedItems;
                result.FailedItems += productResult.FailedItems;

                // Sync customers from server
                OnSyncProgress(new SyncProgressEventArgs { Message = "تحديث العملاء...", Progress = 50 });
                var customerResult = await SyncCustomersFromServerAsync();
                result.ProcessedItems += customerResult.ProcessedItems;
                result.FailedItems += customerResult.FailedItems;

                // Sync warehouses from server
                OnSyncProgress(new SyncProgressEventArgs { Message = "تحديث المستودعات...", Progress = 70 });
                var warehouseResult = await SyncWarehousesFromServerAsync();
                result.ProcessedItems += warehouseResult.ProcessedItems;
                result.FailedItems += warehouseResult.FailedItems;

                // Sync warehouse products (stock levels)
                OnSyncProgress(new SyncProgressEventArgs { Message = "تحديث المخزون...", Progress = 90 });
                var stockResult = await SyncWarehouseProductsFromServerAsync();
                result.ProcessedItems += stockResult.ProcessedItems;
                result.FailedItems += stockResult.FailedItems;

                // Update last sync time
                await UpdateLastSyncTimeAsync();

                result.Duration = DateTime.Now - startTime;
                result.IsSuccess = result.FailedItems == 0;
                result.Message = result.IsSuccess ? "تمت المزامنة بنجاح" : $"تمت المزامنة مع {result.FailedItems} أخطاء";

                OnSyncProgress(new SyncProgressEventArgs { Message = "اكتملت المزامنة", Progress = 100 });
                OnSyncCompleted(new SyncCompletedEventArgs { Result = result });

                return result;
            }
            catch (Exception ex)
            {
                var errorResult = new SyncResult
                {
                    IsSuccess = false,
                    Message = $"خطأ في المزامنة: {ex.Message}",
                    Errors = { ex.Message }
                };

                OnSyncError(new SyncErrorEventArgs { Error = ex, Message = "خطأ في المزامنة الشاملة" });
                return errorResult;
            }
            finally
            {
                _isSyncing = false;
                _syncSemaphore.Release();
            }
        }

        /// <summary>
        /// مزامنة المعاملات المعلقة إلى السيرفر
        /// </summary>
        public async Task<SyncResult> SyncPendingTransactionsAsync()
        {
            var result = new SyncResult();

            try
            {
                var pendingTransactions = await _localDb.LocalPosTransactions
                    .Where(t => t.SyncStatus == SyncStatus.Pending || t.SyncStatus == SyncStatus.Failed)
                    .OrderBy(t => t.CreatedAt)
                    .ToListAsync();

                foreach (var transaction in pendingTransactions)
                {
                    try
                    {
                        // Update status to syncing
                        transaction.SyncStatus = SyncStatus.Syncing;
                        await _localDb.SaveChangesAsync();

                        // Send to server via API
                        var response = await _httpClient.PostAsync("/api/pos", 
                            new StringContent(transaction.TransactionData, Encoding.UTF8, "application/json"));

                        if (response.IsSuccessStatusCode)
                        {
                            var responseContent = await response.Content.ReadAsStringAsync();
                            var serverResponse = JsonSerializer.Deserialize<ApiResponse>(responseContent);

                            if (serverResponse.Success)
                            {
                                transaction.SyncStatus = SyncStatus.Synced;
                                transaction.SyncedAt = DateTime.Now;
                                transaction.ServerPosId = serverResponse.Data?.GetProperty("pos_id").GetInt64();
                                result.ProcessedItems++;
                            }
                            else
                            {
                                transaction.SyncStatus = SyncStatus.Failed;
                                transaction.ErrorMessage = serverResponse.Message;
                                result.FailedItems++;
                            }
                        }
                        else
                        {
                            transaction.SyncStatus = SyncStatus.Failed;
                            transaction.ErrorMessage = $"HTTP Error: {response.StatusCode}";
                            result.FailedItems++;
                        }

                        transaction.RetryCount++;
                        await _localDb.SaveChangesAsync();
                    }
                    catch (Exception ex)
                    {
                        transaction.SyncStatus = SyncStatus.Failed;
                        transaction.ErrorMessage = ex.Message;
                        transaction.RetryCount++;
                        result.FailedItems++;
                        result.Errors.Add($"Transaction {transaction.Id}: {ex.Message}");

                        await _localDb.SaveChangesAsync();
                    }
                }

                result.IsSuccess = result.FailedItems == 0;
                result.Message = $"تمت معالجة {result.ProcessedItems} معاملة، فشل {result.FailedItems}";
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.Message = $"خطأ في مزامنة المعاملات: {ex.Message}";
                result.Errors.Add(ex.Message);
            }

            return result;
        }

        /// <summary>
        /// مزامنة المنتجات من السيرفر
        /// </summary>
        public async Task<SyncResult> SyncProductsFromServerAsync()
        {
            var result = new SyncResult();

            try
            {
                var lastSyncTime = await GetLastSyncTimeAsync();
                var url = $"/api/products?since={lastSyncTime:yyyy-MM-dd HH:mm:ss}";

                var response = await _httpClient.GetAsync(url);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var apiResponse = JsonSerializer.Deserialize<ApiResponse>(content);

                    if (apiResponse.Success && apiResponse.Data != null)
                    {
                        var products = JsonSerializer.Deserialize<List<JsonElement>>(apiResponse.Data.ToString());

                        foreach (var productElement in products)
                        {
                            try
                            {
                                var productId = productElement.GetProperty("id").GetInt64();
                                var productJson = productElement.GetRawText();

                                var existingProduct = await _localDb.LocalProducts
                                    .FirstOrDefaultAsync(p => p.ServerProductId == productId);

                                if (existingProduct != null)
                                {
                                    existingProduct.ProductData = productJson;
                                    existingProduct.LastUpdated = DateTime.Now;
                                    existingProduct.Name = productElement.GetProperty("name").GetString();
                                    existingProduct.Sku = productElement.GetProperty("sku").GetString();
                                    existingProduct.SalePrice = productElement.GetProperty("sale_price").GetDecimal();
                                }
                                else
                                {
                                    var newProduct = new LocalProduct
                                    {
                                        ServerProductId = productId,
                                        ProductData = productJson,
                                        LastUpdated = DateTime.Now,
                                        Name = productElement.GetProperty("name").GetString(),
                                        Sku = productElement.GetProperty("sku").GetString(),
                                        SalePrice = productElement.GetProperty("sale_price").GetDecimal(),
                                        IsActive = true
                                    };

                                    _localDb.LocalProducts.Add(newProduct);
                                }

                                result.ProcessedItems++;
                            }
                            catch (Exception ex)
                            {
                                result.FailedItems++;
                                result.Errors.Add($"Product sync error: {ex.Message}");
                            }
                        }

                        await _localDb.SaveChangesAsync();
                        result.IsSuccess = true;
                        result.Message = $"تم تحديث {result.ProcessedItems} منتج";
                    }
                }
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.Message = $"خطأ في مزامنة المنتجات: {ex.Message}";
                result.Errors.Add(ex.Message);
            }

            return result;
        }

        /// <summary>
        /// مزامنة العملاء من السيرفر
        /// </summary>
        public async Task<SyncResult> SyncCustomersFromServerAsync()
        {
            var result = new SyncResult();

            try
            {
                var lastSyncTime = await GetLastSyncTimeAsync();
                var url = $"/api/customers?since={lastSyncTime:yyyy-MM-dd HH:mm:ss}";

                var response = await _httpClient.GetAsync(url);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var apiResponse = JsonSerializer.Deserialize<ApiResponse>(content);

                    if (apiResponse.Success && apiResponse.Data != null)
                    {
                        var customers = JsonSerializer.Deserialize<List<JsonElement>>(apiResponse.Data.ToString());

                        foreach (var customerElement in customers)
                        {
                            try
                            {
                                var customerId = customerElement.GetProperty("id").GetInt64();
                                var customerJson = customerElement.GetRawText();

                                var existingCustomer = await _localDb.LocalCustomers
                                    .FirstOrDefaultAsync(c => c.ServerCustomerId == customerId);

                                if (existingCustomer != null)
                                {
                                    existingCustomer.CustomerData = customerJson;
                                    existingCustomer.LastUpdated = DateTime.Now;
                                    existingCustomer.Name = customerElement.GetProperty("name").GetString();
                                    existingCustomer.Contact = customerElement.GetProperty("contact").GetString();
                                    existingCustomer.Email = customerElement.GetProperty("email").GetString();
                                }
                                else
                                {
                                    var newCustomer = new LocalCustomer
                                    {
                                        ServerCustomerId = customerId,
                                        CustomerData = customerJson,
                                        LastUpdated = DateTime.Now,
                                        Name = customerElement.GetProperty("name").GetString(),
                                        Contact = customerElement.GetProperty("contact").GetString(),
                                        Email = customerElement.GetProperty("email").GetString(),
                                        IsActive = true
                                    };

                                    _localDb.LocalCustomers.Add(newCustomer);
                                }

                                result.ProcessedItems++;
                            }
                            catch (Exception ex)
                            {
                                result.FailedItems++;
                                result.Errors.Add($"Customer sync error: {ex.Message}");
                            }
                        }

                        await _localDb.SaveChangesAsync();
                        result.IsSuccess = true;
                        result.Message = $"تم تحديث {result.ProcessedItems} عميل";
                    }
                }
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.Message = $"خطأ في مزامنة العملاء: {ex.Message}";
                result.Errors.Add(ex.Message);
            }

            return result;
        }

        /// <summary>
        /// مزامنة المستودعات من السيرفر
        /// </summary>
        public async Task<SyncResult> SyncWarehousesFromServerAsync()
        {
            // Similar implementation to SyncCustomersFromServerAsync
            // Implementation details...
            return new SyncResult { IsSuccess = true, Message = "تم تحديث المستودعات" };
        }

        /// <summary>
        /// مزامنة مخزون المستودعات من السيرفر
        /// </summary>
        public async Task<SyncResult> SyncWarehouseProductsFromServerAsync()
        {
            // Implementation for syncing warehouse stock levels
            // Implementation details...
            return new SyncResult { IsSuccess = true, Message = "تم تحديث المخزون" };
        }

        /// <summary>
        /// فحص إمكانية الوصول للسيرفر
        /// </summary>
        public async Task<bool> IsServerReachableAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("/api/health", 
                    new CancellationTokenSource(TimeSpan.FromSeconds(5)).Token);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// الحصول على آخر وقت مزامنة
        /// </summary>
        private async Task<DateTime> GetLastSyncTimeAsync()
        {
            var setting = await _localDb.AppSettings.FindAsync(SettingsKeys.LastSyncTime);
            if (setting != null && DateTime.TryParse(setting.Value, out DateTime lastSync))
            {
                return lastSync;
            }
            return DateTime.MinValue;
        }

        /// <summary>
        /// تحديث آخر وقت مزامنة
        /// </summary>
        private async Task UpdateLastSyncTimeAsync()
        {
            var setting = await _localDb.AppSettings.FindAsync(SettingsKeys.LastSyncTime);
            if (setting != null)
            {
                setting.Value = DateTime.Now.ToString();
                setting.LastUpdated = DateTime.Now;
            }
            else
            {
                _localDb.AppSettings.Add(new AppSettings
                {
                    Key = SettingsKeys.LastSyncTime,
                    Value = DateTime.Now.ToString(),
                    LastUpdated = DateTime.Now
                });
            }

            await _localDb.SaveChangesAsync();
        }

        // Event handlers
        protected virtual void OnSyncProgress(SyncProgressEventArgs e) => SyncProgress?.Invoke(this, e);
        protected virtual void OnSyncCompleted(SyncCompletedEventArgs e) => SyncCompleted?.Invoke(this, e);
        protected virtual void OnSyncError(SyncErrorEventArgs e) => SyncError?.Invoke(this, e);

        public void Dispose()
        {
            if (!_isDisposed)
            {
                _syncTimer?.Dispose();
                _syncSemaphore?.Dispose();
                _isDisposed = true;
            }
        }
    }

    // Event argument classes
    public class SyncProgressEventArgs : EventArgs
    {
        public string Message { get; set; }
        public int Progress { get; set; } // 0-100
    }

    public class SyncCompletedEventArgs : EventArgs
    {
        public SyncResult Result { get; set; }
    }

    public class SyncErrorEventArgs : EventArgs
    {
        public Exception Error { get; set; }
        public string Message { get; set; }
    }

    // API Response model
    public class ApiResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public JsonElement? Data { get; set; }
    }
}
