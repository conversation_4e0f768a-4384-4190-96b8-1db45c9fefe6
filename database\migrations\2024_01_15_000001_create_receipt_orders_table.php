<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('receipt_orders', function (Blueprint $table) {
            $table->id();
            $table->string('order_number')->unique();
            $table->enum('order_type', ['استلام بضاعة', 'نقل بضاعة', 'أمر إخراج']);
            $table->unsignedBigInteger('vendor_id')->nullable();
            $table->unsignedBigInteger('warehouse_id');
            $table->unsignedBigInteger('from_warehouse_id')->nullable();
            $table->string('invoice_number')->nullable();
            $table->decimal('invoice_total', 15, 2)->nullable();
            $table->date('invoice_date')->nullable();
            $table->boolean('has_return')->default(false);
            $table->integer('total_products')->default(0);
            $table->decimal('total_amount', 15, 2)->default(0);
            $table->text('notes')->nullable();
            $table->enum('exit_reason', ['فقدان', 'منتهي الصلاحية', 'تلف/خراب', 'بيع بالتجزئة'])->nullable();
            $table->date('exit_date')->nullable();
            $table->string('responsible_person')->nullable();
            $table->unsignedBigInteger('created_by');
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('vendor_id')->references('id')->on('venders')->onDelete('set null');
            $table->foreign('warehouse_id')->references('id')->on('warehouses')->onDelete('cascade');
            $table->foreign('from_warehouse_id')->references('id')->on('warehouses')->onDelete('set null');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');

            // Indexes for better performance
            $table->index(['order_type', 'created_by']);
            $table->index(['warehouse_id', 'created_by']);
            $table->index(['vendor_id', 'created_by']);
            $table->index(['invoice_date', 'created_by']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('receipt_orders');
    }
};
