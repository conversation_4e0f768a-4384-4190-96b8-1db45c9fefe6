
@extends('layouts.admin')
@section('page-title')
    {{__('Purchase Create')}}
@endsection

@push('css-page')
<style>
    .product-image-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 60px;
    }

    .product-image {
        border-radius: 8px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .product-image:hover {
        border-color: #007bff;
        transform: scale(1.05);
    }

    .table th {
        background-color: #f8f9fa;
        font-weight: 600;
        border-bottom: 2px solid #dee2e6;
    }

    .amount-display {
        font-size: 1.1em;
        padding: 8px;
        background-color: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e9ecef;
    }

    .summary-item .form-label {
        font-weight: 600;
        margin-bottom: 8px;
    }
</style>
@endpush
@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item"><a href="{{route('purchase.index')}}">{{__('Purchase')}}</a></li>
    <li class="breadcrumb-item">{{__('Purchase Create')}}</li>
@endsection
@push('script-page')
    <script src="{{asset('js/jquery-ui.min.js')}}"></script>
    <script src="{{asset('js/jquery.repeater.min.js')}}"></script>
    <!-- Select2 JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

    <style>
        .product-row {
            border-bottom: 2px solid #f8f9fa;
        }

        .description-row {
            background-color: #f8f9fa;
        }

        .amount-display {
            background-color: #e3f2fd;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #bbdefb;
        }

        .summary-item {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .summary-item:last-child {
            border-bottom: none;
        }

        .taxes-display .badge {
            font-size: 0.75rem;
            margin: 2px;
        }

        .card-header {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
        }

        .table th {
            background-color: #f1f3f4;
            font-weight: 600;
            border-bottom: 2px solid #dee2e6;
        }

        .input-group-text {
            font-size: 0.875rem;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        /* تحسين Select2 للمنتجات */
        .select2-container {
            width: 100% !important;
        }

        .select2-container--bootstrap-5 .select2-selection {
            min-height: calc(1.5em + 0.75rem + 2px);
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            color: #212529;
            background-color: #fff;
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
        }

        .select2-dropdown {
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .select2-search--dropdown .select2-search__field {
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
            width: 100%;
        }

        .select2-results__option {
            padding: 0.5rem 0.75rem;
            font-size: 1rem;
            line-height: 1.5;
        }

        .select2-results__option--highlighted {
            background-color: var(--bs-primary);
            color: #fff;
        }

        /* تحسين عرض النتائج */
        .product-option {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .product-option-title {
            font-weight: 600;
            color: #212529;
        }

        .product-option-details {
            font-size: 0.875rem;
            color: #6c757d;
            display: flex;
            gap: 1rem;
        }

        .product-option-price,
        .product-option-sku,
        .product-option-unit {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }
    </style>
    <script>
        var selector = "body";
        if ($(selector + " .repeater").length) {
            var $dragAndDrop = $("body .repeater tbody").sortable({
                handle: '.sort-handler'
            });

            // تعطيل jQuery Repeater الأصلي واستخدام المعالجات المخصصة
            /*
            var $repeater = $(selector + ' .repeater').repeater({
                initEmpty: false,
                defaultValues: {
                    'status': 1
                },
                show: function () {
                    $(this).slideDown();
                    var file_uploads = $(this).find('input.multi');
                    if (file_uploads.length) {
                        $(this).find('input.multi').MultiFile({
                            max: 3,
                            accept: 'png|jpg|jpeg',
                            max_size: 2048
                        });
                    }

                    // إعادة تهيئة Select2 للصف الجديد
                    setTimeout(function() {
                        initializeProductSelects();
                        initializeChartAccountSelect();
                        updateSummaryInfo();
                    }, 200);

                    // تنظيف عمود الوحدة
                    var toSelect = $(this).find('.unit');
                    toSelect.html('');

                    $('.price').change();
                    $('.discount').change();
                },
                hide: function (deleteElement) {
                    // التأكد من وجود أكثر من صف واحد قبل الحذف
                    if ($('.product-row').length <= 1) {
                        alert('{{ __("لا يمكن حذف آخر منتج في الفاتورة") }}');
                        return false;
                    }

                    if (confirm('{{ __("هل أنت متأكد من حذف هذا المنتج؟") }}')) {
                        $(this).slideUp(deleteElement);
                        $(this).remove();

                        $(".price").change();
                        $(".discount").change();

                        // إعادة تهيئة الخيارات المخفية
                        $('.item option').prop('hidden', false);
                        $('.item :selected').each(function () {
                            var id = $(this).val();
                            if (id) {
                                $('.item').not(this).find("option[value=" + id + "]").prop('hidden', true);
                            }
                        });

                        // تحديث المجاميع
                        var inputs = $(".amount");
                        var subTotal = 0;
                        for (var i = 0; i < inputs.length; i++) {
                            subTotal = parseFloat(subTotal) + parseFloat($(inputs[i]).html());
                        }
                        $('.subTotal').html(subTotal.toFixed(2));
                        $('.totalAmount').html(subTotal.toFixed(2));

                        // تحديث المعلومات الإضافية
                        updateSummaryInfo();
                    }
                },
                ready: function (setIndexes) {
                    $dragAndDrop.on('drop', setIndexes);
                    $('.select2').select2();

                },
                isFirstItemUndeletable: true
            });
            var value = $(selector + " .repeater").attr('data-value');
            if (typeof value != 'undefined' && value.length != 0) {
                value = JSON.parse(value);
                $repeater.setList(value);
            }
            */

        }

        $(document).on('change', '#vender', function () {
            $('#vender_detail').removeClass('d-none');
            $('#vender_detail').addClass('d-block');
            $('#vender-box').removeClass('d-block');
            $('#vender-box').addClass('d-none');
            var id = $(this).val();
            var url = $(this).data('url');
            $.ajax({
                url: url,
                type: 'POST',
                headers: {
                    'X-CSRF-TOKEN': jQuery('#token').val()
                },
                data: {
                    'id': id
                },
                cache: false,
                success: function (data) {
                    if (data != '') {
                        $('#vender_detail').html(data);
                    } else {
                        $('#vender-box').removeClass('d-none');
                        $('#vender-box').addClass('d-block');
                        $('#vender_detail').removeClass('d-block');
                        $('#vender_detail').addClass('d-none');
                    }
                },
            });
        });

        $(document).on('click', '#remove', function () {
            $('#vender-box').removeClass('d-none');
            $('#vender-box').addClass('d-block');
            $('#vender_detail').removeClass('d-block');
            $('#vender_detail').addClass('d-none');
        })


        $(document).on('change', '.item', function () {
            var iteams_id = $(this).val();
            var url = $(this).data('url');
            var el = $(this);
            var selectedOption = $(this).find('option:selected');

            //disable SKU if product_id is empty
            if(iteams_id){
                el.parent().parent().find('.productSKU').prop('disabled', true);

                // تحديث صورة المنتج من البيانات المحفوظة
                var imageUrl = selectedOption.data('image') || '{{ asset("assets/images/default-product.svg") }}';
                $(el.closest('.product-row').find('.product-image')).attr('src', imageUrl);
            }else{
                el.parent().parent().find('.productSKU').prop('disabled', false);

                // إعادة تعيين الصورة الافتراضية
                $(el.closest('.product-row').find('.product-image')).attr('src', '{{ asset("assets/images/default-product.svg") }}');
            }

            $.ajax({
                url: url,
                type: 'POST',
                headers: {
                    'X-CSRF-TOKEN': jQuery('#token').val()
                },
                data: {
                    'product_id': iteams_id
                },
                cache: false,
                success: function (data) {
                    var item = JSON.parse(data);

                    $(el.parent().parent().find('.quantity')).val(1);
                    $(el.parent().parent().find('.price')).val(item.product?.purchase_price ?? 0);
                    $(el.parent().parent().parent().find('.pro_description')).val(item.product.description);
                    var taxes = '';
                    var tax = [];

                    var totalItemTaxRate = 0;
                    if (item.taxes == 0) {
                        taxes += '-';
                    } else {
                        for (var i = 0; i < item.taxes.length; i++) {

                            taxes += '<span class="badge bg-primary mt-1 mr-2">' + item.taxes[i].name + ' ' + '(' + item.taxes[i].rate + '%)' + '</span>';
                            tax.push(item.taxes[i].id);
                            totalItemTaxRate += parseFloat(item.taxes[i].rate);

                        }
                    }
                    var itemTaxPrice = parseFloat((totalItemTaxRate / 100) * ((item.product?.purchase_price??0) * 1));

                    $(el.parent().parent().find('.itemTaxPrice')).val(itemTaxPrice.toFixed(2));
                    $(el.parent().parent().find('.itemTaxRate')).val(totalItemTaxRate.toFixed(2));
                    $(el.parent().parent().parent().find('.taxes-display')).html(taxes);
                    $(el.parent().parent().parent().find('.tax')).val(tax);
                    var unitText = item.unit || '';
                    // إزالة كلمة "حبة" إذا كانت موجودة
                    if (unitText === 'حبة' || unitText === 'حبه') {
                        unitText = '';
                    }
                    $(el.parent().parent().parent().find('.unit')).html(unitText);
                    $(el.parent().parent().parent().find('.discount')).val(0);
                    $(el.parent().parent().parent().find('.amount')).html(item.totalAmount);

                    var inputs = $(".amount");
                    var subTotal = 0;
                    for (var i = 0; i < inputs.length; i++) {
                        subTotal = parseFloat(subTotal) + parseFloat($(inputs[i]).html());
                    }
                    $('.subTotal').html(subTotal.toFixed(2));


                    var totalItemPrice = 0;
                    var priceInput = $('.price');
                    for (var j = 0; j < priceInput.length; j++) {
                        if (!isNaN(parseFloat(priceInput[j].value))) {
                            totalItemPrice += parseFloat(priceInput[j].value);
                        }
                    }

                    var totalItemTaxPrice = 0;
                    var itemTaxPriceInput = $('.itemTaxPrice');
                    for (var j = 0; j < itemTaxPriceInput.length; j++) {
                        if (!isNaN(parseFloat(itemTaxPriceInput[j].value))) {
                            totalItemTaxPrice += parseFloat(itemTaxPriceInput[j].value);
                            const amountValue = el.parent().parent().parent().find('.amount').html();
                            // console.log("AMOUNT",amountValue);
                            // console.log("EL",el.parent().parent().parent().find('.amountInput'));
                            el.parent().parent().parent().find('.amountInput').val(parseFloat(amountValue));
                        }
                    }

                    $('.totalTax').html(totalItemTaxPrice.toFixed(2));
                    $('.totalAmount').html((parseFloat(subTotal) + parseFloat(totalItemTaxPrice)).toFixed(2));

                },
            });
        });

        $(document).on('change', '.item-sku', function () {
            var iteams_id = $(this).val();
            var url = $(this).data('url');
            var el = $(this);

            if(iteams_id){
                el.parent().parent().find('.productName').prop('disabled', true);
            }else{
                el.parent().parent().find('.productName').prop('disabled', false);
            }

            $.ajax({
                url: url,
                type: 'POST',
                headers: {
                    'X-CSRF-TOKEN': jQuery('#token').val()
                },
                data: {
                    'product_id': iteams_id
                },
                cache: false,
                success: function (data) {
                    var item = JSON.parse(data);

                    $(el.parent().parent().find('.quantity')).val(1);
                    $(el.parent().parent().find('.price')).val(item.product?.purchase_price ?? 0);
                    $(el.parent().parent().parent().find('.pro_description')).val(item.product.description);
                    var taxes = '';
                    var tax = [];

                    var totalItemTaxRate = 0;
                    if (item.taxes == 0) {
                        taxes += '-';
                    } else {
                        for (var i = 0; i < item.taxes.length; i++) {

                            taxes += '<span class="badge bg-primary mt-1 mr-2">' + item.taxes[i].name + ' ' + '(' + item.taxes[i].rate + '%)' + '</span>';
                            tax.push(item.taxes[i].id);
                            totalItemTaxRate += parseFloat(item.taxes[i].rate);

                        }
                    }
                    var itemTaxPrice = parseFloat((totalItemTaxRate / 100) * ((item.product?.purchase_price ?? 0) * 1));

                    $(el.parent().parent().find('.itemTaxPrice')).val(itemTaxPrice.toFixed(2));
                    $(el.parent().parent().find('.itemTaxRate')).val(totalItemTaxRate.toFixed(2));
                    $(el.parent().parent().parent().find('.taxes-display')).html(taxes);
                    $(el.parent().parent().parent().find('.tax')).val(tax);
                    var unitText = item.unit || '';
                    // إزالة كلمة "حبة" إذا كانت موجودة
                    if (unitText === 'حبة' || unitText === 'حبه') {
                        unitText = '';
                    }
                    $(el.parent().parent().parent().find('.unit')).html(unitText);
                    $(el.parent().parent().parent().find('.discount')).val(0);
                    $(el.parent().parent().parent().find('.amount')).html(item.totalAmount);

                    var inputs = $(".amount");
                    var subTotal = 0;
                    for (var i = 0; i < inputs.length; i++) {
                        subTotal = parseFloat(subTotal) + parseFloat($(inputs[i]).html());
                    }
                    $('.subTotal').html(subTotal.toFixed(2));


                    var totalItemPrice = 0;
                    var priceInput = $('.price');
                    for (var j = 0; j < priceInput.length; j++) {
                        if (!isNaN(parseFloat(priceInput[j].value))) {
                            totalItemPrice += parseFloat(priceInput[j].value);
                        }
                    }

                    var totalItemTaxPrice = 0;
                    var itemTaxPriceInput = $('.itemTaxPrice');
                    for (var j = 0; j < itemTaxPriceInput.length; j++) {
                        if (!isNaN(parseFloat(itemTaxPriceInput[j].value))) {
                            totalItemTaxPrice += parseFloat(itemTaxPriceInput[j].value);
                            const amountValue = el.parent().parent().parent().find('.amount').html();
                            // console.log("AMOUNT",amountValue);
                            // console.log("EL",el.parent().parent().parent().find('.amountInput'));
                            el.parent().parent().parent().find('.amountInput').val(parseFloat(amountValue));
                        }
                    }

                    $('.totalTax').html(totalItemTaxPrice.toFixed(2));
                    $('.totalAmount').html((parseFloat(subTotal) + parseFloat(totalItemTaxPrice)).toFixed(2));

                },
            });
        });

        $(document).on('keyup', '.quantity', function () {
            var quntityTotalTaxPrice = 0;

            var el = $(this).parent().parent().parent().parent();
            var quantity = $(this).val();
            var price = $(el.find('.price')).val();
            var discount = $(el.find('.discount')).val();
            if(discount.length <= 0)
            {
                discount = 0 ;
            }

            var totalItemPrice = (quantity * price) - discount;
            var amount = (totalItemPrice);


            var amount = (totalItemPrice);


            var totalItemTaxRate = $(el.find('.itemTaxRate')).val();
            var itemTaxPrice = parseFloat((totalItemTaxRate / 100) * (totalItemPrice));
            $(el.find('.itemTaxPrice')).val(itemTaxPrice.toFixed(2));

            $(el.find('.amount')).html(parseFloat(itemTaxPrice)+parseFloat(amount));
            $(el.find('.amountInput')).val(parseFloat(itemTaxPrice)+parseFloat(amount));


            var totalItemTaxPrice = 0;
            var itemTaxPriceInput = $('.itemTaxPrice');
            for (var j = 0; j < itemTaxPriceInput.length; j++) {
                totalItemTaxPrice += parseFloat(itemTaxPriceInput[j].value);
            }


            var totalItemPrice = 0;
            var inputs_quantity = $(".quantity");

            var priceInput = $('.price');
            for (var j = 0; j < priceInput.length; j++) {
                totalItemPrice += (parseFloat(priceInput[j].value) * parseFloat(inputs_quantity[j].value));
            }

            var inputs = $(".amount");

            var subTotal = 0;
            for (var i = 0; i < inputs.length; i++) {
                subTotal = parseFloat(subTotal) + parseFloat($(inputs[i]).html());
            }

            $('.subTotal').html(totalItemPrice.toFixed(2));
            $('.totalTax').html(totalItemTaxPrice.toFixed(2));
            $('.totalAmount').html((parseFloat(subTotal)).toFixed(2));

            // تحديث المعلومات الإضافية
            updateSummaryInfo();

        })

        $(document).on('keyup change', '.price', function () {
            var el = $(this).parent().parent().parent().parent();
            var price = $(this).val();
            var quantity = $(el.find('.quantity')).val();

            var discount = $(el.find('.discount')).val();
            if(discount.length <= 0)
            {
                discount = 0 ;
            }
            var totalItemPrice = (quantity * price)-discount;

            var amount = (totalItemPrice);


            var totalItemTaxRate = $(el.find('.itemTaxRate')).val();
            var itemTaxPrice = parseFloat((totalItemTaxRate / 100) * (totalItemPrice));
            $(el.find('.itemTaxPrice')).val(itemTaxPrice.toFixed(2));

            $(el.find('.amount')).html(parseFloat(itemTaxPrice)+parseFloat(amount));
            $(el.find('.amountInput')).val(parseFloat(itemTaxPrice)+parseFloat(amount));


            var totalItemTaxPrice = 0;
            var itemTaxPriceInput = $('.itemTaxPrice');
            for (var j = 0; j < itemTaxPriceInput.length; j++) {
                totalItemTaxPrice += parseFloat(itemTaxPriceInput[j].value);
            }


            var totalItemPrice = 0;
            var inputs_quantity = $(".quantity");

            var priceInput = $('.price');
            for (var j = 0; j < priceInput.length; j++) {
                totalItemPrice += (parseFloat(priceInput[j].value) * parseFloat(inputs_quantity[j].value));
            }

            var inputs = $(".amount");

            var subTotal = 0;
            for (var i = 0; i < inputs.length; i++) {
                subTotal = parseFloat(subTotal) + parseFloat($(inputs[i]).html());
            }

            $('.subTotal').html(totalItemPrice.toFixed(2));
            $('.totalTax').html(totalItemTaxPrice.toFixed(2));

            $('.totalAmount').html((parseFloat(subTotal)).toFixed(2));


        })

        $(document).on('keyup change', '.discount', function () {
            var el = $(this).parent().parent().parent();
            var discount = $(this).val();
            if(discount.length <= 0)
            {
                discount = 0 ;
            }

            var price = $(el.find('.price')).val();
            var quantity = $(el.find('.quantity')).val();
            var totalItemPrice = (quantity * price) - discount;


            var amount = (totalItemPrice);


            var totalItemTaxRate = $(el.find('.itemTaxRate')).val();
            var itemTaxPrice = parseFloat((totalItemTaxRate / 100) * (totalItemPrice));
            $(el.find('.itemTaxPrice')).val(itemTaxPrice.toFixed(2));

            $(el.find('.amount')).html(parseFloat(itemTaxPrice)+parseFloat(amount));


            var totalItemTaxPrice = 0;
            var itemTaxPriceInput = $('.itemTaxPrice');
            for (var j = 0; j < itemTaxPriceInput.length; j++) {
                totalItemTaxPrice += parseFloat(itemTaxPriceInput[j].value);
            }


            var totalItemPrice = 0;
            var inputs_quantity = $(".quantity");

            var priceInput = $('.price');
            for (var j = 0; j < priceInput.length; j++) {
                totalItemPrice += (parseFloat(priceInput[j].value) * parseFloat(inputs_quantity[j].value));
            }

            var inputs = $(".amount");

            var subTotal = 0;
            for (var i = 0; i < inputs.length; i++) {
                subTotal = parseFloat(subTotal) + parseFloat($(inputs[i]).html());
            }


            var totalItemDiscountPrice = 0;
            var itemDiscountPriceInput = $('.discount');

            for (var k = 0; k < itemDiscountPriceInput.length; k++) {
                if (!isNaN(parseFloat(itemDiscountPriceInput[k].value))) {
                    totalItemDiscountPrice += parseFloat(itemDiscountPriceInput[k].value);
                }
            }


            $('.subTotal').html(totalItemPrice.toFixed(2));
            $('.totalTax').html(totalItemTaxPrice.toFixed(2));

            $('.totalAmount').html((parseFloat(subTotal)).toFixed(2));
            $('.totalDiscount').html(totalItemDiscountPrice.toFixed(2));

            el.parent().find('.amountInput').val(parseFloat(itemTaxPrice)+parseFloat(amount));



        })

        var vendorId = '{{$vendorId}}';
        if (vendorId > 0) {
            $('#vender').val(vendorId).change();
        }

        // وظيفة تحديث المعلومات الإضافية في الملخص
        function updateSummaryInfo() {
            var productCount = 0;
            var totalQuantity = 0;
            var totalPrice = 0;

            $('.product-row').each(function() {
                var quantity = parseFloat($(this).find('.quantity').val()) || 0;
                var price = parseFloat($(this).find('.price').val()) || 0;
                var productSelected = $(this).find('.item').val();

                if (productSelected && quantity > 0) {
                    productCount++;
                    totalQuantity += quantity;
                    totalPrice += price;
                }
            });

            var averagePrice = productCount > 0 ? (totalPrice / productCount) : 0;

            $('.product-count').text(productCount);
            $('.total-quantity').text(totalQuantity);
            $('.average-price').text(averagePrice.toFixed(2));
        }

        // تحديث المعلومات عند تغيير أي حقل
        $(document).on('change keyup', '.quantity, .price, .item', function() {
            updateSummaryInfo();
        });

        // معالج الخصم الإجمالي
        $(document).on('change keyup', '.total-discount-input', function() {
            var totalDiscount = parseFloat($(this).val()) || 0;
            var subTotal = parseFloat($('.subTotal').text()) || 0;
            var totalTax = parseFloat($('.totalTax').text()) || 0;

            var finalTotal = (subTotal + totalTax) - totalDiscount;
            $('.totalAmount').text(finalTotal.toFixed(2));
        });

        // تحسين Select2 للمنتجات
        function initializeProductSelects() {
            // تهيئة Select2 للمنتجات
            $('.product-select').select2({
                theme: 'bootstrap-5',
                placeholder: '{{ __("اختر المنتج") }}',
                allowClear: true,
                width: '100%',
                language: {
                    noResults: function() {
                        return '{{ __("لا توجد نتائج") }}';
                    },
                    searching: function() {
                        return '{{ __("جاري البحث...") }}';
                    }
                },
                templateResult: function(option) {
                    if (!option.id) return option.text;

                    var $option = $(option.element);
                    var price = $option.data('price') || '0';
                    var sku = $option.data('sku') || '';
                    var unit = $option.data('unit') || '';

                    var $result = $('<div class="product-option">');
                    $result.append('<div class="product-option-title">' + option.text + '</div>');

                    var $details = $('<div class="product-option-details">');
                    if (sku) {
                        $details.append('<span class="product-option-sku"><i class="ti ti-barcode"></i> ' + sku + '</span>');
                    }
                    $details.append('<span class="product-option-price"><i class="ti ti-currency-dollar"></i> ' + price + '</span>');
                    if (unit) {
                        $details.append('<span class="product-option-unit"><i class="ti ti-package"></i> ' + unit + '</span>');
                    }

                    $result.append($details);
                    return $result;
                },
                templateSelection: function(option) {
                    if (!option.id) return option.text;

                    var $option = $(option.element);
                    var sku = $option.data('sku') || '';

                    return option.text + (sku ? ' (' + sku + ')' : '');
                }
            });
        }

        // تهيئة Select2 للحساب المحاسبي
        function initializeChartAccountSelect() {
            $('.chart-account-select').select2({
                theme: 'bootstrap-5',
                placeholder: '{{ __("اختر الحساب المحاسبي") }}',
                allowClear: true,
                width: '100%',
                language: {
                    noResults: function() {
                        return '{{ __("لا توجد نتائج") }}';
                    },
                    searching: function() {
                        return '{{ __("جاري البحث...") }}';
                    }
                },
                templateResult: function(option) {
                    if (!option.id) return option.text;

                    var $option = $(option.element);
                    var isSubAccount = $option.hasClass('sub-account');

                    var $result = $('<div>');
                    if (isSubAccount) {
                        $result.addClass('ps-3 text-muted');
                        $result.html('<i class="ti ti-corner-down-right me-1"></i>' + option.text);
                    } else {
                        $result.addClass('fw-bold');
                        $result.html('<i class="ti ti-folder me-1"></i>' + option.text);
                    }

                    return $result;
                },
                templateSelection: function(option) {
                    if (!option.id) return option.text;

                    var $option = $(option.element);
                    var isSubAccount = $option.hasClass('sub-account');

                    if (isSubAccount) {
                        return '↳ ' + option.text.trim();
                    }
                    return option.text;
                }
            });
        }

        // تهيئة Select2 عند تحميل الصفحة
        initializeProductSelects();
        initializeChartAccountSelect();

        // دالة إضافة صف منتج جديد (متاحة عالمياً)
        window.addNewProductRow = function() {
            console.log('تنفيذ دالة إضافة صف جديد');

            // التحقق من وجود صف أول
            var $firstRow = $('.product-row').first();
            if ($firstRow.length === 0) {
                console.error('لا يوجد صف منتج للاستنساخ');
                return false;
            }

            // استنساخ الصف الأول
            var $newRow = $firstRow.clone(true);

            // تنظيف القيم في الصف الجديد
            $newRow.find('input, select, textarea').each(function() {
                if ($(this).is('select')) {
                    $(this).val('').trigger('change');
                } else if ($(this).hasClass('quantity')) {
                    $(this).val('1');
                } else {
                    $(this).val('');
                }
            });

            // تنظيف النصوص المعروضة
            $newRow.find('.unit').html('');
            $newRow.find('.amount').html('0.00');
            $newRow.find('.product-image').attr('src', '{{ asset("assets/images/default-product.svg") }}');

            // تحديث أسماء الحقول للصف الجديد
            var newIndex = $('.product-row').length;
            $newRow.find('[name*="items[0]"]').each(function() {
                var name = $(this).attr('name');
                if (name) {
                    $(this).attr('name', name.replace('items[0]', 'items[' + newIndex + ']'));
                }
            });

            // إضافة الصف الجديد
            $('.repeater tbody').append($newRow);

            // إعادة تهيئة Select2 للصف الجديد
            setTimeout(function() {
                initializeProductSelects();
                updateSummaryInfo();

                // إعادة تهيئة الخيارات المخفية
                $('.item option').prop('hidden', false);
                $('.item :selected').each(function () {
                    var id = $(this).val();
                    if (id) {
                        $('.item').not(this).find("option[value=" + id + "]").prop('hidden', true);
                    }
                });
            }, 300);

            console.log('تم إضافة صف جديد بنجاح');
            return true;
        };

        // معالج إضافي للتأكد من عمل زر الإضافة
        $('body').on('click', 'button[data-repeater-create]', function(e) {
            e.preventDefault();
            console.log('معالج بديل: زر إضافة المنتج تم النقر عليه');
            addNewProductRow();
        });

        // معالج مباشر للزر عند تحميل الصفحة
        $(document).ready(function() {
            console.log('تم تحميل الصفحة - البحث عن زر الإضافة');
            var addButton = $('.btn[data-repeater-create]');
            console.log('عدد أزرار الإضافة الموجودة:', addButton.length);

            addButton.on('click', function(e) {
                e.preventDefault();
                console.log('معالج مباشر: زر إضافة المنتج تم النقر عليه');
                addNewProductRow();
            });

            // اختبار إضافي
            $('button').each(function() {
                if ($(this).attr('data-repeater-create') !== undefined) {
                    console.log('تم العثور على زر الإضافة:', this);
                }
            });
        });

        // إزالة أي نص افتراضي من عمود الوحدة
        $('.unit').each(function() {
            if ($(this).text().trim() === 'حبة' || $(this).text().trim() === '') {
                $(this).html('');
            }
        });

        // مراقبة إضافة صفوف جديدة
        var observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1 && $(node).hasClass('product-row')) {
                            setTimeout(function() {
                                initializeProductSelects();
                                initializeChartAccountSelect();
                                updateSummaryInfo();
                            }, 300);
                        }
                    });
                }
            });
        });

        // بدء مراقبة التغييرات
        observer.observe(document.querySelector('.repeater tbody'), {
            childList: true,
            subtree: true
        });

        // معالج مخصص لزر إضافة المنتج
        $(document).on('click', '[data-repeater-create]', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('المعالج الأصلي: زر إضافة المنتج تم النقر عليه');
            addNewProductRow();
            return false;
        });

        // معالج مخصص لزر حذف المنتج
        $(document).on('click', '[data-repeater-delete]', function(e) {
            e.preventDefault();
            console.log('زر حذف المنتج تم النقر عليه');

            // التأكد من وجود أكثر من صف واحد
            if ($('.product-row').length <= 1) {
                alert('{{ __("لا يمكن حذف آخر منتج في الفاتورة") }}');
                return false;
            }

            if (confirm('{{ __("هل أنت متأكد من حذف هذا المنتج؟") }}')) {
                var $row = $(this).closest('.product-row');

                // حذف الصف
                $row.fadeOut(300, function() {
                    $(this).remove();

                    // تحديث المجاميع
                    $(".price").change();
                    $(".discount").change();
                    updateSummaryInfo();

                    // إعادة تهيئة الخيارات المخفية
                    setTimeout(function() {
                        $('.item option').prop('hidden', false);
                        $('.item :selected').each(function () {
                            var id = $(this).val();
                            if (id) {
                                $('.item').not(this).find("option[value=" + id + "]").prop('hidden', true);
                            }
                        });
                    }, 100);
                });
            }

            return false;
        });



        $(document).on('change', '.item', function () {
            $('.item option').prop('hidden', false);
            $('.item :selected').each(function () {
                var id = $(this).val();
                if (id) {
                    $('.item').not(this).find("option[value=" + id + "]").prop('hidden', true);
                }
            });
        });


    </script>


@endpush

@section('content')
    <div class="row">
        {{ Form::open(array('url' => 'purchase','class'=>'w-100', 'class'=>'needs-validation', 'novalidate','enctype' => 'multipart/form-data')) }}
        <div class="col-12">
            <input type="hidden" name="_token" id="token" value="{{ csrf_token() }}">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group" id="vender-box">
                                {{ Form::label('vender_id', __('Vendor'),['class'=>'form-label']) }}<x-required></x-required>
                                {{ Form::select('vender_id', $venders,$vendorId, array('class' => 'form-control select2','id'=>'vender','data-url'=>route('bill.vender'),'required'=>'required')) }}
                            </div>
                            <div id="vender_detail" class="d-none">
                            </div>
                        </div>


                        <div class="col-md-6">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ Form::label('warehouse_id', __('Warehouse'),['class'=>'form-label']) }}<x-required></x-required>
                                        {{ Form::select('warehouse_id', $warehouse,null, array('class' => 'form-control select','required'=>'required')) }}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ Form::label('category_id', __('Category'),['class'=>'form-label']) }}<x-required></x-required>
                                        {{ Form::select('category_id', $category,null, array('class' => 'form-control select','required'=>'required')) }}
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ Form::label('purchase_date', __('Purchase Date'),['class'=>'form-label']) }}<x-required></x-required>
                                        {{Form::date('purchase_date',null,array('class'=>'form-control','required'=>'required'))}}

                                    </div>
                                </div>


                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ Form::label('purchase_number', __('Purchase Number'),['class'=>'form-label']) }}
                                        <input type="text" class="form-control" value="{{$purchase_number}}" readonly>

                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ Form::label('order_number', __('Order Number'),['class'=>'form-label']) }}<x-required></x-required>
                                        {{ Form::number('order_number', '', array('class' => 'form-control' , 'placeholder'=>__('Enter Order Number'),'required'=>'required')) }}
                                    </div>
                                </div>
                                @if(Auth::user()->can('manage bill'))
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            {{ Form::label('invoice_image', __('Invoice Image'),['class'=>'form-label']) }}
                                            <input type="file" class="form-control" name="invoice_image" id="invoice_image" accept=".pdf,.jpeg,.png,.jpg,.docx,.doc,.gif,.bmp,.tiff,.webp">

                                        </div>
                                    </div>
                                @endif
                            </div>


                            </div>
                        </div>
                    </div>
                </div>
            </div>

        <div class="col-12">
            <div class="row">
                <!-- قسم المنتجات -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">{{__('منتجات الفاتورة')}}</h5>
                            <button type="button" class="btn btn-success btn-sm" data-repeater-create="" onclick="addNewProductRow(); return false;">
                                <i class="ti ti-plus"></i> {{__('إضافة منتج')}}
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered" data-repeater-list="items" id="sortable-table">
                                    <thead class="table-light">
                                        <tr>
                                            <th width="30%">{{__('المنتج')}}</th>
                                            <th width="10%">{{__('الصورة')}}</th>
                                            <th width="15%">{{__('الكمية')}}</th>
                                            <th width="15%">{{__('السعر')}}</th>
                                            <th width="20%">{{__('المجموع')}}</th>
                                            <th width="10%">{{__('حذف')}}</th>
                                        </tr>
                                    </thead>
                                    <tbody class="ui-sortable repeater" data-repeater-item>
                                        <tr class="product-row">
                                            <td>
                                                <div class="form-group">
                                                    <select name="items[0][item]" class="form-control select2 item productName product-select" data-url="{{ route('purchase.product') }}" required>
                                                        <option value="">{{ __('اختر المنتج') }}</option>
                                                        @if(isset($product_services) && count($product_services) > 0)
                                                            @foreach($product_services as $id => $name)
                                                                @if($id !== '')
                                                                    @php
                                                                        $product = \App\Models\ProductService::find($id);
                                                                    @endphp
                                                                    <option value="{{ $id }}"
                                                                        data-price="{{ $product->purchase_price ?? 0 }}"
                                                                        data-sku="{{ $product->sku ?? '' }}"
                                                                        data-unit="{{ $product->unit->name ?? '' }}"
                                                                        data-image="{{ $product->image ? asset('storage/uploads/product/' . $product->image) : asset('assets/images/default-product.svg') }}">
                                                                        {{ $name }} @if($product->sku ?? false)({{ $product->sku }})@endif
                                                                    </option>
                                                                @endif
                                                            @endforeach
                                                        @endif
                                                    </select>
                                                </div>
                                            </td>
                                            <!-- عمود الصورة -->
                                            <td class="text-center">
                                                <div class="product-image-container">
                                                    <img src="{{ asset('assets/images/default-product.svg') }}"
                                                         alt="صورة المنتج"
                                                         class="product-image img-thumbnail"
                                                         style="width: 50px; height: 50px; object-fit: cover;">
                                                </div>
                                            </td>

                                            <!-- عمود الكمية -->
                                            <td>
                                                <div class="input-group">
                                                    {{ Form::number('quantity','1', array('class' => 'form-control quantity text-center','required'=>'required','min'=>'1','step'=>'1')) }}
                                                    <span class="unit input-group-text bg-light"></span>
                                                </div>
                                            </td>

                                            <!-- عمود السعر -->
                                            <td>
                                                <div class="input-group">
                                                    {{ Form::number('price','', array('class' => 'form-control price','required'=>'required','step'=>'0.01','min'=>'0')) }}
                                                    <span class="input-group-text bg-light">{{\Auth::user()->currencySymbol()}}</span>
                                                </div>
                                            </td>

                                            <!-- عمود المجموع -->
                                            <td>
                                                <div class="text-center fw-bold amount-display">
                                                    <span class="amount">0.00</span>
                                                    <span class="currency-symbol">{{\Auth::user()->currencySymbol()}}</span>
                                                </div>
                                                <!-- حقول مخفية للضرائب والخصم -->
                                                {{ Form::hidden('discount','0', array('class' => 'form-control discount')) }}
                                                {{ Form::hidden('tax','', array('class' => 'form-control tax')) }}
                                                {{ Form::hidden('itemTaxPrice','0', array('class' => 'form-control itemTaxPrice')) }}
                                                {{ Form::hidden('itemTaxRate','0', array('class' => 'form-control itemTaxRate')) }}
                                            </td>
                                            <td class="text-center">
                                                <button type="button" class="btn btn-danger btn-sm" data-repeater-delete>
                                                    <i class="ti ti-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ملخص الفاتورة -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">{{__('ملخص الفاتورة')}}</h5>
                        </div>
                        <div class="card-body">
                            <div class="summary-item d-flex justify-content-between mb-3">
                                <span>{{__('المجموع الفرعي')}}:</span>
                                <span class="fw-bold">
                                    <span class="subTotal">0.00</span> {{\Auth::user()->currencySymbol()}}
                                </span>
                            </div>

                            <!-- حقل خصم من الإجمالي -->
                            <div class="summary-item mb-3">
                                <label class="form-label">{{__('خصم من الإجمالي')}}:</label>
                                <div class="input-group">
                                    {{ Form::number('total_discount','0', array('class' => 'form-control total-discount-input','step'=>'0.01','min'=>'0','placeholder'=>'0.00')) }}
                                    <span class="input-group-text bg-light">{{\Auth::user()->currencySymbol()}}</span>
                                </div>
                            </div>

                            <div class="summary-item d-flex justify-content-between mb-3">
                                <span>{{__('إجمالي الضريبة')}}:</span>
                                <span class="text-info">
                                    <span class="totalTax">0.00</span> {{\Auth::user()->currencySymbol()}}
                                </span>
                            </div>
                            <hr>
                            <div class="summary-item d-flex justify-content-between mb-0">
                                <span class="h6">{{__('المبلغ الإجمالي')}}:</span>
                                <span class="h5 text-primary fw-bold">
                                    <span class="totalAmount">0.00</span> {{\Auth::user()->currencySymbol()}}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="mb-0">{{__('معلومات إضافية')}}</h6>
                        </div>
                        <div class="card-body">
                            <div class="info-item mb-2">
                                <small class="text-muted">{{__('عدد المنتجات')}}:</small>
                                <span class="fw-bold product-count">0</span>
                            </div>
                            <div class="info-item mb-2">
                                <small class="text-muted">{{__('إجمالي الكمية')}}:</small>
                                <span class="fw-bold total-quantity">0</span>
                            </div>
                            <div class="info-item">
                                <small class="text-muted">{{__('متوسط سعر المنتج')}}:</small>
                                <span class="fw-bold average-price">0.00</span> {{\Auth::user()->currencySymbol()}}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal-footer">
            <input type="button" value="{{__('Cancel')}}" onclick="location.href = '{{route("purchase.index")}}';" class="btn btn-secondary me-2">
            <input type="submit" value="{{__('Create')}}" class="btn  btn-primary">
        </div>
    {{ Form::close() }}
    </div>

@endsection

