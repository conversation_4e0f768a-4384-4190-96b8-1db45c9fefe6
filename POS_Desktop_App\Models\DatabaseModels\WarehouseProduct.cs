using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace POS_Desktop_App.Models.DatabaseModels
{
    /// <summary>
    /// نموذج مخزون المستودع - يطابق جدول warehouse_products في قاعدة البيانات
    /// </summary>
    [Table("warehouse_products")]
    public class WarehouseProduct
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("warehouse_id")]
        public int WarehouseId { get; set; } = 0;

        [Column("product_id")]
        public int ProductId { get; set; } = 0;

        [Column("quantity")]
        public int Quantity { get; set; } = 0;

        [Column("created_by")]
        public int CreatedBy { get; set; } = 0;

        [Column("created_at")]
        public DateTime CreatedAt { get; set; }

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; }

        // Navigation Properties
        public virtual Warehouse Warehouse { get; set; }
        public virtual Product Product { get; set; }

        // Calculated Properties
        [NotMapped]
        public string WarehouseName => Warehouse?.Name ?? "غير محدد";

        [NotMapped]
        public string ProductName => Product?.Name ?? "غير محدد";

        [NotMapped]
        public string ProductSku => Product?.Sku ?? "";

        [NotMapped]
        public decimal ProductSalePrice => Product?.SalePrice ?? 0;

        [NotMapped]
        public decimal TotalValue => Quantity * ProductSalePrice;

        [NotMapped]
        public bool IsLowStock
        {
            get
            {
                // You can make this configurable
                const int lowStockThreshold = 10;
                return Quantity <= lowStockThreshold;
            }
        }

        [NotMapped]
        public bool IsOutOfStock => Quantity <= 0;

        // Helper Methods
        public void AddStock(int quantity)
        {
            if (quantity > 0)
            {
                Quantity += quantity;
            }
        }

        public bool RemoveStock(int quantity)
        {
            if (quantity > 0 && Quantity >= quantity)
            {
                Quantity -= quantity;
                return true;
            }
            return false;
        }

        public void SetStock(int quantity)
        {
            if (quantity >= 0)
            {
                Quantity = quantity;
            }
        }

        public bool CanFulfillOrder(int requestedQuantity)
        {
            return Quantity >= requestedQuantity;
        }

        // Display Methods
        public string GetStockStatusText()
        {
            if (IsOutOfStock)
                return "نفد المخزون";
            else if (IsLowStock)
                return "مخزون منخفض";
            else
                return "متوفر";
        }

        public string GetFormattedTotalValue()
        {
            return TotalValue.ToString("C2");
        }

        // Validation
        public List<string> Validate()
        {
            var errors = new List<string>();

            if (WarehouseId <= 0)
                errors.Add("معرف المستودع غير صحيح");

            if (ProductId <= 0)
                errors.Add("معرف المنتج غير صحيح");

            if (Quantity < 0)
                errors.Add("الكمية لا يمكن أن تكون سالبة");

            return errors;
        }

        public bool IsValid()
        {
            return !Validate().Any();
        }

        // Override Methods
        public override string ToString()
        {
            return $"{ProductName} - {WarehouseName}: {Quantity}";
        }

        public override bool Equals(object obj)
        {
            if (obj is WarehouseProduct other)
            {
                return this.WarehouseId == other.WarehouseId && this.ProductId == other.ProductId;
            }
            return false;
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(WarehouseId, ProductId);
        }
    }
}
