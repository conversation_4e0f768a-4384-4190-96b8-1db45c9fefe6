<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DailySalesSummary extends Model
{
    protected $table = 'daily_sales_summary';

    protected $fillable = [
        'summary_date',
        'warehouse_id',
        'total_sales',
        'total_amount',
        'total_customers',
        'average_sale',
        'cash_amount',
        'network_amount',
        'created_by',
    ];

    protected $casts = [
        'summary_date' => 'date',
        'total_amount' => 'decimal:2',
        'average_sale' => 'decimal:2',
        'cash_amount' => 'decimal:2',
        'network_amount' => 'decimal:2',
    ];

    /**
     * العلاقة مع المستودع
     */
    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(\App\Models\warehouse::class);
    }

    /**
     * العلاقة مع المستخدم المنشئ
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * حساب نسبة النقد إلى الشبكة
     */
    public function getCashToNetworkRatioAttribute(): float
    {
        if ($this->network_amount == 0) {
            return 100; // كله نقد
        }
        
        if ($this->cash_amount == 0) {
            return 0; // كله شبكة
        }

        return round(($this->cash_amount / ($this->cash_amount + $this->network_amount)) * 100, 2);
    }

    /**
     * فلترة الملخصات حسب المستودع
     */
    public function scopeForWarehouse($query, $warehouseId)
    {
        if ($warehouseId) {
            return $query->where('warehouse_id', $warehouseId);
        }
        return $query;
    }

    /**
     * فلترة الملخصات حسب فترة زمنية
     */
    public function scopeForDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('summary_date', [$startDate, $endDate]);
    }

    /**
     * فلترة الملخصات حسب المنشئ
     */
    public function scopeForCreator($query, $creatorId)
    {
        return $query->where('created_by', $creatorId);
    }

    /**
     * ترتيب حسب التاريخ (الأحدث أولاً)
     */
    public function scopeLatest($query)
    {
        return $query->orderBy('summary_date', 'desc');
    }

    /**
     * الحصول على إجمالي المبيعات لفترة معينة
     */
    public static function getTotalSalesForPeriod($startDate, $endDate, $warehouseId = null, $creatorId = null)
    {
        $query = self::forDateRange($startDate, $endDate);
        
        if ($warehouseId) {
            $query->forWarehouse($warehouseId);
        }
        
        if ($creatorId) {
            $query->forCreator($creatorId);
        }

        return $query->sum('total_amount');
    }

    /**
     * الحصول على متوسط المبيعات اليومية لفترة معينة
     */
    public static function getAverageDailySales($startDate, $endDate, $warehouseId = null, $creatorId = null)
    {
        $query = self::forDateRange($startDate, $endDate);
        
        if ($warehouseId) {
            $query->forWarehouse($warehouseId);
        }
        
        if ($creatorId) {
            $query->forCreator($creatorId);
        }

        return $query->avg('total_amount') ?? 0;
    }
}
