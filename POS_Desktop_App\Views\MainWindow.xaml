<Window x:Class="POS_Desktop_App.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        mc:Ignorable="d"
        Title="نظام نقاط البيع" 
        Height="800" 
        Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        Background="{StaticResource BackgroundBrush}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}"
        FlowDirection="RightToLeft">

    <materialDesign:DialogHost Identifier="RootDialog">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Top Bar -->
            <materialDesign:ColorZone Grid.Row="0" 
                                    Mode="PrimaryDark" 
                                    Padding="16,8"
                                    materialDesign:ShadowAssist.ShadowDepth="Depth2">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Logo and Title -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="CashRegister" 
                                               Width="32" 
                                               Height="32" 
                                               VerticalAlignment="Center"
                                               Foreground="White"
                                               Margin="0,0,8,0"/>
                        <TextBlock Text="نظام نقاط البيع" 
                                 FontSize="20" 
                                 FontWeight="Medium"
                                 VerticalAlignment="Center"
                                 Foreground="White"/>
                    </StackPanel>

                    <!-- Connection Status -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal" Margin="16,0">
                        <materialDesign:PackIcon x:Name="ConnectionIcon"
                                               Kind="WifiStrength4" 
                                               Width="20" 
                                               Height="20" 
                                               VerticalAlignment="Center"
                                               Foreground="LightGreen"
                                               Margin="0,0,4,0"/>
                        <TextBlock x:Name="ConnectionStatus"
                                 Text="متصل" 
                                 VerticalAlignment="Center"
                                 Foreground="White"
                                 FontSize="12"/>
                    </StackPanel>

                    <!-- Current User -->
                    <StackPanel Grid.Column="3" Orientation="Horizontal" Margin="16,0">
                        <materialDesign:PackIcon Kind="Account" 
                                               Width="20" 
                                               Height="20" 
                                               VerticalAlignment="Center"
                                               Foreground="White"
                                               Margin="0,0,4,0"/>
                        <TextBlock x:Name="CurrentUser"
                                 Text="المستخدم الحالي" 
                                 VerticalAlignment="Center"
                                 Foreground="White"
                                 FontSize="12"/>
                    </StackPanel>

                    <!-- Settings Button -->
                    <Button Grid.Column="4" 
                          Style="{StaticResource MaterialDesignIconButton}"
                          Foreground="White"
                          Click="SettingsButton_Click"
                          ToolTip="الإعدادات">
                        <materialDesign:PackIcon Kind="Settings" Width="24" Height="24"/>
                    </Button>
                </Grid>
            </materialDesign:ColorZone>

            <!-- Main Content -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="250"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Navigation Menu -->
                <materialDesign:ColorZone Grid.Column="0" 
                                        Mode="Light"
                                        materialDesign:ShadowAssist.ShadowDepth="Depth1">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="0,16">
                            
                            <!-- POS Section -->
                            <TextBlock Text="نقاط البيع" 
                                     Margin="16,8"
                                     FontWeight="Medium"
                                     Foreground="{StaticResource TextSecondaryBrush}"/>
                            
                            <Button x:Name="PosButton"
                                  Style="{StaticResource MaterialDesignFlatButton}"
                                  HorizontalAlignment="Stretch"
                                  HorizontalContentAlignment="Right"
                                  Padding="16,12"
                                  Click="PosButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="CashRegister" 
                                                           Width="24" 
                                                           Height="24" 
                                                           Margin="0,0,12,0"/>
                                    <TextBlock Text="شاشة البيع" FontSize="14"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="InvoicesButton"
                                  Style="{StaticResource MaterialDesignFlatButton}"
                                  HorizontalAlignment="Stretch"
                                  HorizontalContentAlignment="Right"
                                  Padding="16,12"
                                  Click="InvoicesButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Receipt" 
                                                           Width="24" 
                                                           Height="24" 
                                                           Margin="0,0,12,0"/>
                                    <TextBlock Text="الفواتير" FontSize="14"/>
                                </StackPanel>
                            </Button>

                            <Separator Margin="8,16"/>

                            <!-- Data Management Section -->
                            <TextBlock Text="إدارة البيانات" 
                                     Margin="16,8"
                                     FontWeight="Medium"
                                     Foreground="{StaticResource TextSecondaryBrush}"/>

                            <Button x:Name="ProductsButton"
                                  Style="{StaticResource MaterialDesignFlatButton}"
                                  HorizontalAlignment="Stretch"
                                  HorizontalContentAlignment="Right"
                                  Padding="16,12"
                                  Click="ProductsButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Package" 
                                                           Width="24" 
                                                           Height="24" 
                                                           Margin="0,0,12,0"/>
                                    <TextBlock Text="المنتجات" FontSize="14"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="CustomersButton"
                                  Style="{StaticResource MaterialDesignFlatButton}"
                                  HorizontalAlignment="Stretch"
                                  HorizontalContentAlignment="Right"
                                  Padding="16,12"
                                  Click="CustomersButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="AccountGroup" 
                                                           Width="24" 
                                                           Height="24" 
                                                           Margin="0,0,12,0"/>
                                    <TextBlock Text="العملاء" FontSize="14"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="WarehousesButton"
                                  Style="{StaticResource MaterialDesignFlatButton}"
                                  HorizontalAlignment="Stretch"
                                  HorizontalContentAlignment="Right"
                                  Padding="16,12"
                                  Click="WarehousesButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Warehouse" 
                                                           Width="24" 
                                                           Height="24" 
                                                           Margin="0,0,12,0"/>
                                    <TextBlock Text="المستودعات" FontSize="14"/>
                                </StackPanel>
                            </Button>

                            <Separator Margin="8,16"/>

                            <!-- Reports Section -->
                            <TextBlock Text="التقارير" 
                                     Margin="16,8"
                                     FontWeight="Medium"
                                     Foreground="{StaticResource TextSecondaryBrush}"/>

                            <Button x:Name="SalesReportButton"
                                  Style="{StaticResource MaterialDesignFlatButton}"
                                  HorizontalAlignment="Stretch"
                                  HorizontalContentAlignment="Right"
                                  Padding="16,12"
                                  Click="SalesReportButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ChartLine" 
                                                           Width="24" 
                                                           Height="24" 
                                                           Margin="0,0,12,0"/>
                                    <TextBlock Text="تقرير المبيعات" FontSize="14"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="InventoryReportButton"
                                  Style="{StaticResource MaterialDesignFlatButton}"
                                  HorizontalAlignment="Stretch"
                                  HorizontalContentAlignment="Right"
                                  Padding="16,12"
                                  Click="InventoryReportButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ChartBar" 
                                                           Width="24" 
                                                           Height="24" 
                                                           Margin="0,0,12,0"/>
                                    <TextBlock Text="تقرير المخزون" FontSize="14"/>
                                </StackPanel>
                            </Button>

                            <Separator Margin="8,16"/>

                            <!-- System Section -->
                            <TextBlock Text="النظام" 
                                     Margin="16,8"
                                     FontWeight="Medium"
                                     Foreground="{StaticResource TextSecondaryBrush}"/>

                            <Button x:Name="SyncButton"
                                  Style="{StaticResource MaterialDesignFlatButton}"
                                  HorizontalAlignment="Stretch"
                                  HorizontalContentAlignment="Right"
                                  Padding="16,12"
                                  Click="SyncButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Sync" 
                                                           Width="24" 
                                                           Height="24" 
                                                           Margin="0,0,12,0"/>
                                    <TextBlock Text="مزامنة البيانات" FontSize="14"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="BackupButton"
                                  Style="{StaticResource MaterialDesignFlatButton}"
                                  HorizontalAlignment="Stretch"
                                  HorizontalContentAlignment="Right"
                                  Padding="16,12"
                                  Click="BackupButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Backup" 
                                                           Width="24" 
                                                           Height="24" 
                                                           Margin="0,0,12,0"/>
                                    <TextBlock Text="نسخ احتياطي" FontSize="14"/>
                                </StackPanel>
                            </Button>

                        </StackPanel>
                    </ScrollViewer>
                </materialDesign:ColorZone>

                <!-- Content Area -->
                <ContentControl x:Name="MainContent" 
                              Grid.Column="1" 
                              Margin="8"/>

            </Grid>

            <!-- Status Bar -->
            <materialDesign:ColorZone Grid.Row="2" 
                                    Mode="Light" 
                                    Padding="16,8"
                                    materialDesign:ShadowAssist.ShadowDepth="Depth1">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock x:Name="StatusText" 
                             Grid.Column="0"
                             Text="جاهز" 
                             VerticalAlignment="Center"
                             FontSize="12"/>

                    <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="16,0">
                        <TextBlock Text="آخر مزامنة: " 
                                 VerticalAlignment="Center"
                                 FontSize="12"
                                 Foreground="{StaticResource TextSecondaryBrush}"/>
                        <TextBlock x:Name="LastSyncTime"
                                 Text="لم يتم" 
                                 VerticalAlignment="Center"
                                 FontSize="12"/>
                    </StackPanel>

                    <TextBlock x:Name="CurrentTime"
                             Grid.Column="2"
                             Text="{Binding Source={x:Static sys:DateTime.Now}, StringFormat='yyyy/MM/dd HH:mm:ss'}" 
                             VerticalAlignment="Center"
                             FontSize="12"/>
                </Grid>
            </materialDesign:ColorZone>

        </Grid>
    </materialDesign:DialogHost>
</Window>
