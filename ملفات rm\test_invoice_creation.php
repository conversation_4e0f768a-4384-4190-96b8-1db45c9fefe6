<?php

// اختبار إنشاء فاتورة مبيعات مع المنتجات المكتوبة

require_once 'vendor/autoload.php';

use App\Models\Invoice;
use App\Models\InvoiceProduct;
use App\Models\Customer;

// بيانات اختبار
$testData = [
    'customer_id' => 1, // افتراض وجود عميل برقم 1
    'issue_date' => '2025-01-01',
    'due_date' => '2025-01-31',
    'category_id' => 1,
    'ref_number' => 'TEST-001',
    'items' => [
        [
            'product_name' => 'منتج اختبار 1',
            'quantity' => 2,
            'price' => 100.00,
            'discount' => 0,
            'tax' => '0.00',
            'description' => 'وصف المنتج الأول'
        ],
        [
            'product_name' => 'منتج اختبار 2',
            'quantity' => 1,
            'price' => 50.00,
            'discount' => 5,
            'tax' => '0.00',
            'description' => 'وصف المنتج الثاني'
        ]
    ]
];

echo "بدء اختبار إنشاء فاتورة المبيعات...\n";

try {
    // إنشاء الفاتورة
    $invoice = new Invoice();
    $invoice->invoice_id = 1001; // رقم اختبار
    $invoice->customer_id = $testData['customer_id'];
    $invoice->issue_date = $testData['issue_date'];
    $invoice->due_date = $testData['due_date'];
    $invoice->category_id = $testData['category_id'];
    $invoice->ref_number = $testData['ref_number'];
    $invoice->status = 0;
    $invoice->created_by = 1; // افتراض المستخدم رقم 1
    
    echo "حفظ بيانات الفاتورة الأساسية...\n";
    $invoice->save();
    echo "تم حفظ الفاتورة برقم: " . $invoice->id . "\n";
    
    // إضافة المنتجات
    foreach ($testData['items'] as $index => $productData) {
        echo "إضافة المنتج " . ($index + 1) . ": " . $productData['product_name'] . "\n";
        
        $invoiceProduct = new InvoiceProduct();
        $invoiceProduct->invoice_id = $invoice->id;
        $invoiceProduct->product_id = null; // لا نستخدم منتجات حقيقية
        $invoiceProduct->product_name = $productData['product_name'];
        $invoiceProduct->quantity = $productData['quantity'];
        $invoiceProduct->price = $productData['price'];
        $invoiceProduct->discount = $productData['discount'];
        $invoiceProduct->tax = $productData['tax'];
        $invoiceProduct->description = $productData['description'];
        
        $invoiceProduct->save();
        echo "تم حفظ المنتج برقم: " . $invoiceProduct->id . "\n";
    }
    
    echo "\n✅ تم إنشاء الفاتورة بنجاح!\n";
    echo "رقم الفاتورة: " . $invoice->id . "\n";
    echo "عدد المنتجات: " . count($testData['items']) . "\n";
    
    // عرض تفاصيل الفاتورة
    echo "\n--- تفاصيل الفاتورة ---\n";
    $savedInvoice = Invoice::with('items')->find($invoice->id);
    foreach ($savedInvoice->items as $item) {
        echo "- " . $item->product_name . " (الكمية: " . $item->quantity . ", السعر: " . $item->price . ")\n";
    }
    
} catch (Exception $e) {
    echo "\n❌ خطأ في إنشاء الفاتورة:\n";
    echo "الرسالة: " . $e->getMessage() . "\n";
    echo "الملف: " . $e->getFile() . "\n";
    echo "السطر: " . $e->getLine() . "\n";
}

echo "\nانتهى الاختبار.\n";
