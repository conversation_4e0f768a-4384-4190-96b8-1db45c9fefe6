using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace POS_Desktop_App.Models.DatabaseModels
{
    /// <summary>
    /// نموذج منتج الفاتورة - يطابق جدول pos_v2_products في قاعدة البيانات
    /// </summary>
    [Table("pos_v2_products")]
    public class PosProduct
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("pos_id")]
        public long PosId { get; set; }

        [Column("product_id")]
        public long ProductId { get; set; }

        [Column("quantity")]
        public int Quantity { get; set; }

        [Column("tax")]
        [StringLength(50)]
        public string Tax { get; set; } = "0.00";

        [Column("discount")]
        public decimal Discount { get; set; } = 0.00m;

        [Column("price")]
        [Precision(15, 2)]
        public decimal Price { get; set; } = 0.00m;

        [Column("total")]
        [Precision(15, 2)]
        public decimal Total { get; set; } = 0.00m;

        [Column("total_discount")]
        [Precision(15, 2)]
        public decimal TotalDiscount { get; set; } = 0.00m;

        [Column("description")]
        public string Description { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; }

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; }

        // Navigation Properties
        public virtual PosTransaction PosTransaction { get; set; }
        public virtual Product Product { get; set; }

        // Calculated Properties
        [NotMapped]
        public decimal TaxAmount
        {
            get
            {
                if (string.IsNullOrEmpty(Tax) || Tax == "0.00")
                    return 0;

                // Parse tax rates (could be multiple taxes separated by commas)
                var taxRates = Tax.Split(',');
                decimal totalTaxRate = 0;

                foreach (var rate in taxRates)
                {
                    if (decimal.TryParse(rate.Trim(), out decimal taxRate))
                    {
                        totalTaxRate += taxRate;
                    }
                }

                return (Price * Quantity * totalTaxRate) / 100;
            }
        }

        [NotMapped]
        public decimal SubTotal => Price * Quantity;

        [NotMapped]
        public decimal NetTotal => SubTotal + TaxAmount - Discount;

        [NotMapped]
        public decimal UnitPriceWithTax
        {
            get
            {
                var taxRate = GetTaxRate();
                return Price + (Price * taxRate / 100);
            }
        }

        [NotMapped]
        public string DisplayName => Product?.Name ?? $"منتج #{ProductId}";

        [NotMapped]
        public string ProductSku => Product?.Sku ?? "";

        [NotMapped]
        public string ProductImage => Product?.ProImage ?? "";

        // Helper Methods
        public decimal GetTaxRate()
        {
            if (string.IsNullOrEmpty(Tax) || Tax == "0.00")
                return 0;

            var taxRates = Tax.Split(',');
            decimal totalTaxRate = 0;

            foreach (var rate in taxRates)
            {
                if (decimal.TryParse(rate.Trim(), out decimal taxRate))
                {
                    totalTaxRate += taxRate;
                }
            }

            return totalTaxRate;
        }

        public void CalculateTotal()
        {
            var taxAmount = TaxAmount;
            Total = (Price * Quantity) + taxAmount - Discount;
            TotalDiscount = Discount;
        }

        public void SetTaxRate(decimal taxRate)
        {
            Tax = taxRate.ToString("0.00");
            CalculateTotal();
        }

        public void SetTaxRates(params decimal[] taxRates)
        {
            Tax = string.Join(",", taxRates.Select(r => r.ToString("0.00")));
            CalculateTotal();
        }

        public void ApplyDiscount(decimal discountAmount, bool isPercentage = false)
        {
            if (isPercentage)
            {
                Discount = (SubTotal * discountAmount) / 100;
            }
            else
            {
                Discount = discountAmount;
            }

            // Ensure discount doesn't exceed subtotal
            if (Discount > SubTotal)
                Discount = SubTotal;

            CalculateTotal();
        }

        public void ClearDiscount()
        {
            Discount = 0;
            CalculateTotal();
        }

        public void UpdateQuantity(int newQuantity)
        {
            if (newQuantity < 0)
                throw new ArgumentException("الكمية لا يمكن أن تكون أقل من صفر");

            Quantity = newQuantity;
            CalculateTotal();
        }

        public void UpdatePrice(decimal newPrice)
        {
            if (newPrice < 0)
                throw new ArgumentException("السعر لا يمكن أن يكون أقل من صفر");

            Price = newPrice;
            CalculateTotal();
        }

        // Validation
        public List<string> Validate()
        {
            var errors = new List<string>();

            if (ProductId <= 0)
                errors.Add("معرف المنتج غير صحيح");

            if (Quantity <= 0)
                errors.Add("الكمية يجب أن تكون أكبر من صفر");

            if (Price < 0)
                errors.Add("السعر لا يمكن أن يكون سالب");

            if (Discount < 0)
                errors.Add("الخصم لا يمكن أن يكون سالب");

            if (Discount > SubTotal)
                errors.Add("الخصم لا يمكن أن يكون أكبر من المجموع الفرعي");

            return errors;
        }

        public bool IsValid()
        {
            return !Validate().Any();
        }

        // Display Methods
        public string GetFormattedPrice()
        {
            return Price.ToString("C2");
        }

        public string GetFormattedTotal()
        {
            return Total.ToString("C2");
        }

        public string GetFormattedDiscount()
        {
            return Discount.ToString("C2");
        }

        public string GetTaxDisplayText()
        {
            var taxRate = GetTaxRate();
            if (taxRate == 0)
                return "بدون ضريبة";

            return $"ضريبة {taxRate:0.##}%";
        }

        // Copy Methods
        public PosProduct Clone()
        {
            return new PosProduct
            {
                ProductId = this.ProductId,
                Quantity = this.Quantity,
                Price = this.Price,
                Tax = this.Tax,
                Discount = this.Discount,
                Description = this.Description,
                Product = this.Product
            };
        }

        public void CopyFrom(PosProduct other)
        {
            if (other == null) return;

            this.ProductId = other.ProductId;
            this.Quantity = other.Quantity;
            this.Price = other.Price;
            this.Tax = other.Tax;
            this.Discount = other.Discount;
            this.Description = other.Description;
            
            CalculateTotal();
        }

        // Override Methods
        public override string ToString()
        {
            return $"{DisplayName} - الكمية: {Quantity} - السعر: {GetFormattedPrice()} - المجموع: {GetFormattedTotal()}";
        }

        public override bool Equals(object obj)
        {
            if (obj is PosProduct other)
            {
                return this.ProductId == other.ProductId && this.PosId == other.PosId;
            }
            return false;
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(ProductId, PosId);
        }
    }
}
