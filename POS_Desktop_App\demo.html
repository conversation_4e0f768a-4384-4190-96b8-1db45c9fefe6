<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض تجريبي - نظام نقاط البيع</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            direction: rtl;
        }

        .app-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        /* Header */
        .header {
            background: #673AB7;
            color: white;
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 20px;
            font-weight: bold;
        }

        .header-info {
            display: flex;
            gap: 20px;
            align-items: center;
            font-size: 14px;
        }

        .status {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .status.online {
            color: #4CAF50;
        }

        /* Main Content */
        .main-content {
            display: flex;
            flex: 1;
        }

        /* Sidebar */
        .sidebar {
            width: 250px;
            background: #fafafa;
            border-left: 1px solid #e0e0e0;
            padding: 20px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-title {
            padding: 0 20px 10px;
            font-size: 12px;
            color: #757575;
            font-weight: bold;
            text-transform: uppercase;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #333;
            text-decoration: none;
            transition: background 0.2s;
            cursor: pointer;
        }

        .nav-item:hover, .nav-item.active {
            background: #e3f2fd;
            color: #1976d2;
        }

        .nav-item .icon {
            margin-left: 12px;
            font-size: 18px;
        }

        /* Content Area */
        .content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        /* POS Interface */
        .pos-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            height: 100%;
        }

        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
        }

        .card-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #673AB7;
            color: white;
        }

        .btn-primary:hover {
            background: #5e35b1;
        }

        /* Search Bar */
        .search-bar {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            margin-bottom: 15px;
        }

        .search-bar:focus {
            outline: none;
            border-color: #673AB7;
        }

        /* Categories */
        .categories {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            overflow-x: auto;
        }

        .category-btn {
            padding: 8px 16px;
            border: 2px solid #673AB7;
            background: white;
            color: #673AB7;
            border-radius: 20px;
            cursor: pointer;
            white-space: nowrap;
            transition: all 0.2s;
        }

        .category-btn.active, .category-btn:hover {
            background: #673AB7;
            color: white;
        }

        /* Products Grid */
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            max-height: 400px;
            overflow-y: auto;
        }

        .product-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .product-image {
            width: 60px;
            height: 60px;
            background: #f0f0f0;
            border-radius: 6px;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .product-name {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .product-price {
            color: #673AB7;
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 5px;
        }

        .product-stock {
            color: #757575;
            font-size: 12px;
        }

        /* Cart */
        .cart {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .cart-header {
            margin-bottom: 20px;
        }

        .cart-items {
            flex: 1;
            margin-bottom: 20px;
        }

        .empty-cart {
            text-align: center;
            color: #757575;
            padding: 40px 0;
        }

        .cart-totals {
            border-top: 1px solid #e0e0e0;
            padding-top: 15px;
            margin-bottom: 20px;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .total-row.final {
            font-weight: bold;
            font-size: 18px;
            color: #673AB7;
            border-top: 1px solid #e0e0e0;
            padding-top: 8px;
        }

        .cart-actions {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .btn-disabled {
            background: #e0e0e0;
            color: #757575;
            cursor: not-allowed;
        }

        /* Footer */
        .footer {
            background: #fafafa;
            padding: 10px 20px;
            border-top: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #757575;
        }

        .footer-info {
            display: flex;
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <div class="header">
            <div class="logo">
                <span>🏪</span>
                <span>نظام نقاط البيع</span>
            </div>
            <div class="header-info">
                <div class="status online">
                    <span>📶</span>
                    <span>متصل</span>
                </div>
                <div class="status">
                    <span>👤</span>
                    <span>مستخدم تجريبي</span>
                </div>
                <div class="status">
                    <span>⚙️</span>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Sidebar -->
            <div class="sidebar">
                <div class="nav-section">
                    <div class="nav-title">نقاط البيع</div>
                    <div class="nav-item active">
                        <span class="icon">🛒</span>
                        <span>شاشة البيع</span>
                    </div>
                    <div class="nav-item">
                        <span class="icon">🧾</span>
                        <span>الفواتير</span>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-title">إدارة البيانات</div>
                    <div class="nav-item">
                        <span class="icon">📦</span>
                        <span>المنتجات</span>
                    </div>
                    <div class="nav-item">
                        <span class="icon">👥</span>
                        <span>العملاء</span>
                    </div>
                    <div class="nav-item">
                        <span class="icon">🏪</span>
                        <span>المستودعات</span>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-title">التقارير</div>
                    <div class="nav-item">
                        <span class="icon">📊</span>
                        <span>تقرير المبيعات</span>
                    </div>
                    <div class="nav-item">
                        <span class="icon">📈</span>
                        <span>تقرير المخزون</span>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-title">النظام</div>
                    <div class="nav-item">
                        <span class="icon">🔄</span>
                        <span>مزامنة البيانات</span>
                    </div>
                    <div class="nav-item">
                        <span class="icon">💾</span>
                        <span>نسخ احتياطي</span>
                    </div>
                </div>
            </div>

            <!-- Content Area -->
            <div class="content">
                <div class="pos-container">
                    <!-- Products Section -->
                    <div class="card">
                        <div class="card-header">
                            <div>
                                <div class="card-title">شاشة نقاط البيع</div>
                                <p style="color: #757575; margin-top: 5px;">اختر المنتجات وأضفها إلى السلة</p>
                            </div>
                            <button class="btn btn-primary">+ فاتورة جديدة</button>
                        </div>

                        <input type="text" class="search-bar" placeholder="البحث عن منتج (الاسم أو الباركود)">

                        <div class="categories">
                            <div class="category-btn active">الكل</div>
                            <div class="category-btn">مشروبات</div>
                            <div class="category-btn">وجبات خفيفة</div>
                            <div class="category-btn">حلويات</div>
                            <div class="category-btn">فواكه</div>
                        </div>

                        <div class="products-grid">
                            <div class="product-card">
                                <div class="product-image">🥤</div>
                                <div class="product-name">كوكا كولا</div>
                                <div class="product-price">5.00 ر.س</div>
                                <div class="product-stock">متوفر: 25</div>
                            </div>
                            <div class="product-card">
                                <div class="product-image">🍟</div>
                                <div class="product-name">شيبس ليز</div>
                                <div class="product-price">3.50 ر.س</div>
                                <div class="product-stock">متوفر: 15</div>
                            </div>
                            <div class="product-card">
                                <div class="product-image">🍫</div>
                                <div class="product-name">شوكولاتة</div>
                                <div class="product-price">2.75 ر.س</div>
                                <div class="product-stock">متوفر: 30</div>
                            </div>
                            <div class="product-card">
                                <div class="product-image">🍕</div>
                                <div class="product-name">بيتزا مارجريتا</div>
                                <div class="product-price">15.00 ر.س</div>
                                <div class="product-stock">متوفر: 8</div>
                            </div>
                            <div class="product-card">
                                <div class="product-image">🧃</div>
                                <div class="product-name">عصير برتقال</div>
                                <div class="product-price">4.00 ر.س</div>
                                <div class="product-stock">متوفر: 20</div>
                            </div>
                            <div class="product-card">
                                <div class="product-image">🍪</div>
                                <div class="product-name">بسكوت أوريو</div>
                                <div class="product-price">6.00 ر.س</div>
                                <div class="product-stock">متوفر: 12</div>
                            </div>
                            <div class="product-card">
                                <div class="product-image">🥛</div>
                                <div class="product-name">حليب نادك</div>
                                <div class="product-price">8.50 ر.س</div>
                                <div class="product-stock">متوفر: 18</div>
                            </div>
                            <div class="product-card">
                                <div class="product-image">🍎</div>
                                <div class="product-name">تفاح أحمر</div>
                                <div class="product-price">12.00 ر.س</div>
                                <div class="product-stock">متوفر: 25</div>
                            </div>
                        </div>
                    </div>

                    <!-- Cart Section -->
                    <div class="card cart">
                        <div class="cart-header">
                            <div class="card-title">سلة التسوق</div>
                            <p style="color: #757575; font-size: 14px;">0 منتج</p>
                        </div>

                        <div class="cart-items">
                            <div class="empty-cart">
                                <div style="font-size: 48px; margin-bottom: 10px;">🛒</div>
                                <div>السلة فارغة</div>
                                <div style="font-size: 12px; margin-top: 5px;">اختر المنتجات لإضافتها</div>
                            </div>
                        </div>

                        <div class="cart-totals">
                            <div class="total-row">
                                <span>المجموع الفرعي:</span>
                                <span>0.00 ر.س</span>
                            </div>
                            <div class="total-row">
                                <span>الضريبة (15%):</span>
                                <span>0.00 ر.س</span>
                            </div>
                            <div class="total-row">
                                <span>الخصم:</span>
                                <span>0.00 ر.س</span>
                            </div>
                            <div class="total-row final">
                                <span>الإجمالي:</span>
                                <span>0.00 ر.س</span>
                            </div>
                        </div>

                        <div class="cart-actions">
                            <button class="btn btn-primary btn-disabled">
                                💳 الدفع
                            </button>
                            <button class="btn btn-disabled" style="border: 2px solid #e0e0e0; background: white; color: #757575;">
                                🗑️ مسح الكل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div>الحالة: جاهز</div>
            <div class="footer-info">
                <div>آخر مزامنة: 14:30:25</div>
                <div id="current-time">2024/01/15 14:30:25</div>
            </div>
        </div>
    </div>

    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('current-time').textContent = timeString;
        }

        // Update time every second
        setInterval(updateTime, 1000);
        updateTime();

        // Add click handlers for products
        document.querySelectorAll('.product-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'translateY(-2px)';
                }, 100);
            });
        });

        // Add click handlers for navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.nav-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>
