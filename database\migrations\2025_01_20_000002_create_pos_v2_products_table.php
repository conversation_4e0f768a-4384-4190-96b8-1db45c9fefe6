<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pos_v2_products', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('pos_id')->default('0');
            $table->unsignedBigInteger('product_id')->default('0');
            $table->integer('quantity')->default('0');
            $table->string('tax')->default('0.00');
            $table->float('discount')->default('0.00')->nullable();
            $table->decimal('price', 15, 2)->default('0.00');
            $table->decimal('total', 15, 2)->default('0.00');
            $table->decimal('total_discount', 15, 2)->default('0.00');
            $table->text('description')->nullable();
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('pos_id')->references('id')->on('pos_v2')->onDelete('cascade');
            $table->foreign('product_id')->references('id')->on('product_services')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pos_v2_products');
    }
};
