using Microsoft.EntityFrameworkCore;
using POS_Desktop_App.Models.DatabaseModels;
using System;
using System.Configuration;

namespace POS_Desktop_App.Services.DatabaseService
{
    /// <summary>
    /// سياق قاعدة البيانات الرئيسية - الاتصال بقاعدة بيانات MySQL على السيرفر
    /// </summary>
    public class PosDbContext : DbContext
    {
        // DbSets for all entities
        public DbSet<PosTransaction> PosTransactions { get; set; }
        public DbSet<PosProduct> PosProducts { get; set; }
        public DbSet<PosPayment> PosPayments { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<Customer> Customers { get; set; }
        public DbSet<Warehouse> Warehouses { get; set; }
        public DbSet<WarehouseProduct> WarehouseProducts { get; set; }
        public DbSet<User> Users { get; set; }
        public DbSet<Shift> Shifts { get; set; }
        public DbSet<ProductCategory> ProductCategories { get; set; }
        public DbSet<ProductUnit> ProductUnits { get; set; }
        public DbSet<FinancialRecord> FinancialRecords { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                // Get connection string from app.config
                var connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString;
                
                if (string.IsNullOrEmpty(connectionString))
                {
                    // Fallback connection string - should be configured in app.config
                    connectionString = "Server=localhost;Database=pos_database;Uid=root;Pwd=;";
                }

                optionsBuilder.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString));
                
                // Enable sensitive data logging in development
                #if DEBUG
                optionsBuilder.EnableSensitiveDataLogging();
                optionsBuilder.LogTo(Console.WriteLine);
                #endif
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure PosTransaction relationships
            modelBuilder.Entity<PosTransaction>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                // Relationships
                entity.HasOne(e => e.Customer)
                      .WithMany(c => c.PosTransactions)
                      .HasForeignKey(e => e.CustomerId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Warehouse)
                      .WithMany()
                      .HasForeignKey(e => e.WarehouseId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.User)
                      .WithMany()
                      .HasForeignKey(e => e.UserId)
                      .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.Shift)
                      .WithMany()
                      .HasForeignKey(e => e.ShiftId)
                      .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.CreatedByUser)
                      .WithMany()
                      .HasForeignKey(e => e.CreatedBy)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Payment)
                      .WithOne(p => p.PosTransaction)
                      .HasForeignKey<PosPayment>(p => p.PosId);

                entity.HasMany(e => e.Products)
                      .WithOne(p => p.PosTransaction)
                      .HasForeignKey(p => p.PosId)
                      .OnDelete(DeleteBehavior.Cascade);

                // Indexes
                entity.HasIndex(e => e.PosId);
                entity.HasIndex(e => e.CustomerId);
                entity.HasIndex(e => e.WarehouseId);
                entity.HasIndex(e => e.PosDate);
                entity.HasIndex(e => e.CreatedBy);
            });

            // Configure PosProduct relationships
            modelBuilder.Entity<PosProduct>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.HasOne(e => e.Product)
                      .WithMany(p => p.PosProducts)
                      .HasForeignKey(e => e.ProductId)
                      .OnDelete(DeleteBehavior.Cascade);

                // Indexes
                entity.HasIndex(e => e.PosId);
                entity.HasIndex(e => e.ProductId);
            });

            // Configure PosPayment relationships
            modelBuilder.Entity<PosPayment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                // Indexes
                entity.HasIndex(e => e.PosId);
                entity.HasIndex(e => e.Date);
            });

            // Configure Product relationships
            modelBuilder.Entity<Product>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.HasOne(e => e.Category)
                      .WithMany()
                      .HasForeignKey(e => e.CategoryId)
                      .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.Unit)
                      .WithMany()
                      .HasForeignKey(e => e.UnitId)
                      .OnDelete(DeleteBehavior.SetNull);

                entity.HasMany(e => e.WarehouseProducts)
                      .WithOne(wp => wp.Product)
                      .HasForeignKey(wp => wp.ProductId)
                      .OnDelete(DeleteBehavior.Cascade);

                // Indexes
                entity.HasIndex(e => e.Sku).IsUnique();
                entity.HasIndex(e => e.Name);
                entity.HasIndex(e => e.CategoryId);
                entity.HasIndex(e => e.CreatedBy);
            });

            // Configure Customer relationships
            modelBuilder.Entity<Customer>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.HasOne(e => e.Warehouse)
                      .WithMany()
                      .HasForeignKey(e => e.WarehouseId)
                      .OnDelete(DeleteBehavior.SetNull);

                // Indexes
                entity.HasIndex(e => e.CustomerId);
                entity.HasIndex(e => e.Email);
                entity.HasIndex(e => e.Contact);
                entity.HasIndex(e => e.CreatedBy);
            });

            // Configure Warehouse relationships
            modelBuilder.Entity<Warehouse>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.HasMany(e => e.WarehouseProducts)
                      .WithOne(wp => wp.Warehouse)
                      .HasForeignKey(wp => wp.WarehouseId)
                      .OnDelete(DeleteBehavior.Cascade);

                // Indexes
                entity.HasIndex(e => e.Name);
                entity.HasIndex(e => e.CreatedBy);
            });

            // Configure WarehouseProduct relationships
            modelBuilder.Entity<WarehouseProduct>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                // Composite unique index
                entity.HasIndex(e => new { e.WarehouseId, e.ProductId }).IsUnique();
            });

            // Configure User entity
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.HasOne(e => e.Warehouse)
                      .WithMany()
                      .HasForeignKey(e => e.WarehouseId)
                      .OnDelete(DeleteBehavior.SetNull);

                // Indexes
                entity.HasIndex(e => e.Email).IsUnique();
                entity.HasIndex(e => e.CreatedBy);
            });

            // Configure Shift relationships
            modelBuilder.Entity<Shift>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.HasOne(e => e.Warehouse)
                      .WithMany()
                      .HasForeignKey(e => e.WarehouseId)
                      .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.CreatedByUser)
                      .WithMany()
                      .HasForeignKey(e => e.CreatedBy)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.UpdatedByUser)
                      .WithMany()
                      .HasForeignKey(e => e.UpdatedBy)
                      .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.ClosedByUser)
                      .WithMany()
                      .HasForeignKey(e => e.ClosedBy)
                      .OnDelete(DeleteBehavior.SetNull);

                // Indexes
                entity.HasIndex(e => e.OpenedAt);
                entity.HasIndex(e => e.ClosedAt);
                entity.HasIndex(e => e.IsClosed);
                entity.HasIndex(e => e.WarehouseId);
            });

            // Configure ProductCategory
            modelBuilder.Entity<ProductCategory>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.HasIndex(e => e.Name);
                entity.HasIndex(e => e.CreatedBy);
            });

            // Configure ProductUnit
            modelBuilder.Entity<ProductUnit>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.HasIndex(e => e.Name);
                entity.HasIndex(e => e.CreatedBy);
            });

            // Configure FinancialRecord
            modelBuilder.Entity<FinancialRecord>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.HasOne(e => e.Shift)
                      .WithMany()
                      .HasForeignKey(e => e.ShiftId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => e.ShiftId);
            });
        }

        // Override SaveChanges to automatically set timestamps
        public override int SaveChanges()
        {
            UpdateTimestamps();
            return base.SaveChanges();
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            UpdateTimestamps();
            return await base.SaveChangesAsync(cancellationToken);
        }

        private void UpdateTimestamps()
        {
            var entries = ChangeTracker.Entries()
                .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified);

            foreach (var entry in entries)
            {
                if (entry.Entity is ITimestampEntity timestampEntity)
                {
                    if (entry.State == EntityState.Added)
                    {
                        timestampEntity.CreatedAt = DateTime.Now;
                    }
                    timestampEntity.UpdatedAt = DateTime.Now;
                }
            }
        }

        // Connection test method
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                await Database.OpenConnectionAsync();
                await Database.CloseConnectionAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        // Database initialization
        public async Task InitializeDatabaseAsync()
        {
            try
            {
                await Database.EnsureCreatedAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تهيئة قاعدة البيانات: {ex.Message}", ex);
            }
        }
    }

    // Interface for entities with timestamps
    public interface ITimestampEntity
    {
        DateTime CreatedAt { get; set; }
        DateTime UpdatedAt { get; set; }
    }
}
