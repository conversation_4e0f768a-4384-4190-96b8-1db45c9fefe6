<?php
// اختبار الخادم والإعدادات
echo "<h1>🚀 اختبار الخادم - up20251</h1>";
echo "<hr>";

// معلومات الخادم
echo "<h2>📊 معلومات الخادم:</h2>";
echo "<ul>";
echo "<li><strong>عنوان URL:</strong> http://localhost/up20251</li>";
echo "<li><strong>مجلد المشروع:</strong> " . __DIR__ . "</li>";
echo "<li><strong>إصدار PHP:</strong> " . PHP_VERSION . "</li>";
echo "<li><strong>الوقت الحالي:</strong> " . date('Y-m-d H:i:s') . "</li>";
echo "</ul>";

// اختبار قاعدة البيانات
echo "<h2>🗄️ اختبار قاعدة البيانات:</h2>";
try {
    $host = '127.0.0.1';
    $port = '3306';
    $database = 'ty';
    $username = 'root';
    $password = '';
    
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات ناجح!</p>";
    echo "<ul>";
    echo "<li><strong>قاعدة البيانات:</strong> $database</li>";
    echo "<li><strong>الخادم:</strong> $host:$port</li>";
    
    // عدد الجداول
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<li><strong>عدد الجداول:</strong> " . count($tables) . "</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
}

// اختبار ملفات Laravel
echo "<h2>🔧 اختبار ملفات Laravel:</h2>";
echo "<ul>";

$files_to_check = [
    '../.env' => 'ملف البيئة',
    '../config/database.php' => 'إعدادات قاعدة البيانات',
    '../vendor/autoload.php' => 'Composer Autoloader',
    '../bootstrap/app.php' => 'Bootstrap Laravel',
    '../artisan' => 'Artisan CLI'
];

foreach ($files_to_check as $file => $description) {
    if (file_exists($file)) {
        echo "<li style='color: green;'>✅ $description موجود</li>";
    } else {
        echo "<li style='color: red;'>❌ $description غير موجود</li>";
    }
}
echo "</ul>";

// اختبار الامتدادات المطلوبة
echo "<h2>🔌 اختبار امتدادات PHP:</h2>";
echo "<ul>";

$required_extensions = [
    'pdo_mysql' => 'MySQL PDO',
    'mbstring' => 'Multibyte String',
    'openssl' => 'OpenSSL',
    'tokenizer' => 'Tokenizer',
    'xml' => 'XML',
    'ctype' => 'Character Type',
    'json' => 'JSON',
    'bcmath' => 'BC Math'
];

foreach ($required_extensions as $ext => $name) {
    if (extension_loaded($ext)) {
        echo "<li style='color: green;'>✅ $name</li>";
    } else {
        echo "<li style='color: orange;'>⚠️ $name (غير مطلوب بالضرورة)</li>";
    }
}
echo "</ul>";

echo "<hr>";
echo "<p><strong>📝 ملاحظة:</strong> إذا كانت جميع الاختبارات ناجحة، فإن الخادم يعمل بشكل صحيح!</p>";
echo "<p><strong>🌐 رابط التطبيق:</strong> <a href='http://localhost/up20251'>http://localhost/up20251</a></p>";
?>
