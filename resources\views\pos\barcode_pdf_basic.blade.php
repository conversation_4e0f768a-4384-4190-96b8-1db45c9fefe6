<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>لصاقات الأسعار</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 10px;
            direction: rtl;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #000;
            padding-bottom: 10px;
        }
        
        .product-label {
            border: 2px solid #000;
            padding: 10px;
            width: 240px;
            height: 90px;
            text-align: center;
            background: white;
            margin: 5px;
            display: inline-block;
            vertical-align: top;
            position: relative;
        }
        
        .logo-placeholder {
            position: absolute;
            top: 5px;
            right: 5px;
            width: 25px;
            height: 25px;
            border: 1px solid #999;
            font-size: 6px;
            text-align: center;
            line-height: 25px;
            background: #f0f0f0;
        }
        
        .product-name {
            font-weight: bold;
            font-size: 10px;
            margin: 5px 0;
            color: #000;
        }
        
        .barcode-area {
            margin: 5px 0;
            height: 35px;
            border: 1px solid #ccc;
            background: #f9f9f9;
            display: table;
            width: 100%;
        }
        
        .barcode-content {
            display: table-cell;
            vertical-align: middle;
            text-align: center;
        }
        
        .product-price {
            font-size: 12px;
            font-weight: bold;
            color: #d32f2f;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h3>{{ $companyData['company_name'] ?? 'اسم الشركة' }}</h3>
        <p>لصاقات الأسعار والباركود - {{ date('Y-m-d') }}</p>
    </div>

    @if($productServices && $productServices->count() > 0)
        @foreach($productServices as $product)
            <div class="product-label">
                <!-- مكان الشعار -->
                <div class="logo-placeholder">LOGO</div>
                
                <!-- اسم المنتج -->
                <div class="product-name">{{ $product->name ?? 'منتج غير محدد' }}</div>
                
                <!-- منطقة الباركود -->
                <div class="barcode-area">
                    <div class="barcode-content">
                        @if(!empty($product->barcode_html))
                            {!! $product->barcode_html !!}
                        @else
                            <div style="font-family: monospace; font-size: 8px; border: 1px solid #000; padding: 2px;">
                                {{ $product->sku ?? 'N/A' }}
                            </div>
                        @endif
                    </div>
                </div>
                
                <!-- السعر -->
                <div class="product-price">
                    @php
                        $price = $product->sale_price ?? 0;
                        $formattedPrice = number_format($price, 2) . ' ر.س';
                    @endphp
                    {{ $formattedPrice }}
                </div>
            </div>
        @endforeach
    @else
        <div style="text-align: center; padding: 50px;">
            <h3>لا توجد منتجات متاحة</h3>
        </div>
    @endif

    <div style="margin-top: 30px; text-align: center; border-top: 1px solid #ccc; padding-top: 10px; clear: both;">
        <p>عدد المنتجات: {{ $productServices ? $productServices->count() : 0 }} منتج</p>
    </div>
</body>
</html>
