using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace POS_Desktop_App.Models.DatabaseModels
{
    [Table("pos_v2_payments")]
    public class PosPayment
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("pos_id")]
        public long PosId { get; set; }

        [Column("date")]
        public DateTime Date { get; set; }

        [Column("amount")]
        [Precision(15, 2)]
        public decimal Amount { get; set; } = 0.00m;

        [Column("discount")]
        [Precision(15, 2)]
        public decimal Discount { get; set; } = 0.00m;

        [Column("discount_amount")]
        [Precision(15, 2)]
        public decimal DiscountAmount { get; set; } = 0.00m;

        [Column("created_by")]
        public long CreatedBy { get; set; }

        [Column("payment_type")]
        [StringLength(50)]
        public string PaymentType { get; set; } = "cash";

        [Column("cash_amount")]
        [Precision(15, 2)]
        public decimal CashAmount { get; set; } = 0.00m;

        [Column("network_amount")]
        [Precision(15, 2)]
        public decimal NetworkAmount { get; set; } = 0.00m;

        [Column("transaction_number")]
        [StringLength(255)]
        public string TransactionNumber { get; set; } = "";

        [Column("created_at")]
        public DateTime CreatedAt { get; set; }

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; }

        // Navigation Properties
        public virtual PosTransaction PosTransaction { get; set; }
    }

    [Table("product_service_categories")]
    public class ProductCategory
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Column("name")]
        [Required]
        [StringLength(255)]
        public string Name { get; set; }

        [Column("color")]
        [StringLength(50)]
        public string Color { get; set; } = "#000000";

        [Column("created_by")]
        public int CreatedBy { get; set; } = 0;

        [Column("created_at")]
        public DateTime CreatedAt { get; set; }

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; }

        public override string ToString()
        {
            return Name;
        }
    }

    [Table("product_service_units")]
    public class ProductUnit
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Column("name")]
        [Required]
        [StringLength(255)]
        public string Name { get; set; }

        [Column("created_by")]
        public int CreatedBy { get; set; } = 0;

        [Column("created_at")]
        public DateTime CreatedAt { get; set; }

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; }

        public override string ToString()
        {
            return Name;
        }
    }

    [Table("shifts")]
    public class Shift
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("warehouse_id")]
        public long? WarehouseId { get; set; }

        [Column("opened_at")]
        public DateTime OpenedAt { get; set; }

        [Column("closed_at")]
        public DateTime? ClosedAt { get; set; }

        [Column("opening_balance")]
        [Precision(15, 2)]
        public decimal OpeningBalance { get; set; } = 0.00m;

        [Column("closing_balance")]
        [Precision(15, 2)]
        public decimal ClosingBalance { get; set; } = 0.00m;

        [Column("total_sales")]
        [Precision(15, 2)]
        public decimal TotalSales { get; set; } = 0.00m;

        [Column("total_cash")]
        [Precision(15, 2)]
        public decimal TotalCash { get; set; } = 0.00m;

        [Column("total_network")]
        [Precision(15, 2)]
        public decimal TotalNetwork { get; set; } = 0.00m;

        [Column("is_closed")]
        public bool IsClosed { get; set; } = false;

        [Column("created_by")]
        public long CreatedBy { get; set; }

        [Column("updated_by")]
        public long? UpdatedBy { get; set; }

        [Column("closed_by")]
        public long? ClosedBy { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; }

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; }

        // Navigation Properties
        public virtual Warehouse Warehouse { get; set; }
        public virtual User CreatedByUser { get; set; }
        public virtual User UpdatedByUser { get; set; }
        public virtual User ClosedByUser { get; set; }

        // Calculated Properties
        [NotMapped]
        public TimeSpan Duration
        {
            get
            {
                var endTime = ClosedAt ?? DateTime.Now;
                return endTime - OpenedAt;
            }
        }

        [NotMapped]
        public string DurationText
        {
            get
            {
                var duration = Duration;
                return $"{duration.Hours:D2}:{duration.Minutes:D2}:{duration.Seconds:D2}";
            }
        }

        [NotMapped]
        public decimal ExpectedClosingBalance => OpeningBalance + TotalCash;

        [NotMapped]
        public decimal BalanceDifference => ClosingBalance - ExpectedClosingBalance;
    }

    [Table("financial_records")]
    public class FinancialRecord
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("shift_id")]
        public long ShiftId { get; set; }

        [Column("record_type")]
        [StringLength(50)]
        public string RecordType { get; set; }

        [Column("amount")]
        [Precision(15, 2)]
        public decimal Amount { get; set; }

        [Column("description")]
        public string Description { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; }

        // Navigation Properties
        public virtual Shift Shift { get; set; }
    }
}
